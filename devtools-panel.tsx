import React, { useState, useEffect } from "react";

interface DevToolsRequest {
  id: string;
  url: string;
  method: string;
  timestamp: number;
  status?: number;
  statusText?: string;
  requestHeaders?: Array<{name: string, value: string}>;
  responseHeaders?: Array<{name: string, value: string}>;
  requestBody?: string;
  responseBody?: string;
  duration?: number;
  type: string;
}

function DevToolsPanel() {
  const [requests, setRequests] = useState<DevToolsRequest[]>([]);
  const [selectedRequest, setSelectedRequest] = useState<DevToolsRequest | null>(null);
  const [responseBodyCache, setResponseBodyCache] = useState<Map<string, string>>(new Map());
  const [currentDomain, setCurrentDomain] = useState<string>('');
  const [preserveLog, setPreserveLog] = useState<boolean>(false);
  const [captureEnabled, setCaptureEnabled] = useState<boolean>(true);

  // Load stored requests on mount
  useEffect(() => {
    loadSettings();
    loadStoredRequests();
    setupMessageListener();
    updateCurrentDomain();

    // Set up periodic domain check
    const domainCheckInterval = setInterval(() => {
      checkDomainChange();
    }, 5000);

    return () => clearInterval(domainCheckInterval);
  }, []);

  // Load settings from storage
  const loadSettings = async () => {
    try {
      const result = await chrome.storage.local.get(['preserveLog', 'captureEnabled']);
      setPreserveLog(result.preserveLog || false);
      setCaptureEnabled(result.captureEnabled !== false); // Default to true
      console.log(`📋 Settings loaded: preserveLog=${result.preserveLog}, captureEnabled=${result.captureEnabled}`);
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  };

  // Save settings to storage
  const saveSettings = async (newPreserveLog?: boolean, newCaptureEnabled?: boolean) => {
    const settingsToSave = {
      preserveLog: newPreserveLog !== undefined ? newPreserveLog : preserveLog,
      captureEnabled: newCaptureEnabled !== undefined ? newCaptureEnabled : captureEnabled
    };

    try {
      await chrome.storage.local.set(settingsToSave);
      console.log(`💾 Settings saved:`, settingsToSave);
    } catch (error) {
      console.error('Error saving settings:', error);
    }
  };

  // Update current domain
  const updateCurrentDomain = () => {
    if (chrome.devtools && chrome.devtools.inspectedWindow) {
      chrome.devtools.inspectedWindow.eval('window.location.hostname', (hostname: string) => {
        if (hostname && hostname !== currentDomain) {
          const previousDomain = currentDomain;
          setCurrentDomain(hostname);
          console.log(`🌐 Domain changed from "${previousDomain}" to "${hostname}"`);

          if (previousDomain && !preserveLog) {
            console.log("🗑️ Clearing requests due to domain change (preserve log disabled)");
            setRequests([]);
            setSelectedRequest(null);
            setResponseBodyCache(new Map());
          }
        }
      });
    }
  };

  // Check for domain changes
  const checkDomainChange = () => {
    updateCurrentDomain();
  };

  const loadStoredRequests = async () => {
    try {
      const response = await chrome.runtime.sendMessage({ type: 'GET_DEVTOOLS_REQUESTS' });
      if (response && response.requests) {
        console.log('Loaded DevTools requests:', response.requests.length);
        // Only update if we have more requests or different data
        setRequests(prevRequests => {
          if (response.requests.length >= prevRequests.length ||
              JSON.stringify(response.requests) !== JSON.stringify(prevRequests)) {
            return response.requests;
          }
          return prevRequests;
        });
      } else {
        // Fallback to general request method
        const fallbackResponse = await chrome.runtime.sendMessage({ type: 'GET_REQUESTS' });
        if (fallbackResponse && fallbackResponse.requests) {
          const devtoolsRequests = fallbackResponse.requests.filter((req: DevToolsRequest) =>
            req.type === 'devtools' || req.type === 'fetch' || req.type === 'xmlhttprequest'
          );
          console.log('Loaded DevTools requests (fallback):', devtoolsRequests.length);
          // Only update if we have more requests or different data
          setRequests(prevRequests => {
            if (devtoolsRequests.length >= prevRequests.length ||
                JSON.stringify(devtoolsRequests) !== JSON.stringify(prevRequests)) {
              return devtoolsRequests;
            }
            return prevRequests;
          });
        } else {
          console.log("⚠️ No data from storage, keeping existing requests");
        }
      }
    } catch (error) {
      console.error('Error loading requests:', error);
    }
  };

  const setupMessageListener = () => {
    const handleMessage = (message: any, sender: any, sendResponse: any) => {
      console.log('DevTools panel received message:', message.type, message);

      if (message.type === 'DEVTOOLS_REQUEST') {
        addRequest(message.data);
        sendResponse({ success: true });
      } else if (message.type === 'DEVTOOLS_REQUEST_COMPLETE') {
        addRequest(message.data);
        sendResponse({ success: true });
      } else if (message.type === 'DEVTOOLS_RESPONSE_BODY') {
        updateResponseBody(message.requestId, message.responseBody);
        sendResponse({ success: true });
      } else if (message.type === 'NEW_REQUEST' && message.data.type === 'devtools') {
        addRequest(message.data);
        sendResponse({ success: true });
      } else {
        sendResponse({ success: false, message: 'Message type not handled' });
      }

      return true; // Keep message channel open
    };

    chrome.runtime.onMessage.addListener(handleMessage);
    return () => chrome.runtime.onMessage.removeListener(handleMessage);
  };

  const addRequest = (requestData: DevToolsRequest) => {
    // Don't add requests if capture is disabled
    if (!captureEnabled) {
      console.log("⏸️ Capture disabled, ignoring request:", requestData.url);
      return;
    }

    setRequests(prevRequests => {
      // Check if request already exists (avoid duplicates)
      const existingIndex = prevRequests.findIndex(req =>
        req.id === requestData.id ||
        (req.url === requestData.url &&
         req.method === requestData.method &&
         Math.abs(req.timestamp - requestData.timestamp) < 1000) // Within 1 second
      );

      let newRequests;
      if (existingIndex !== -1) {
        // Update existing request with new data
        newRequests = [...prevRequests];
        newRequests[existingIndex] = { ...newRequests[existingIndex], ...requestData };
        console.log("🔄 Updated existing request:", requestData.url);
      } else {
        // Add new request to beginning
        newRequests = [requestData, ...prevRequests];
        console.log("✅ Added new request:", requestData.url);
      }

      return newRequests.slice(0, 1000); // Keep only latest 1000 requests
    });
  };

  const updateResponseBody = (requestId: string, responseBody: string) => {
    setResponseBodyCache(prev => new Map(prev.set(requestId, responseBody)));
    
    // If this request is currently selected, update it
    setSelectedRequest(prevSelected => {
      if (prevSelected && `${prevSelected.url}_${prevSelected.timestamp}` === requestId) {
        return { ...prevSelected, responseBody };
      }
      return prevSelected;
    });
  };

  const selectRequest = (request: DevToolsRequest) => {
    // Check if we have cached response body
    const cacheKey = `${request.url}_${request.timestamp}`;
    const cachedResponse = responseBodyCache.get(cacheKey);
    if (cachedResponse) {
      request.responseBody = cachedResponse;
    }
    
    setSelectedRequest(request);
  };

  const clearRequests = () => {
    setRequests([]);
    setSelectedRequest(null);
    setResponseBodyCache(new Map());
    chrome.runtime.sendMessage({ type: 'CLEAR_DEVTOOLS_REQUESTS' }, (response) => {
      if (chrome.runtime.lastError) {
        console.error('Error clearing DevTools requests:', chrome.runtime.lastError.message);
      }
    });
  };

  const togglePreserveLog = () => {
    const newPreserveLog = !preserveLog;
    setPreserveLog(newPreserveLog);
    saveSettings(newPreserveLog, undefined);
    console.log(`🔄 Preserve log ${newPreserveLog ? 'enabled' : 'disabled'}`);
  };

  const toggleCapture = () => {
    const newCaptureEnabled = !captureEnabled;
    setCaptureEnabled(newCaptureEnabled);
    saveSettings(undefined, newCaptureEnabled);

    // Notify background script about capture status change
    chrome.runtime.sendMessage({
      type: 'SET_CAPTURE_STATUS',
      isCapturing: newCaptureEnabled
    }, (response) => {
      if (chrome.runtime.lastError) {
        console.error('Error setting capture status:', chrome.runtime.lastError.message);
      }
    });

    console.log(`🔄 Capture ${newCaptureEnabled ? 'enabled' : 'disabled'}`);
  };

  const exportRequests = () => {
    const dataStr = JSON.stringify(requests, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `devtools-requests-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  // Get content type from headers
  const getContentTypeFromHeaders = (headers: Array<{name: string, value: string}> = []) => {
    const contentTypeHeader = headers.find(header =>
      header.name.toLowerCase() === 'content-type'
    );
    return contentTypeHeader ? contentTypeHeader.value : '';
  };

  // Escape HTML for safe display
  const escapeHtml = (text: string) => {
    if (!text) return '';

    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  };

  // Format HTML content
  const formatHtml = (html: string) => {
    if (!html) return '';

    try {
      // Basic HTML formatting with proper indentation
      let formatted = html
        // Add line breaks after closing tags
        .replace(/></g, '>\n<')
        // Add line breaks after opening tags that contain content
        .replace(/(<[^>]+>)([^<\n]+)/g, '$1\n$2')
        // Clean up multiple newlines
        .replace(/\n\s*\n/g, '\n')
        // Add basic indentation
        .split('\n')
        .map((line, index, array) => {
          const trimmed = line.trim();
          if (!trimmed) return '';

          // Calculate indentation level
          let indent = 0;
          for (let i = 0; i < index; i++) {
            const prevLine = array[i].trim();
            if (prevLine.match(/^<[^\/][^>]*[^\/]>$/)) {
              indent++;
            } else if (prevLine.match(/^<\/[^>]+>$/)) {
              indent--;
            }
          }

          // Adjust for closing tags
          if (trimmed.match(/^<\/[^>]+>$/)) {
            indent--;
          }

          const spaces = '  '.repeat(Math.max(0, indent));
          return spaces + escapeHtml(trimmed);
        })
        .filter(line => line.trim())
        .join('\n');

      return formatted;
    } catch (error) {
      // Fallback to simple escaping if formatting fails
      return escapeHtml(html);
    }
  };

  // Format XML content
  const formatXml = (xml: string) => {
    if (!xml) return '';

    // Basic XML formatting
    let formatted = xml
      .replace(/></g, '>\n<')
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;');

    return formatted;
  };

  // Format response body based on content type
  const formatResponseBody = (text: string, contentType = '') => {
    if (!text) return 'Empty';

    // Handle JSON content
    if (contentType.includes('application/json') || contentType.includes('text/json')) {
      try {
        const parsed = JSON.parse(text);
        return JSON.stringify(parsed, null, 2);
      } catch {
        return text;
      }
    }

    // Handle HTML content
    if (contentType.includes('text/html') || text.trim().startsWith('<!DOCTYPE') || text.trim().startsWith('<html')) {
      return formatHtml(text);
    }

    // Handle XML content
    if (contentType.includes('application/xml') || contentType.includes('text/xml')) {
      return formatXml(text);
    }

    // Handle other JSON-like content (try to parse)
    if (text.trim().startsWith('{') || text.trim().startsWith('[')) {
      try {
        const parsed = JSON.parse(text);
        return JSON.stringify(parsed, null, 2);
      } catch {
        return text;
      }
    }

    return text;
  };

  // Legacy function for backward compatibility
  const formatJson = (text: string) => {
    return formatResponseBody(text, 'application/json');
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', height: '100vh', fontFamily: 'system-ui, sans-serif' }}>
      {/* Header */}
      <div style={{ 
        background: 'white', 
        borderBottom: '1px solid #e0e0e0', 
        padding: '12px 16px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between'
      }}>
        <div style={{ fontSize: '16px', fontWeight: '600', color: '#333' }}>
          API Interceptor - DevTools Panel
        </div>
        <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
          <button
            onClick={toggleCapture}
            style={{
              padding: '6px 12px',
              border: `1px solid ${captureEnabled ? '#4caf50' : '#ddd'}`,
              borderRadius: '4px',
              background: captureEnabled ? '#4caf50' : 'white',
              color: captureEnabled ? 'white' : 'black',
              cursor: 'pointer',
              fontSize: '12px'
            }}
            title={captureEnabled ? 'Stop capturing requests' : 'Start capturing requests'}
          >
            {captureEnabled ? '⏸️ Stop' : '▶️ Start'}
          </button>
          <button
            onClick={togglePreserveLog}
            style={{
              padding: '6px 12px',
              border: `1px solid ${preserveLog ? '#4caf50' : '#ddd'}`,
              borderRadius: '4px',
              background: preserveLog ? '#4caf50' : 'white',
              color: preserveLog ? 'white' : 'black',
              cursor: 'pointer',
              fontSize: '12px'
            }}
            title={preserveLog ? 'Preserve log across domain changes (enabled)' : 'Clear log on domain change (disabled)'}
          >
            📌 Preserve Log
          </button>
          <button
            onClick={clearRequests}
            style={{
              padding: '6px 12px',
              border: '1px solid #ddd',
              borderRadius: '4px',
              background: 'white',
              cursor: 'pointer',
              fontSize: '12px'
            }}
          >
            🗑️ Clear
          </button>
          <button
            onClick={exportRequests}
            style={{
              padding: '6px 12px',
              border: '1px solid #1976d2',
              borderRadius: '4px',
              background: '#1976d2',
              color: 'white',
              cursor: 'pointer',
              fontSize: '12px'
            }}
          >
            📥 Export
          </button>
          <span style={{ fontSize: '11px', color: '#888', marginLeft: '10px' }}>
            Domain: {currentDomain || 'Unknown'}
          </span>
        </div>
      </div>

      {/* Content */}
      <div style={{ flex: 1, display: 'flex', overflow: 'hidden' }}>
        {/* Request List */}
        <div style={{ 
          width: '50%', 
          borderRight: '1px solid #e0e0e0', 
          background: 'white',
          overflowY: 'auto'
        }}>
          {requests.length === 0 ? (
            <div style={{ 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'center', 
              height: '100%',
              color: '#666',
              fontSize: '14px',
              textAlign: 'center'
            }}>
              No requests captured yet.<br />
              Navigate to any page to see network requests.
            </div>
          ) : (
            requests.map(request => (
              <div
                key={request.id}
                onClick={() => selectRequest(request)}
                style={{
                  padding: '12px 16px',
                  borderBottom: '1px solid #f0f0f0',
                  cursor: 'pointer',
                  backgroundColor: selectedRequest?.id === request.id ? '#e3f2fd' : 'transparent',
                  transition: 'background-color 0.2s'
                }}
                onMouseEnter={(e) => {
                  if (selectedRequest?.id !== request.id) {
                    e.currentTarget.style.backgroundColor = '#f8f9fa';
                  }
                }}
                onMouseLeave={(e) => {
                  if (selectedRequest?.id !== request.id) {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }
                }}
              >
                <div style={{ marginBottom: '4px' }}>
                  <span style={{
                    display: 'inline-block',
                    padding: '2px 6px',
                    borderRadius: '3px',
                    fontSize: '11px',
                    fontWeight: '600',
                    marginRight: '8px',
                    backgroundColor: request.method === 'GET' ? '#e8f5e8' :
                                   request.method === 'POST' ? '#e3f2fd' :
                                   request.method === 'PUT' ? '#fff3e0' :
                                   request.method === 'DELETE' ? '#ffebee' : '#f0f0f0',
                    color: request.method === 'GET' ? '#2e7d32' :
                           request.method === 'POST' ? '#1976d2' :
                           request.method === 'PUT' ? '#f57c00' :
                           request.method === 'DELETE' ? '#d32f2f' : '#666'
                  }}>
                    {request.method}
                  </span>
                  <span style={{ fontSize: '13px', color: '#333', wordBreak: 'break-all' }}>
                    {request.url}
                  </span>
                </div>
                <div style={{ fontSize: '12px', color: '#666' }}>
                  Status: {request.status || 'Pending'} | 
                  {request.duration ? ` ${Math.round(request.duration)}ms` : ' N/A'} |
                  {` ${new Date(request.timestamp).toLocaleTimeString()}`}
                </div>
              </div>
            ))
          )}
        </div>

        {/* Request Details */}
        <div style={{ 
          width: '50%', 
          background: 'white',
          overflowY: 'auto'
        }}>
          {selectedRequest ? (
            <div style={{ padding: '16px' }}>
              <div style={{ marginBottom: '24px' }}>
                <div style={{ fontSize: '14px', fontWeight: '600', color: '#333', marginBottom: '8px' }}>
                  Request URL
                </div>
                <div style={{ 
                  background: '#f8f9fa', 
                  padding: '12px', 
                  borderRadius: '4px',
                  fontFamily: 'Monaco, Menlo, monospace',
                  fontSize: '12px',
                  wordBreak: 'break-all'
                }}>
                  {selectedRequest.url}
                </div>
              </div>

              <div style={{ marginBottom: '24px' }}>
                <div style={{ fontSize: '14px', fontWeight: '600', color: '#333', marginBottom: '8px' }}>
                  Request Method
                </div>
                <div style={{ 
                  background: '#f8f9fa', 
                  padding: '12px', 
                  borderRadius: '4px',
                  fontFamily: 'Monaco, Menlo, monospace',
                  fontSize: '12px'
                }}>
                  {selectedRequest.method}
                </div>
              </div>

              <div style={{ marginBottom: '24px' }}>
                <div style={{ fontSize: '14px', fontWeight: '600', color: '#333', marginBottom: '8px' }}>
                  Status Code
                </div>
                <div style={{ 
                  background: '#f8f9fa', 
                  padding: '12px', 
                  borderRadius: '4px',
                  fontFamily: 'Monaco, Menlo, monospace',
                  fontSize: '12px'
                }}>
                  {selectedRequest.status || 'Pending'} {selectedRequest.statusText || ''}
                </div>
              </div>

              {selectedRequest.requestBody && (
                <div style={{ marginBottom: '24px' }}>
                  <div style={{ fontSize: '14px', fontWeight: '600', color: '#333', marginBottom: '8px' }}>
                    Request Body
                  </div>
                  <div style={{
                    background: '#f8f9fa',
                    padding: '12px',
                    borderRadius: '4px',
                    fontFamily: 'Monaco, Menlo, monospace',
                    fontSize: '12px',
                    whiteSpace: 'pre-wrap',
                    wordBreak: 'break-all',
                    maxHeight: '200px',
                    overflowY: 'auto'
                  }}>
                    {formatResponseBody(selectedRequest.requestBody, getContentTypeFromHeaders(selectedRequest.requestHeaders))}
                  </div>
                </div>
              )}

              <div style={{ marginBottom: '24px' }}>
                <div style={{ fontSize: '14px', fontWeight: '600', color: '#333', marginBottom: '8px' }}>
                  Response Body {getContentTypeFromHeaders(selectedRequest.responseHeaders) && `(${getContentTypeFromHeaders(selectedRequest.responseHeaders)})`}
                </div>
                <div style={{
                  background: '#f8f9fa',
                  padding: '12px',
                  borderRadius: '4px',
                  fontFamily: 'Monaco, Menlo, monospace',
                  fontSize: '12px',
                  whiteSpace: 'pre-wrap',
                  wordBreak: 'break-all',
                  maxHeight: '300px',
                  overflowY: 'auto'
                }}>
                  {selectedRequest.responseBody ?
                    formatResponseBody(selectedRequest.responseBody, getContentTypeFromHeaders(selectedRequest.responseHeaders) || selectedRequest.mimeType) :
                    'Loading response body...'
                  }
                </div>
              </div>
            </div>
          ) : (
            <div style={{ 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'center', 
              height: '100%',
              color: '#666',
              fontSize: '14px'
            }}>
              Select a request to view details
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Make initPanel available globally for devtools
(window as any).initPanel = () => {
  console.log('DevTools panel initialized');
};

export default DevToolsPanel;
