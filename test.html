<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Interceptor Test Page</title>
    <style>
        body {
            font-family: system-ui, -apple-system, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .button:hover {
            background: #1d4ed8;
        }
        .button.secondary {
            background: #6b7280;
        }
        .button.secondary:hover {
            background: #4b5563;
        }
        .section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        .log {
            background: #f3f4f6;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <h1>API Interceptor Test Page</h1>
    <p>Use this page to test the API interceptor extension. Click the buttons below to make various API requests.</p>
    
    <div class="section">
        <h2>JSON API Tests</h2>
        <button class="button" onclick="testJSONAPI()">GET JSON Data</button>
        <button class="button" onclick="testPOSTAPI()">POST JSON Data</button>
        <button class="button" onclick="testPUTAPI()">PUT JSON Data</button>
        <button class="button" onclick="testDELETEAPI()">DELETE Request</button>
        <div id="jsonLog" class="log"></div>
    </div>
    
    <div class="section">
        <h2>Different Response Types</h2>
        <button class="button secondary" onclick="testTextAPI()">GET Text Response</button>
        <button class="button secondary" onclick="testXMLAPI()">GET XML Response</button>
        <button class="button secondary" onclick="testImageAPI()">GET Image</button>
        <button class="button secondary" onclick="testErrorAPI()">Trigger 404 Error</button>
        <div id="responseLog" class="log"></div>
    </div>
    
    <div class="section">
        <h2>XMLHttpRequest Tests</h2>
        <button class="button" onclick="testXHRGet()">XHR GET Request</button>
        <button class="button" onclick="testXHRPost()">XHR POST Request</button>
        <div id="xhrLog" class="log"></div>
    </div>
    
    <div class="section">
        <h2>Multiple Requests</h2>
        <button class="button" onclick="testMultipleRequests()">Send 5 Requests</button>
        <button class="button secondary" onclick="clearLogs()">Clear Logs</button>
        <div id="multiLog" class="log"></div>
    </div>

    <script>
        function log(elementId, message) {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            element.innerHTML += `[${timestamp}] ${message}\n`;
            element.scrollTop = element.scrollHeight;
        }

        function clearLogs() {
            document.querySelectorAll('.log').forEach(log => log.innerHTML = '');
        }

        // JSON API Tests
        async function testJSONAPI() {
            try {
                log('jsonLog', 'Making GET request to JSONPlaceholder...');
                const response = await fetch('https://jsonplaceholder.typicode.com/posts/1');
                const data = await response.json();
                log('jsonLog', `✅ GET Success: ${response.status} - ${JSON.stringify(data).substring(0, 100)}...`);
            } catch (error) {
                log('jsonLog', `❌ GET Error: ${error.message}`);
            }
        }

        async function testPOSTAPI() {
            try {
                log('jsonLog', 'Making POST request to JSONPlaceholder...');
                const response = await fetch('https://jsonplaceholder.typicode.com/posts', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        title: 'Test Post',
                        body: 'This is a test post from API Interceptor',
                        userId: 1
                    })
                });
                const data = await response.json();
                log('jsonLog', `✅ POST Success: ${response.status} - Created post with ID ${data.id}`);
            } catch (error) {
                log('jsonLog', `❌ POST Error: ${error.message}`);
            }
        }

        async function testPUTAPI() {
            try {
                log('jsonLog', 'Making PUT request to JSONPlaceholder...');
                const response = await fetch('https://jsonplaceholder.typicode.com/posts/1', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        id: 1,
                        title: 'Updated Post',
                        body: 'This post has been updated',
                        userId: 1
                    })
                });
                const data = await response.json();
                log('jsonLog', `✅ PUT Success: ${response.status} - Updated post: ${data.title}`);
            } catch (error) {
                log('jsonLog', `❌ PUT Error: ${error.message}`);
            }
        }

        async function testDELETEAPI() {
            try {
                log('jsonLog', 'Making DELETE request to JSONPlaceholder...');
                const response = await fetch('https://jsonplaceholder.typicode.com/posts/1', {
                    method: 'DELETE'
                });
                log('jsonLog', `✅ DELETE Success: ${response.status} - Post deleted`);
            } catch (error) {
                log('jsonLog', `❌ DELETE Error: ${error.message}`);
            }
        }

        // Different Response Types
        async function testTextAPI() {
            try {
                log('responseLog', 'Making request for text response...');
                const response = await fetch('https://httpbin.org/robots.txt');
                const text = await response.text();
                log('responseLog', `✅ Text Success: ${response.status} - ${text.substring(0, 50)}...`);
            } catch (error) {
                log('responseLog', `❌ Text Error: ${error.message}`);
            }
        }

        async function testXMLAPI() {
            try {
                log('responseLog', 'Making request for XML response...');
                const response = await fetch('https://httpbin.org/xml');
                const text = await response.text();
                log('responseLog', `✅ XML Success: ${response.status} - ${text.substring(0, 50)}...`);
            } catch (error) {
                log('responseLog', `❌ XML Error: ${error.message}`);
            }
        }

        async function testImageAPI() {
            try {
                log('responseLog', 'Making request for image...');
                const response = await fetch('https://httpbin.org/image/png');
                log('responseLog', `✅ Image Success: ${response.status} - Content-Type: ${response.headers.get('content-type')}`);
            } catch (error) {
                log('responseLog', `❌ Image Error: ${error.message}`);
            }
        }

        async function testErrorAPI() {
            try {
                log('responseLog', 'Making request that will return 404...');
                const response = await fetch('https://httpbin.org/status/404');
                log('responseLog', `⚠️ 404 Response: ${response.status} - ${response.statusText}`);
            } catch (error) {
                log('responseLog', `❌ Error: ${error.message}`);
            }
        }

        // XMLHttpRequest Tests
        function testXHRGet() {
            log('xhrLog', 'Making XHR GET request...');
            const xhr = new XMLHttpRequest();
            xhr.open('GET', 'https://jsonplaceholder.typicode.com/users/1');
            xhr.onload = function() {
                if (xhr.status === 200) {
                    const data = JSON.parse(xhr.responseText);
                    log('xhrLog', `✅ XHR GET Success: ${xhr.status} - User: ${data.name}`);
                } else {
                    log('xhrLog', `❌ XHR GET Error: ${xhr.status}`);
                }
            };
            xhr.onerror = function() {
                log('xhrLog', `❌ XHR GET Network Error`);
            };
            xhr.send();
        }

        function testXHRPost() {
            log('xhrLog', 'Making XHR POST request...');
            const xhr = new XMLHttpRequest();
            xhr.open('POST', 'https://jsonplaceholder.typicode.com/posts');
            xhr.setRequestHeader('Content-Type', 'application/json');
            xhr.onload = function() {
                if (xhr.status === 201) {
                    const data = JSON.parse(xhr.responseText);
                    log('xhrLog', `✅ XHR POST Success: ${xhr.status} - Created post with ID ${data.id}`);
                } else {
                    log('xhrLog', `❌ XHR POST Error: ${xhr.status}`);
                }
            };
            xhr.onerror = function() {
                log('xhrLog', `❌ XHR POST Network Error`);
            };
            xhr.send(JSON.stringify({
                title: 'XHR Test Post',
                body: 'This is a test post via XMLHttpRequest',
                userId: 1
            }));
        }

        // Multiple Requests
        async function testMultipleRequests() {
            log('multiLog', 'Sending 5 concurrent requests...');
            const promises = [];
            
            for (let i = 1; i <= 5; i++) {
                promises.push(
                    fetch(`https://jsonplaceholder.typicode.com/posts/${i}`)
                        .then(response => response.json())
                        .then(data => {
                            log('multiLog', `✅ Request ${i} completed: ${data.title.substring(0, 30)}...`);
                            return data;
                        })
                        .catch(error => {
                            log('multiLog', `❌ Request ${i} failed: ${error.message}`);
                        })
                );
            }
            
            try {
                await Promise.all(promises);
                log('multiLog', '🎉 All 5 requests completed!');
            } catch (error) {
                log('multiLog', `❌ Some requests failed: ${error.message}`);
            }
        }

        // Auto-run a test request on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                log('jsonLog', 'Page loaded - running initial test...');
                testJSONAPI();
            }, 1000);
        });
    </script>
</body>
</html>
