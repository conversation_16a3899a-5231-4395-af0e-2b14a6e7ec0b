# API Interceptor Extension - 使用说明

## 功能概述

API Interceptor Extension 是一个功能强大的 Chrome 浏览器插件，用于捕获、分析和管理网页中的所有 API 请求。它提供了美观的用户界面和丰富的功能，帮助开发者和测试人员更好地理解和调试网络请求。

## 主要功能

### 🔍 请求捕获
- **自动捕获**：自动拦截并记录页面中的所有网络请求
- **详细信息**：记录请求方法、URL、状态码、响应时间等完整信息
- **请求头和响应头**：完整记录 HTTP 头信息
- **请求体**：捕获 POST、PUT 等请求的请求体内容

### 📊 数据展示
- **实时统计**：显示总请求数、成功率、错误数量和平均响应时间
- **状态分类**：按 HTTP 状态码分类显示请求
- **时间信息**：显示请求时间戳和持续时间
- **域名分组**：按域名组织和显示请求

### 🔎 搜索和过滤
- **全文搜索**：支持按 URL、方法、状态码搜索
- **方法过滤**：按 GET、POST、PUT、DELETE 等方法过滤
- **状态过滤**：按 2xx、3xx、4xx、5xx 状态码过滤
- **类型过滤**：按请求类型（XHR、Fetch、Document 等）过滤

### 📋 请求详情
- **完整信息**：查看请求的所有详细信息
- **分标签显示**：Headers、Request、Response、Timing 分标签展示
- **代码高亮**：JSON 格式的请求体和响应体自动格式化和高亮
- **一键复制**：支持复制请求信息到剪贴板

### 💾 数据管理
- **本地存储**：自动保存捕获的请求数据
- **导出功能**：将请求数据导出为 JSON 文件
- **导入功能**：从 JSON 文件导入请求数据
- **存储统计**：显示存储使用情况和配额
- **数据清理**：支持清空所有请求数据

### ⚙️ 设置选项
- **自动捕获开关**：控制是否自动捕获请求
- **存储限制**：设置最大存储请求数量
- **域名过滤**：设置只捕获特定域名的请求
- **响应体捕获**：控制是否尝试捕获响应体

## 安装和使用

### 安装步骤
1. 下载或克隆项目代码
2. 运行 `pnpm install` 安装依赖
3. 运行 `pnpm dev` 启动开发服务器
4. 打开 Chrome 浏览器，进入 `chrome://extensions/`
5. 开启"开发者模式"
6. 点击"加载已解压的扩展程序"
7. 选择项目的 `build/chrome-mv3-dev` 目录

### 基本使用
1. **开始捕获**：安装插件后，它会自动开始捕获当前标签页的网络请求
2. **查看请求**：点击浏览器工具栏中的插件图标打开面板
3. **筛选请求**：使用搜索框和过滤器找到感兴趣的请求
4. **查看详情**：点击任意请求查看详细信息
5. **导出数据**：在设置页面可以导出请求数据

### 界面说明

#### 主界面
- **顶部统计**：显示请求总数、成功率、错误数和平均响应时间
- **搜索栏**：支持全文搜索请求
- **过滤器**：按方法、状态、类型过滤请求
- **请求列表**：显示所有捕获的请求，包含方法、状态、URL、时间等信息

#### 请求详情页
- **Headers 标签**：显示请求头和响应头
- **Request 标签**：显示请求体内容
- **Response 标签**：显示响应体内容（如果可用）
- **Timing 标签**：显示请求时间线信息

#### 设置页面
- **General 标签**：基本设置选项
- **Filters 标签**：域名过滤设置
- **Data 标签**：数据管理功能

## 技术特性

### 架构设计
- **Plasmo 框架**：基于现代的浏览器插件开发框架
- **React + TypeScript**：使用 React 和 TypeScript 构建用户界面
- **Tailwind CSS**：使用 Tailwind CSS 实现美观的界面设计
- **Chrome Extension API**：充分利用 Chrome 扩展 API

### 性能优化
- **内存管理**：限制存储的请求数量，防止内存溢出
- **异步处理**：所有数据操作都是异步的，不阻塞界面
- **虚拟滚动**：大量数据时使用虚拟滚动提升性能
- **懒加载**：按需加载请求详情，减少初始加载时间

### 安全考虑
- **权限最小化**：只请求必要的浏览器权限
- **本地存储**：所有数据都存储在本地，不上传到服务器
- **数据隔离**：每个域名的数据相互隔离
- **安全过滤**：过滤敏感信息，如认证令牌

## 开发和贡献

### 开发环境
```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 构建生产版本
pnpm build

# 打包扩展
pnpm package
```

### 项目结构
```
├── background.ts          # 后台脚本，负责请求拦截
├── popup.tsx             # 主界面组件
├── components/           # UI 组件
│   ├── RequestList.tsx   # 请求列表组件
│   ├── RequestDetails.tsx # 请求详情组件
│   ├── SearchFilter.tsx  # 搜索过滤组件
│   ├── RequestStats.tsx  # 统计组件
│   ├── Settings.tsx      # 设置组件
│   └── DataManager.tsx   # 数据管理组件
├── types/               # TypeScript 类型定义
├── utils/               # 工具函数
└── style.css           # 样式文件
```

## 常见问题

### Q: 为什么看不到响应体内容？
A: 由于浏览器安全限制，扩展程序无法直接访问响应体内容。这是 Chrome 的安全机制。

### Q: 请求数据会上传到服务器吗？
A: 不会。所有数据都存储在本地浏览器中，不会上传到任何服务器。

### Q: 如何清空所有数据？
A: 在设置页面的"Data"标签中，点击"Clear All Requests"按钮。

### Q: 支持哪些浏览器？
A: 目前只支持 Chrome 浏览器和基于 Chromium 的浏览器。

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基本的请求捕获和展示
- 实现搜索和过滤功能
- 添加请求详情页面
- 支持数据导出和导入
- 提供设置页面

## 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。
