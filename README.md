# 🌐 API Interceptor Extension

A powerful Chrome extension that provides a DevTools panel similar to the Network tab, specifically designed to capture and display all API requests with their complete response bodies from the current page.

This is a [Plasmo extension](https://docs.plasmo.com/) project bootstrapped with [`plasmo init`](https://www.npmjs.com/package/plasmo).

## ✨ Features

- **DevTools Panel Integration**: Adds a dedicated "🌐 API Interceptor" tab in Chrome DevTools
- **Complete Request Capture**: Captures all network requests including fetch, XMLHttpRequest, and other HTTP requests
- **Response Body Extraction**: Automatically extracts and displays response bodies for all requests
- **Real-time Monitoring**: Shows requests in real-time as they happen on the page
- **Beautiful UI**: Clean, intuitive interface similar to Chrome's Network tab
- **Request Details**: View complete request/response headers, body, status, timing, and more
- **Export Functionality**: Export captured requests as JSON for analysis
- **Multiple Request Types**: Supports GET, POST, PUT, DELETE, PATCH, and other HTTP methods

## 🚀 Installation & Getting Started

1. **Install dependencies:**
   ```bash
   npm install
   # or
   pnpm install
   ```

2. **Development mode:**
   ```bash
   npm run dev
   # or
   pnpm dev
   ```

3. **Build for production:**
   ```bash
   npm run build
   # or
   pnpm build
   ```

4. **Load the extension in Chrome:**
   - Open Chrome and go to `chrome://extensions/`
   - Enable "Developer mode" in the top right
   - Click "Load unpacked" and select the `build/chrome-mv3-dev` folder

## 📖 Usage

1. **Open DevTools**: Right-click on any webpage and select "Inspect" or press F12
2. **Find the API Interceptor Tab**: Look for the "🌐 API Interceptor" tab in DevTools
3. **Start Monitoring**: The panel will automatically start capturing requests from the current page
4. **View Requests**: Click on any request in the left panel to view detailed information
5. **Export Data**: Use the "Export" button to save captured requests as JSON

## 🧪 Testing

A test page is included (`test-page.html`) that demonstrates various types of API requests:

1. Open the test page in your browser
2. Open DevTools and go to the "API Interceptor" tab
3. Click the test buttons to generate different types of requests
4. Observe how they appear in the DevTools panel with complete response bodies

## DevTools Panel

This extension includes a custom DevTools panel that displays API requests with detailed information including request/response headers and bodies. The DevTools panel files are located in the `static/` directory and are automatically copied to the build directories during the build process.

### CSP Compliance

The DevTools panel has been designed to comply with Chrome Extension Content Security Policy (CSP) requirements:

- **No inline event handlers**: All event handling is done through proper `addEventListener` calls
- **No inline scripts**: All JavaScript is in external files
- **Secure by default**: No `'unsafe-inline'` CSP directive required

### Development

When developing the DevTools panel:

1. Edit the source files in `static/devtools-panel.html` and `static/devtools-panel.js`
2. Run `pnpm dev` or `pnpm build` to copy the files to the build directories
3. The files will be automatically copied to both dev and production build directories

## Troubleshooting

### "Extension context invalidated" Error

This error is common during development and occurs when the extension is reloaded while content scripts are still running on web pages. The extension has been improved to handle this gracefully:

**What causes this error:**
- Reloading the extension during development
- Extension updates
- Browser restart while pages with content scripts are open

**How it's handled:**
- Content scripts check for valid extension context before sending messages
- Background script has improved error handling for context invalidation
- Messages are silently ignored when context is invalid (this is expected behavior)

**If you see this error:**
1. It's normal during development - the extension will continue working
2. Refresh any open web pages to reload content scripts with the new extension context
3. The error should not appear in production builds

**Prevention:**
- Close tabs with the extension's content scripts before reloading the extension
- Use `pnpm dev` for development which handles reloads more gracefully

## Making production build

Run the following:

```bash
pnpm build
# or
npm run build
```

This should create a production bundle for your extension, ready to be zipped and published to the stores.

## Submit to the webstores

The easiest way to deploy your Plasmo extension is to use the built-in [bpp](https://bpp.browser.market) GitHub action. Prior to using this action however, make sure to build your extension and upload the first version to the store to establish the basic credentials. Then, simply follow [this setup instruction](https://docs.plasmo.com/framework/workflows/submit) and you should be on your way for automated submission!
