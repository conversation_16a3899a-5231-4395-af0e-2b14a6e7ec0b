This is a [Plasmo extension](https://docs.plasmo.com/) project bootstrapped with [`plasmo init`](https://www.npmjs.com/package/plasmo).

## Getting Started

First, run the development server:

```bash
pnpm dev
# or
npm run dev
```

Open your browser and load the appropriate development build. For example, if you are developing for the chrome browser, using manifest v3, use: `build/chrome-mv3-dev`.

You can start editing the popup by modifying `popup.tsx`. It should auto-update as you make changes. To add an options page, simply add a `options.tsx` file to the root of the project, with a react component default exported. Likewise to add a content page, add a `content.ts` file to the root of the project, importing some module and do some logic, then reload the extension on your browser.

For further guidance, [visit our Documentation](https://docs.plasmo.com/)

## DevTools Panel

This extension includes a custom DevTools panel that displays API requests with detailed information including request/response headers and bodies. The DevTools panel files are located in the `static/` directory and are automatically copied to the build directories during the build process.

### CSP Compliance

The DevTools panel has been designed to comply with Chrome Extension Content Security Policy (CSP) requirements:

- **No inline event handlers**: All event handling is done through proper `addEventListener` calls
- **No inline scripts**: All JavaScript is in external files
- **Secure by default**: No `'unsafe-inline'` CSP directive required

### Development

When developing the DevTools panel:

1. Edit the source files in `static/devtools-panel.html` and `static/devtools-panel.js`
2. Run `pnpm dev` or `pnpm build` to copy the files to the build directories
3. The files will be automatically copied to both dev and production build directories

## Troubleshooting

### "Extension context invalidated" Error

This error is common during development and occurs when the extension is reloaded while content scripts are still running on web pages. The extension has been improved to handle this gracefully:

**What causes this error:**
- Reloading the extension during development
- Extension updates
- Browser restart while pages with content scripts are open

**How it's handled:**
- Content scripts check for valid extension context before sending messages
- Background script has improved error handling for context invalidation
- Messages are silently ignored when context is invalid (this is expected behavior)

**If you see this error:**
1. It's normal during development - the extension will continue working
2. Refresh any open web pages to reload content scripts with the new extension context
3. The error should not appear in production builds

**Prevention:**
- Close tabs with the extension's content scripts before reloading the extension
- Use `pnpm dev` for development which handles reloads more gracefully

## Making production build

Run the following:

```bash
pnpm build
# or
npm run build
```

This should create a production bundle for your extension, ready to be zipped and published to the stores.

## Submit to the webstores

The easiest way to deploy your Plasmo extension is to use the built-in [bpp](https://bpp.browser.market) GitHub action. Prior to using this action however, make sure to build your extension and upload the first version to the store to establish the basic credentials. Then, simply follow [this setup instruction](https://docs.plasmo.com/framework/workflows/submit) and you should be on your way for automated submission!
