# 🌐 API Interceptor DevTools Panel 使用指南

## 📋 概述

API Interceptor Chrome插件现在具有一个功能强大的DevTools面板，类似于Chrome的Network标签页，专门用于捕获和显示当前页面的所有网络请求及其完整的响应体。

## 🚀 主要功能

### ✅ 已实现的功能

1. **DevTools面板集成**
   - 在Chrome DevTools中添加了专门的"🌐 API Interceptor"标签页
   - 与Chrome DevTools无缝集成

2. **全面的请求捕获**
   - 捕获所有类型的网络请求（fetch、XMLHttpRequest、其他HTTP请求）
   - 使用多种方法确保请求不会遗漏：
     - Chrome DevTools Network API
     - webRequest API拦截
     - Content Script拦截

3. **完整的响应体提取**
   - 自动提取并显示所有请求的响应体
   - 支持JSON、文本、XML等多种内容类型
   - 自动格式化JSON响应以便阅读

4. **实时监控**
   - 实时显示页面上发生的请求
   - 自动更新请求列表

5. **详细的请求信息**
   - 请求URL和方法
   - 请求/响应头
   - 请求/响应体
   - 状态码和响应时间
   - MIME类型和文件大小

6. **用户友好的界面**
   - 清晰直观的界面设计
   - 左右分栏布局（请求列表 + 详细信息）
   - 颜色编码的HTTP方法标签

7. **导出功能**
   - 将捕获的请求导出为JSON文件
   - 便于后续分析和调试

## 📖 使用方法

### 1. 安装插件

1. 构建插件：
   ```bash
   npm run build
   ```

2. 在Chrome中加载插件：
   - 打开 `chrome://extensions/`
   - 启用"开发者模式"
   - 点击"加载已解压的扩展程序"
   - 选择 `build/chrome-mv3-dev` 文件夹

### 2. 使用DevTools面板

1. **打开DevTools**：
   - 右键点击网页，选择"检查"
   - 或按F12键

2. **找到API Interceptor标签页**：
   - 在DevTools顶部找到"🌐 API Interceptor"标签页
   - 点击进入面板

3. **开始监控**：
   - 面板会自动开始捕获当前页面的请求
   - 刷新页面或进行任何网络操作都会被捕获

4. **查看请求详情**：
   - 左侧显示请求列表
   - 点击任意请求查看详细信息
   - 右侧显示完整的请求/响应信息

5. **使用控制按钮**：
   - **🔄 Refresh**：手动刷新请求列表
   - **🗑️ Clear**：清空所有捕获的请求
   - **📥 Export**：导出请求数据为JSON文件

## 🧪 测试功能

项目包含了一个测试页面 `test-page.html`，可以用来验证功能：

1. 在浏览器中打开 `test-page.html`
2. 打开DevTools，切换到"API Interceptor"标签页
3. 点击测试页面上的各种按钮
4. 观察请求如何在DevTools面板中显示

测试页面包含：
- JSON API测试（GET、POST、PUT）
- XMLHttpRequest测试
- 外部API测试（GitHub、天气API）
- 错误测试（404、网络错误）

## 🔧 技术实现

### 多层请求捕获机制

1. **Chrome DevTools Network API**：
   - 使用 `chrome.devtools.network.onRequestFinished` 监听
   - 能够获取完整的请求/响应信息
   - 自动提取响应体内容

2. **webRequest API拦截**：
   - 在background script中使用webRequest API
   - 捕获请求的各个阶段（开始、头部、完成）
   - 作为DevTools API的补充

3. **Content Script拦截**：
   - 拦截页面中的fetch和XMLHttpRequest调用
   - 直接从页面上下文中获取响应体
   - 确保不遗漏任何请求

### 数据流

```
页面请求 → DevTools Network API → Background Script → DevTools Panel
         ↘ Content Script ↗
         ↘ webRequest API ↗
```

## 🎯 使用场景

1. **API调试**：查看API请求和响应的完整内容
2. **网络分析**：分析页面的网络请求模式
3. **问题排查**：调试网络请求相关的问题
4. **数据提取**：导出请求数据进行进一步分析
5. **开发测试**：验证前端应用的网络请求行为

## 📝 注意事项

1. **权限要求**：插件需要webRequest和activeTab权限
2. **CORS限制**：某些跨域请求可能受到浏览器CORS策略限制
3. **性能考虑**：大量请求可能影响性能，面板限制显示最新1000个请求
4. **开发模式**：在开发模式下重新加载插件可能需要刷新页面

## 🔄 更新和维护

- 插件会自动处理扩展上下文失效的情况
- 支持热重载开发模式
- 包含完整的错误处理机制

---

现在您的Chrome插件已经具备了完整的DevTools面板功能，可以像Chrome原生Network标签页一样捕获和显示网络请求及响应体！
