// DevTools registration script for API Interceptor Extension
// This script creates the DevTools panel and sets up network request monitoring

console.log("🚀 API Interceptor DevTools script loaded");

// Create the DevTools panel
chrome.devtools.panels.create(
  "🌐 API Interceptor 1",
  "", // Panel icon
  "devtools-panel.html", // Panel HTML file
  function(panel) {
    console.log("✅ API Interceptor DevTools panel created");

    panel.onShown.addListener(function(panelWindow) {
      console.log("👁️ API Interceptor panel shown");
      if (panelWindow && panelWindow.initPanel) {
        panelWindow.initPanel();
      }
    });

    panel.onHidden.addListener(function() {
      console.log("👁️‍🗨️ API Interceptor panel hidden");
    });
  }
);

// Listen for network events and capture requests with response bodies
chrome.devtools.network.onRequestFinished.addListener(function(request) {
  console.log("🌐 Network request finished:", request.request.url);

  // Create unique request ID
  const requestId = `devtools_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  // Create request data object
  const requestData = {
    id: requestId,
    url: request.request.url,
    method: request.request.method,
    timestamp: new Date(request.startedDateTime).getTime(),
    status: request.response.status,
    statusText: request.response.statusText,
    requestHeaders: request.request.headers || [],
    responseHeaders: request.response.headers || [],
    requestBody: request.request.postData ? request.request.postData.text : null,
    responseBody: null,
    duration: request.time,
    type: 'devtools',
    mimeType: request.response.content?.mimeType || 'unknown',
    size: request.response.content?.size || 0,
    tabId: chrome.devtools.inspectedWindow.tabId
  };

  // Get response body asynchronously
  request.getContent(function(content, encoding) {
    if (content) {
      requestData.responseBody = content;
      requestData.encoding = encoding;
      console.log("📦 Response body captured for:", request.request.url, `(${content.length} chars)`);
    } else {
      console.log("⚠️ No response body for:", request.request.url);
    }

    // Send complete request data to background script
    chrome.runtime.sendMessage({
      type: 'DEVTOOLS_REQUEST_COMPLETE',
      data: requestData
    }, (response) => {
      // Handle response if needed
      if (chrome.runtime.lastError) {
        console.error("❌ Error sending complete request data:", chrome.runtime.lastError.message);
      } else if (response && !response.success) {
        console.warn("⚠️ Request data not processed successfully:", response.message);
      }
    });
  });
});

console.log("🔧 DevTools script setup complete");

// Export a placeholder React component for Plasmo compatibility
import React from "react";

function DevTools() {
  return (
    <div style={{ display: 'none' }}>
      {/* This component is not used - actual DevTools functionality is handled above */}
    </div>
  );
}

export default DevTools;
