import React from 'react';
import { Activity, CheckCircle, AlertCircle, Clock, TrendingUp } from 'lucide-react';
import type { RequestData } from '../types';

interface RequestStatsProps {
  requests: RequestData[];
}

const RequestStats: React.FC<RequestStatsProps> = ({ requests }) => {
  const stats = React.useMemo(() => {
    const total = requests.length;
    const success = requests.filter(r => r.status && r.status >= 200 && r.status < 400).length;
    const error = requests.filter(r => !r.status || r.status >= 400).length;
    const pending = requests.filter(r => !r.status).length;
    
    const completedRequests = requests.filter(r => r.duration);
    const averageResponseTime = completedRequests.length > 0
      ? completedRequests.reduce((sum, r) => sum + (r.duration || 0), 0) / completedRequests.length
      : 0;

    return {
      total,
      success,
      error,
      pending,
      averageResponseTime
    };
  }, [requests]);

  const formatDuration = (duration: number) => {
    if (duration < 1000) return `${Math.round(duration)}ms`;
    return `${(duration / 1000).toFixed(2)}s`;
  };

  const getSuccessRate = () => {
    if (stats.total === 0) return 0;
    return Math.round((stats.success / stats.total) * 100);
  };

  return (
    <div className="grid grid-cols-2 gap-3 mb-4">
      {/* Total Requests */}
      <div className="card p-3">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-xs text-gray-600 font-medium">Total</p>
            <p className="text-lg font-bold text-gray-900">{stats.total}</p>
          </div>
          <div className="p-2 bg-primary-100 rounded-lg">
            <Activity className="w-4 h-4 text-primary-600" />
          </div>
        </div>
      </div>

      {/* Success Rate */}
      <div className="card p-3">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-xs text-gray-600 font-medium">Success</p>
            <p className="text-lg font-bold text-success-600">{getSuccessRate()}%</p>
          </div>
          <div className="p-2 bg-success-100 rounded-lg">
            <CheckCircle className="w-4 h-4 text-success-600" />
          </div>
        </div>
      </div>

      {/* Error Count */}
      <div className="card p-3">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-xs text-gray-600 font-medium">Errors</p>
            <p className="text-lg font-bold text-error-600">{stats.error}</p>
          </div>
          <div className="p-2 bg-error-100 rounded-lg">
            <AlertCircle className="w-4 h-4 text-error-600" />
          </div>
        </div>
      </div>

      {/* Average Response Time */}
      <div className="card p-3">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-xs text-gray-600 font-medium">Avg Time</p>
            <p className="text-lg font-bold text-gray-900">
              {formatDuration(stats.averageResponseTime)}
            </p>
          </div>
          <div className="p-2 bg-warning-100 rounded-lg">
            <Clock className="w-4 h-4 text-warning-600" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default RequestStats;
