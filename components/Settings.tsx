import React, { useState, useEffect } from 'react';
import { ArrowLeft, Save, RotateCcw, Shield, Database, Filter } from 'lucide-react';
import { clsx } from 'clsx';
import DataManager from './DataManager';

interface SettingsProps {
  onBack: () => void;
  onDataChange: () => void;
}

interface SettingsData {
  maxRequests: number;
  autoCapture: boolean;
  captureResponseBody: boolean;
  filterDomains: string[];
}

const Settings: React.FC<SettingsProps> = ({ onBack, onDataChange }) => {
  const [settings, setSettings] = useState<SettingsData>({
    maxRequests: 1000,
    autoCapture: true,
    captureResponseBody: false,
    filterDomains: []
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [newDomain, setNewDomain] = useState('');
  const [activeTab, setActiveTab] = useState<'general' | 'filters' | 'data'>('general');

  // Load settings
  const loadSettings = async () => {
    setIsLoading(true);
    try {
      // For now, we'll use default settings since we haven't implemented settings storage yet
      // In a real implementation, you would load from chrome.storage
      setSettings({
        maxRequests: 1000,
        autoCapture: true,
        captureResponseBody: false,
        filterDomains: []
      });
    } catch (error) {
      console.error('Error loading settings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Save settings
  const saveSettings = async () => {
    setIsSaving(true);
    try {
      // In a real implementation, you would save to chrome.storage
      console.log('Saving settings:', settings);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
    } catch (error) {
      console.error('Error saving settings:', error);
    } finally {
      setIsSaving(false);
    }
  };

  // Reset settings to defaults
  const resetSettings = () => {
    if (confirm('Are you sure you want to reset all settings to defaults?')) {
      setSettings({
        maxRequests: 1000,
        autoCapture: true,
        captureResponseBody: false,
        filterDomains: []
      });
    }
  };

  // Add domain filter
  const addDomainFilter = () => {
    if (newDomain.trim() && !settings.filterDomains.includes(newDomain.trim())) {
      setSettings(prev => ({
        ...prev,
        filterDomains: [...prev.filterDomains, newDomain.trim()]
      }));
      setNewDomain('');
    }
  };

  // Remove domain filter
  const removeDomainFilter = (domain: string) => {
    setSettings(prev => ({
      ...prev,
      filterDomains: prev.filterDomains.filter(d => d !== domain)
    }));
  };

  useEffect(() => {
    loadSettings();
  }, []);

  const tabs = [
    { id: 'general' as const, label: 'General', icon: Shield },
    { id: 'filters' as const, label: 'Filters', icon: Filter },
    { id: 'data' as const, label: 'Data', icon: Database }
  ];

  return (
    <div className="w-[480px] h-[600px] bg-white flex flex-col">
      {/* Header */}
      <div className="border-b border-gray-200 p-4">
        <div className="flex items-center gap-3 mb-3">
          <button onClick={onBack} className="btn btn-ghost btn-sm">
            <ArrowLeft className="w-4 h-4" />
          </button>
          <h1 className="text-lg font-bold text-gray-900">Settings</h1>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-200 -mb-4">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={clsx(
                  'flex items-center gap-2 px-3 py-2 text-sm font-medium border-b-2 transition-colors',
                  activeTab === tab.id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                )}
              >
                <Icon className="w-4 h-4" />
                {tab.label}
              </button>
            );
          })}
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto scrollbar-thin p-4">
        {isLoading ? (
          <div className="flex items-center justify-center h-full">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
          </div>
        ) : (
          <>
            {activeTab === 'general' && (
              <div className="space-y-6">
                {/* Auto Capture */}
                <div className="card p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium text-gray-900">Auto Capture</h3>
                      <p className="text-sm text-gray-600">Automatically capture all network requests</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={settings.autoCapture}
                        onChange={(e) => setSettings(prev => ({ ...prev, autoCapture: e.target.checked }))}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                    </label>
                  </div>
                </div>

                {/* Max Requests */}
                <div className="card p-4">
                  <div>
                    <h3 className="font-medium text-gray-900 mb-2">Maximum Requests</h3>
                    <p className="text-sm text-gray-600 mb-3">Maximum number of requests to store</p>
                    <input
                      type="number"
                      min="100"
                      max="10000"
                      step="100"
                      value={settings.maxRequests}
                      onChange={(e) => setSettings(prev => ({ ...prev, maxRequests: parseInt(e.target.value) || 1000 }))}
                      className="input w-full"
                    />
                  </div>
                </div>

                {/* Response Body Capture */}
                <div className="card p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium text-gray-900">Capture Response Body</h3>
                      <p className="text-sm text-gray-600">Attempt to capture response body (limited by browser security)</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={settings.captureResponseBody}
                        onChange={(e) => setSettings(prev => ({ ...prev, captureResponseBody: e.target.checked }))}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                    </label>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'filters' && (
              <div className="space-y-6">
                {/* Domain Filters */}
                <div className="card p-4">
                  <h3 className="font-medium text-gray-900 mb-3">Domain Filters</h3>
                  <p className="text-sm text-gray-600 mb-4">Only capture requests from these domains (leave empty to capture all)</p>
                  
                  {/* Add Domain */}
                  <div className="flex gap-2 mb-4">
                    <input
                      type="text"
                      placeholder="example.com"
                      value={newDomain}
                      onChange={(e) => setNewDomain(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && addDomainFilter()}
                      className="input flex-1"
                    />
                    <button
                      onClick={addDomainFilter}
                      className="btn btn-primary"
                    >
                      Add
                    </button>
                  </div>

                  {/* Domain List */}
                  {settings.filterDomains.length > 0 ? (
                    <div className="space-y-2">
                      {settings.filterDomains.map((domain) => (
                        <div key={domain} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                          <span className="text-sm font-mono">{domain}</span>
                          <button
                            onClick={() => removeDomainFilter(domain)}
                            className="text-error-600 hover:text-error-700 text-sm"
                          >
                            Remove
                          </button>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-gray-500 italic">No domain filters configured</p>
                  )}
                </div>
              </div>
            )}

            {activeTab === 'data' && (
              <DataManager onDataChange={onDataChange} />
            )}
          </>
        )}
      </div>

      {/* Footer */}
      {activeTab !== 'data' && (
        <div className="border-t border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <button
              onClick={resetSettings}
              className="btn btn-ghost text-gray-600"
            >
              <RotateCcw className="w-4 h-4 mr-2" />
              Reset to Defaults
            </button>
            
            <button
              onClick={saveSettings}
              disabled={isSaving}
              className="btn btn-primary"
            >
              <Save className="w-4 h-4 mr-2" />
              {isSaving ? 'Saving...' : 'Save Settings'}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default Settings;
