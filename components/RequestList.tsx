import React from 'react';
import { Clock, Globe, ArrowRight, AlertCircle, CheckCircle } from 'lucide-react';
import { clsx } from 'clsx';
import type { RequestData } from '../types';

interface RequestListProps {
  requests: RequestData[];
  onRequestSelect: (request: RequestData) => void;
  selectedRequestId?: string;
}

const RequestList: React.FC<RequestListProps> = ({
  requests,
  onRequestSelect,
  selectedRequestId
}) => {
  const getMethodClass = (method: string) => {
    const methodLower = method.toLowerCase();
    switch (methodLower) {
      case 'get':
        return 'method-get';
      case 'post':
        return 'method-post';
      case 'put':
        return 'method-put';
      case 'delete':
        return 'method-delete';
      case 'patch':
        return 'method-patch';
      default:
        return 'status-info';
    }
  };

  const getStatusClass = (status?: number) => {
    if (!status) return 'status-error';
    if (status >= 200 && status < 300) return 'status-success';
    if (status >= 300 && status < 400) return 'status-warning';
    if (status >= 400) return 'status-error';
    return 'status-info';
  };

  const getStatusIcon = (status?: number) => {
    if (!status) return <AlertCircle className="w-3 h-3" />;
    if (status >= 200 && status < 400) return <CheckCircle className="w-3 h-3" />;
    return <AlertCircle className="w-3 h-3" />;
  };

  const formatDuration = (duration?: number) => {
    if (!duration) return '-';
    if (duration < 1000) return `${duration}ms`;
    return `${(duration / 1000).toFixed(2)}s`;
  };

  const formatUrl = (url: string) => {
    try {
      const urlObj = new URL(url);
      const path = urlObj.pathname + urlObj.search;
      return {
        domain: urlObj.hostname,
        path: path.length > 50 ? path.substring(0, 47) + '...' : path
      };
    } catch {
      return {
        domain: 'Invalid URL',
        path: url.length > 50 ? url.substring(0, 47) + '...' : url
      };
    }
  };

  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('en-US', { 
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  if (requests.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-gray-500">
        <Globe className="w-12 h-12 mb-4 opacity-50" />
        <p className="text-sm font-medium">No requests captured yet</p>
        <p className="text-xs text-gray-400 mt-1">Browse any website to see requests</p>
      </div>
    );
  }

  return (
    <div className="space-y-1">
      {requests.map((request) => {
        const { domain, path } = formatUrl(request.url);
        const isSelected = selectedRequestId === request.id;
        
        return (
          <div
            key={request.id}
            onClick={() => onRequestSelect(request)}
            className={clsx(
              'p-3 rounded-lg border cursor-pointer transition-all duration-200 hover:shadow-md',
              isSelected 
                ? 'border-primary-300 bg-primary-50 shadow-sm' 
                : 'border-gray-200 bg-white hover:border-gray-300'
            )}
          >
            <div className="flex items-start justify-between gap-3">
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <span className={clsx('status-badge text-xs font-medium px-2 py-0.5 rounded', getMethodClass(request.method))}>
                    {request.method}
                  </span>
                  <span className={clsx('status-badge text-xs px-2 py-0.5 rounded flex items-center gap-1', getStatusClass(request.status))}>
                    {getStatusIcon(request.status)}
                    {request.status || 'ERR'}
                  </span>
                </div>
                
                <div className="space-y-1">
                  <div className="flex items-center gap-1 text-xs text-gray-600">
                    <Globe className="w-3 h-3 flex-shrink-0" />
                    <span className="font-medium">{domain}</span>
                  </div>
                  <div className="text-sm font-mono text-gray-900 truncate">
                    {path}
                  </div>
                </div>
              </div>
              
              <div className="flex flex-col items-end gap-1 text-xs text-gray-500 flex-shrink-0">
                <div className="flex items-center gap-1">
                  <Clock className="w-3 h-3" />
                  <span>{formatTime(request.timestamp)}</span>
                </div>
                <div className="font-medium">
                  {formatDuration(request.duration)}
                </div>
              </div>
            </div>
            
            {isSelected && (
              <div className="mt-2 pt-2 border-t border-primary-200">
                <div className="flex items-center justify-between text-xs text-primary-700">
                  <span>Click to view details</span>
                  <ArrowRight className="w-3 h-3" />
                </div>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default RequestList;
