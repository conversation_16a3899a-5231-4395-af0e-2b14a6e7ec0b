import React, { useState, useRef } from 'react';
import { Download, Upload, Trash2, HardDrive, AlertCircle, CheckCircle } from 'lucide-react';
import { clsx } from 'clsx';

interface DataManagerProps {
  onDataChange: () => void;
}

interface StorageStats {
  requestCount: number;
  storageUsed: number;
  storageQuota: number;
  usagePercentage: number;
}

const DataManager: React.FC<DataManagerProps> = ({ onDataChange }) => {
  const [isExporting, setIsExporting] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [isClearing, setIsClearing] = useState(false);
  const [storageStats, setStorageStats] = useState<StorageStats | null>(null);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Load storage stats
  const loadStorageStats = async () => {
    try {
      const response = await chrome.runtime.sendMessage({ type: 'GET_STORAGE_STATS' });
      setStorageStats(response.stats);
    } catch (error) {
      console.error('Error loading storage stats:', error);
    }
  };

  // Export requests to JSON file
  const handleExport = async () => {
    setIsExporting(true);
    try {
      const response = await chrome.runtime.sendMessage({ type: 'EXPORT_REQUESTS' });
      
      if (response.error) {
        throw new Error(response.error);
      }

      // Create and download file
      const blob = new Blob([response.data], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `api-interceptor-export-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      setMessage({ type: 'success', text: 'Requests exported successfully!' });
    } catch (error) {
      setMessage({ type: 'error', text: `Export failed: ${error.message}` });
    } finally {
      setIsExporting(false);
    }
  };

  // Import requests from JSON file
  const handleImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsImporting(true);
    try {
      const text = await file.text();
      const response = await chrome.runtime.sendMessage({ 
        type: 'IMPORT_REQUESTS', 
        data: text 
      });

      if (response.error) {
        throw new Error(response.error);
      }

      setMessage({ 
        type: 'success', 
        text: `Successfully imported ${response.importedCount} requests!` 
      });
      onDataChange();
    } catch (error) {
      setMessage({ type: 'error', text: `Import failed: ${error.message}` });
    } finally {
      setIsImporting(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  // Clear all requests
  const handleClear = async () => {
    if (!confirm('Are you sure you want to clear all captured requests? This action cannot be undone.')) {
      return;
    }

    setIsClearing(true);
    try {
      await chrome.runtime.sendMessage({ type: 'CLEAR_REQUESTS' });
      setMessage({ type: 'success', text: 'All requests cleared successfully!' });
      onDataChange();
    } catch (error) {
      setMessage({ type: 'error', text: `Clear failed: ${error.message}` });
    } finally {
      setIsClearing(false);
    }
  };

  // Format bytes to human readable
  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Clear message after 3 seconds
  React.useEffect(() => {
    if (message) {
      const timer = setTimeout(() => setMessage(null), 3000);
      return () => clearTimeout(timer);
    }
  }, [message]);

  // Load storage stats on mount
  React.useEffect(() => {
    loadStorageStats();
  }, []);

  return (
    <div className="space-y-4">
      {/* Message */}
      {message && (
        <div className={clsx(
          'p-3 rounded-lg flex items-center gap-2 text-sm animate-fade-in',
          message.type === 'success' 
            ? 'bg-success-100 text-success-800' 
            : 'bg-error-100 text-error-800'
        )}>
          {message.type === 'success' ? (
            <CheckCircle className="w-4 h-4" />
          ) : (
            <AlertCircle className="w-4 h-4" />
          )}
          {message.text}
        </div>
      )}

      {/* Storage Stats */}
      {storageStats && (
        <div className="card p-4">
          <div className="flex items-center gap-2 mb-3">
            <HardDrive className="w-4 h-4 text-gray-600" />
            <h3 className="font-medium text-gray-900">Storage Usage</h3>
          </div>
          
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Requests stored:</span>
              <span className="font-medium">{storageStats.requestCount.toLocaleString()}</span>
            </div>
            
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Storage used:</span>
              <span className="font-medium">
                {formatBytes(storageStats.storageUsed)} / {formatBytes(storageStats.storageQuota)}
              </span>
            </div>
            
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className={clsx(
                  'h-2 rounded-full transition-all duration-300',
                  storageStats.usagePercentage > 80 
                    ? 'bg-error-500' 
                    : storageStats.usagePercentage > 60 
                      ? 'bg-warning-500' 
                      : 'bg-success-500'
                )}
                style={{ width: `${Math.min(storageStats.usagePercentage, 100)}%` }}
              />
            </div>
            
            <div className="text-xs text-gray-500 text-center">
              {storageStats.usagePercentage.toFixed(1)}% used
            </div>
          </div>
        </div>
      )}

      {/* Data Management Actions */}
      <div className="card p-4">
        <h3 className="font-medium text-gray-900 mb-3">Data Management</h3>
        
        <div className="space-y-3">
          {/* Export */}
          <button
            onClick={handleExport}
            disabled={isExporting}
            className="btn btn-primary w-full flex items-center justify-center gap-2"
          >
            <Download className="w-4 h-4" />
            {isExporting ? 'Exporting...' : 'Export Requests'}
          </button>

          {/* Import */}
          <div>
            <input
              ref={fileInputRef}
              type="file"
              accept=".json"
              onChange={handleImport}
              className="hidden"
            />
            <button
              onClick={() => fileInputRef.current?.click()}
              disabled={isImporting}
              className="btn btn-secondary w-full flex items-center justify-center gap-2"
            >
              <Upload className="w-4 h-4" />
              {isImporting ? 'Importing...' : 'Import Requests'}
            </button>
          </div>

          {/* Clear */}
          <button
            onClick={handleClear}
            disabled={isClearing}
            className="btn w-full flex items-center justify-center gap-2 bg-error-600 text-white hover:bg-error-700"
          >
            <Trash2 className="w-4 h-4" />
            {isClearing ? 'Clearing...' : 'Clear All Requests'}
          </button>
        </div>
      </div>

      {/* Help Text */}
      <div className="text-xs text-gray-500 space-y-1">
        <p>• Export: Download all captured requests as a JSON file</p>
        <p>• Import: Load requests from a previously exported JSON file</p>
        <p>• Clear: Remove all stored requests (cannot be undone)</p>
      </div>
    </div>
  );
};

export default DataManager;
