import React, { useState } from 'react';
import { 
  ArrowLeft, 
  Copy, 
  Download, 
  Clock, 
  Globe, 
  CheckCircle, 
  AlertCircle,
  Code,
  FileText,
  Settings
} from 'lucide-react';
import { clsx } from 'clsx';
import type { RequestData } from '../types';

interface RequestDetailsProps {
  request: RequestData;
  onBack: () => void;
}

type TabType = 'headers' | 'request' | 'response' | 'timing';

const RequestDetails: React.FC<RequestDetailsProps> = ({ request, onBack }) => {
  const [activeTab, setActiveTab] = useState<TabType>('headers');

  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  };

  const formatDuration = (duration?: number) => {
    if (!duration) return 'N/A';
    if (duration < 1000) return `${duration}ms`;
    return `${(duration / 1000).toFixed(2)}s`;
  };

  const getStatusClass = (status?: number) => {
    if (!status) return 'text-error-600 bg-error-100';
    if (status >= 200 && status < 300) return 'text-success-600 bg-success-100';
    if (status >= 300 && status < 400) return 'text-warning-600 bg-warning-100';
    if (status >= 400) return 'text-error-600 bg-error-100';
    return 'text-gray-600 bg-gray-100';
  };

  const getStatusIcon = (status?: number) => {
    if (!status) return <AlertCircle className="w-4 h-4" />;
    if (status >= 200 && status < 400) return <CheckCircle className="w-4 h-4" />;
    return <AlertCircle className="w-4 h-4" />;
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const formatUrl = (url: string) => {
    try {
      const urlObj = new URL(url);
      return {
        protocol: urlObj.protocol,
        host: urlObj.host,
        pathname: urlObj.pathname,
        search: urlObj.search,
        hash: urlObj.hash
      };
    } catch {
      return { protocol: '', host: '', pathname: url, search: '', hash: '' };
    }
  };

  const urlParts = formatUrl(request.url);

  const tabs = [
    { id: 'headers' as TabType, label: 'Headers', icon: Settings },
    { id: 'request' as TabType, label: 'Request', icon: FileText },
    { id: 'response' as TabType, label: 'Response', icon: Code },
    { id: 'timing' as TabType, label: 'Timing', icon: Clock }
  ];

  const renderHeaders = (headers: chrome.webRequest.HttpHeader[], title: string) => (
    <div className="space-y-2">
      <h4 className="font-medium text-gray-900 flex items-center justify-between">
        {title}
        <button
          onClick={() => copyToClipboard(JSON.stringify(headers, null, 2))}
          className="btn btn-ghost btn-sm"
        >
          <Copy className="w-3 h-3" />
        </button>
      </h4>
      {headers.length > 0 ? (
        <div className="space-y-1">
          {headers.map((header, index) => (
            <div key={index} className="flex text-sm">
              <span className="font-medium text-gray-700 w-1/3 break-words">
                {header.name}:
              </span>
              <span className="text-gray-900 w-2/3 break-words font-mono">
                {header.value}
              </span>
            </div>
          ))}
        </div>
      ) : (
        <p className="text-sm text-gray-500 italic">No headers</p>
      )}
    </div>
  );

  const renderJsonContent = (content: string, title: string) => {
    try {
      const parsed = JSON.parse(content);
      const formatted = JSON.stringify(parsed, null, 2);
      return (
        <div className="space-y-2">
          <h4 className="font-medium text-gray-900 flex items-center justify-between">
            {title}
            <button
              onClick={() => copyToClipboard(formatted)}
              className="btn btn-ghost btn-sm"
            >
              <Copy className="w-3 h-3" />
            </button>
          </h4>
          <pre className="bg-gray-50 p-3 rounded-lg text-xs font-mono overflow-x-auto scrollbar-thin">
            <code>{formatted}</code>
          </pre>
        </div>
      );
    } catch {
      return (
        <div className="space-y-2">
          <h4 className="font-medium text-gray-900 flex items-center justify-between">
            {title}
            <button
              onClick={() => copyToClipboard(content)}
              className="btn btn-ghost btn-sm"
            >
              <Copy className="w-3 h-3" />
            </button>
          </h4>
          <pre className="bg-gray-50 p-3 rounded-lg text-xs font-mono overflow-x-auto scrollbar-thin">
            <code>{content}</code>
          </pre>
        </div>
      );
    }
  };

  return (
    <div className="w-[480px] h-[600px] bg-white flex flex-col">
      {/* Header */}
      <div className="border-b border-gray-200 p-4">
        <div className="flex items-center gap-3 mb-3">
          <button onClick={onBack} className="btn btn-ghost btn-sm">
            <ArrowLeft className="w-4 h-4" />
          </button>
          <h1 className="text-lg font-bold text-gray-900">Request Details</h1>
        </div>

        {/* Request Summary */}
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <span className={clsx('px-2 py-1 rounded text-xs font-medium', `method-${request.method.toLowerCase()}`)}>
              {request.method}
            </span>
            <span className={clsx('px-2 py-1 rounded text-xs font-medium flex items-center gap-1', getStatusClass(request.status))}>
              {getStatusIcon(request.status)}
              {request.status || 'ERR'}
            </span>
            <span className="text-xs text-gray-600">
              {formatDuration(request.duration)}
            </span>
          </div>

          <div className="space-y-1">
            <div className="flex items-center gap-2 text-sm">
              <Globe className="w-4 h-4 text-gray-400" />
              <span className="font-medium text-gray-700">{urlParts.host}</span>
            </div>
            <div className="text-sm font-mono text-gray-900 break-all">
              <span className="text-gray-500">{urlParts.protocol}//</span>
              <span className="font-medium">{urlParts.host}</span>
              <span>{urlParts.pathname}</span>
              <span className="text-blue-600">{urlParts.search}</span>
              <span className="text-purple-600">{urlParts.hash}</span>
            </div>
          </div>

          <div className="text-xs text-gray-500">
            {formatTime(request.timestamp)}
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <div className="flex">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={clsx(
                  'flex items-center gap-2 px-4 py-3 text-sm font-medium border-b-2 transition-colors',
                  activeTab === tab.id
                    ? 'border-primary-500 text-primary-600 bg-primary-50'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                )}
              >
                <Icon className="w-4 h-4" />
                {tab.label}
              </button>
            );
          })}
        </div>
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-y-auto scrollbar-thin p-4">
        {activeTab === 'headers' && (
          <div className="space-y-6">
            {renderHeaders(request.requestHeaders, 'Request Headers')}
            {renderHeaders(request.responseHeaders || [], 'Response Headers')}
          </div>
        )}

        {activeTab === 'request' && (
          <div className="space-y-4">
            {request.requestBody ? (
              renderJsonContent(request.requestBody, 'Request Body')
            ) : (
              <div className="text-center py-8 text-gray-500">
                <FileText className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p>No request body</p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'response' && (
          <div className="space-y-4">
            {request.responseBody ? (
              renderJsonContent(request.responseBody, 'Response Body')
            ) : (
              <div className="text-center py-8 text-gray-500">
                <Code className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p>No response body captured</p>
                <p className="text-xs mt-1">Response body interception is limited by browser security</p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'timing' && (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="card p-3">
                <div className="text-xs text-gray-600 mb-1">Started</div>
                <div className="text-sm font-medium">{formatTime(request.timestamp)}</div>
              </div>
              <div className="card p-3">
                <div className="text-xs text-gray-600 mb-1">Duration</div>
                <div className="text-sm font-medium">{formatDuration(request.duration)}</div>
              </div>
            </div>
            
            <div className="card p-4">
              <h4 className="font-medium text-gray-900 mb-3">Request Timeline</h4>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Request initiated</span>
                  <span className="font-mono">0ms</span>
                </div>
                {request.duration && (
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Response received</span>
                    <span className="font-mono">{formatDuration(request.duration)}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default RequestDetails;
