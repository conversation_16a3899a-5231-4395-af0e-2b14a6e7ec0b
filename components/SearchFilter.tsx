import React, { useState } from 'react';
import { Search, Filter, X, RotateCcw } from 'lucide-react';
import { clsx } from 'clsx';
import type { RequestFilter } from '../types';

interface SearchFilterProps {
  onFilterChange: (filter: RequestFilter & { searchTerm: string }) => void;
  onClear: () => void;
  requestCount: number;
}

const SearchFilter: React.FC<SearchFilterProps> = ({
  onFilterChange,
  onClear,
  requestCount
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedMethod, setSelectedMethod] = useState<string>('');
  const [selectedStatus, setSelectedStatus] = useState<string>('');
  const [selectedType, setSelectedType] = useState<string>('');
  const [showFilters, setShowFilters] = useState(false);

  const methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS', 'HEAD'];
  const statusGroups = [
    { label: '2xx Success', value: '2xx' },
    { label: '3xx Redirect', value: '3xx' },
    { label: '4xx Client Error', value: '4xx' },
    { label: '5xx Server Error', value: '5xx' },
    { label: 'Failed', value: 'failed' }
  ];
  const types = ['xmlhttprequest', 'fetch', 'document', 'stylesheet', 'script', 'image', 'font', 'other'];

  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    updateFilter({ searchTerm: value });
  };

  const handleMethodChange = (method: string) => {
    const newMethod = selectedMethod === method ? '' : method;
    setSelectedMethod(newMethod);
    updateFilter({ method: newMethod || undefined });
  };

  const handleStatusChange = (status: string) => {
    const newStatus = selectedStatus === status ? '' : status;
    setSelectedStatus(newStatus);
    updateFilter({ status: newStatus || undefined });
  };

  const handleTypeChange = (type: string) => {
    const newType = selectedType === type ? '' : type;
    setSelectedType(newType);
    updateFilter({ type: newType || undefined });
  };

  const updateFilter = (updates: Partial<RequestFilter & { searchTerm: string }>) => {
    onFilterChange({
      searchTerm,
      method: selectedMethod || undefined,
      status: selectedStatus || undefined,
      type: selectedType || undefined,
      ...updates
    });
  };

  const clearAllFilters = () => {
    setSearchTerm('');
    setSelectedMethod('');
    setSelectedStatus('');
    setSelectedType('');
    onFilterChange({ searchTerm: '' });
  };

  const hasActiveFilters = searchTerm || selectedMethod || selectedStatus || selectedType;

  return (
    <div className="space-y-3">
      {/* Search Bar */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
        <input
          type="text"
          placeholder="Search requests by URL, method, or status..."
          value={searchTerm}
          onChange={(e) => handleSearchChange(e.target.value)}
          className="input pl-10 pr-10"
        />
        {searchTerm && (
          <button
            onClick={() => handleSearchChange('')}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
          >
            <X className="w-4 h-4" />
          </button>
        )}
      </div>

      {/* Filter Toggle and Clear */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={clsx(
              'btn btn-sm flex items-center gap-2',
              showFilters ? 'btn-primary' : 'btn-secondary'
            )}
          >
            <Filter className="w-3 h-3" />
            Filters
            {hasActiveFilters && (
              <span className="bg-primary-600 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                {[selectedMethod, selectedStatus, selectedType, searchTerm].filter(Boolean).length}
              </span>
            )}
          </button>
          
          {hasActiveFilters && (
            <button
              onClick={clearAllFilters}
              className="btn btn-ghost btn-sm flex items-center gap-1 text-gray-600"
            >
              <RotateCcw className="w-3 h-3" />
              Clear
            </button>
          )}
        </div>

        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600">
            {requestCount} request{requestCount !== 1 ? 's' : ''}
          </span>
          <button
            onClick={onClear}
            className="btn btn-ghost btn-sm text-error-600 hover:bg-error-50"
          >
            Clear All
          </button>
        </div>
      </div>

      {/* Filter Options */}
      {showFilters && (
        <div className="card p-4 space-y-4 animate-slide-up">
          {/* Method Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Method</label>
            <div className="flex flex-wrap gap-2">
              {methods.map((method) => (
                <button
                  key={method}
                  onClick={() => handleMethodChange(method)}
                  className={clsx(
                    'btn btn-sm',
                    selectedMethod === method ? 'btn-primary' : 'btn-secondary'
                  )}
                >
                  {method}
                </button>
              ))}
            </div>
          </div>

          {/* Status Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
            <div className="flex flex-wrap gap-2">
              {statusGroups.map((group) => (
                <button
                  key={group.value}
                  onClick={() => handleStatusChange(group.value)}
                  className={clsx(
                    'btn btn-sm',
                    selectedStatus === group.value ? 'btn-primary' : 'btn-secondary'
                  )}
                >
                  {group.label}
                </button>
              ))}
            </div>
          </div>

          {/* Type Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Type</label>
            <div className="flex flex-wrap gap-2">
              {types.map((type) => (
                <button
                  key={type}
                  onClick={() => handleTypeChange(type)}
                  className={clsx(
                    'btn btn-sm capitalize',
                    selectedType === type ? 'btn-primary' : 'btn-secondary'
                  )}
                >
                  {type}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SearchFilter;
