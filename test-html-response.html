<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTML Response Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f0f0f0;
            border-radius: 4px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>🧪 HTML Response Body Test</h1>
    <p>This page tests how the API Interceptor Extension handles different types of response bodies, especially HTML content.</p>

    <div class="test-section">
        <h2>📄 HTML Response Test</h2>
        <p>Click the button below to make a request that returns HTML content:</p>
        <button onclick="testHtmlResponse()">Test HTML Response</button>
        <div id="htmlResult" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>📋 JSON Response Test</h2>
        <p>Click the button below to make a request that returns JSON content:</p>
        <button onclick="testJsonResponse()">Test JSON Response</button>
        <div id="jsonResult" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>🌐 XML Response Test</h2>
        <p>Click the button below to make a request that returns XML content:</p>
        <button onclick="testXmlResponse()">Test XML Response</button>
        <div id="xmlResult" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>📝 Plain Text Response Test</h2>
        <p>Click the button below to make a request that returns plain text:</p>
        <button onclick="testTextResponse()">Test Text Response</button>
        <div id="textResult" class="result" style="display: none;"></div>
    </div>

    <script>
        // Create a mock server response using data URLs
        function createMockResponse(content, contentType) {
            return new Promise((resolve) => {
                // Simulate network delay
                setTimeout(() => {
                    resolve({
                        content: content,
                        contentType: contentType,
                        status: 200,
                        statusText: 'OK'
                    });
                }, 500);
            });
        }

        async function testHtmlResponse() {
            const button = event.target;
            const resultDiv = document.getElementById('htmlResult');
            
            button.disabled = true;
            button.textContent = 'Loading...';
            resultDiv.style.display = 'block';
            resultDiv.textContent = 'Making request...';

            try {
                // Make a fetch request that returns HTML
                const response = await fetch('data:text/html,<!DOCTYPE html><html><head><title>Test Page</title></head><body><h1>Hello World</h1><p>This is a test HTML response with <strong>formatted</strong> content.</p><div class="container"><ul><li>Item 1</li><li>Item 2</li><li>Item 3</li></ul></div></body></html>');
                const html = await response.text();
                
                resultDiv.innerHTML = `
                    <strong>Response received!</strong><br>
                    Content-Type: ${response.headers.get('content-type') || 'text/html'}<br>
                    Status: ${response.status}<br>
                    <details>
                        <summary>Response Body (click to expand)</summary>
                        <pre>${html}</pre>
                    </details>
                `;
            } catch (error) {
                resultDiv.textContent = `Error: ${error.message}`;
            } finally {
                button.disabled = false;
                button.textContent = 'Test HTML Response';
            }
        }

        async function testJsonResponse() {
            const button = event.target;
            const resultDiv = document.getElementById('jsonResult');
            
            button.disabled = true;
            button.textContent = 'Loading...';
            resultDiv.style.display = 'block';
            resultDiv.textContent = 'Making request...';

            try {
                const jsonData = {
                    message: "Hello World",
                    data: {
                        users: [
                            { id: 1, name: "John Doe", email: "<EMAIL>" },
                            { id: 2, name: "Jane Smith", email: "<EMAIL>" }
                        ],
                        metadata: {
                            total: 2,
                            page: 1,
                            timestamp: new Date().toISOString()
                        }
                    }
                };

                const response = await fetch('data:application/json,' + encodeURIComponent(JSON.stringify(jsonData)));
                const json = await response.json();
                
                resultDiv.innerHTML = `
                    <strong>Response received!</strong><br>
                    Content-Type: ${response.headers.get('content-type') || 'application/json'}<br>
                    Status: ${response.status}<br>
                    <details>
                        <summary>Response Body (click to expand)</summary>
                        <pre>${JSON.stringify(json, null, 2)}</pre>
                    </details>
                `;
            } catch (error) {
                resultDiv.textContent = `Error: ${error.message}`;
            } finally {
                button.disabled = false;
                button.textContent = 'Test JSON Response';
            }
        }

        async function testXmlResponse() {
            const button = event.target;
            const resultDiv = document.getElementById('xmlResult');
            
            button.disabled = true;
            button.textContent = 'Loading...';
            resultDiv.style.display = 'block';
            resultDiv.textContent = 'Making request...';

            try {
                const xmlData = `<?xml version="1.0" encoding="UTF-8"?>
<root>
    <message>Hello World</message>
    <data>
        <users>
            <user id="1">
                <name>John Doe</name>
                <email><EMAIL></email>
            </user>
            <user id="2">
                <name>Jane Smith</name>
                <email><EMAIL></email>
            </user>
        </users>
        <metadata>
            <total>2</total>
            <page>1</page>
        </metadata>
    </data>
</root>`;

                const response = await fetch('data:application/xml,' + encodeURIComponent(xmlData));
                const xml = await response.text();
                
                resultDiv.innerHTML = `
                    <strong>Response received!</strong><br>
                    Content-Type: ${response.headers.get('content-type') || 'application/xml'}<br>
                    Status: ${response.status}<br>
                    <details>
                        <summary>Response Body (click to expand)</summary>
                        <pre>${xml}</pre>
                    </details>
                `;
            } catch (error) {
                resultDiv.textContent = `Error: ${error.message}`;
            } finally {
                button.disabled = false;
                button.textContent = 'Test XML Response';
            }
        }

        async function testTextResponse() {
            const button = event.target;
            const resultDiv = document.getElementById('textResult');
            
            button.disabled = true;
            button.textContent = 'Loading...';
            resultDiv.style.display = 'block';
            resultDiv.textContent = 'Making request...';

            try {
                const textData = `This is a plain text response.
It contains multiple lines.
And some special characters: <>&"'
Numbers: 123456789
Symbols: !@#$%^&*()_+-=[]{}|;:,.<>?`;

                const response = await fetch('data:text/plain,' + encodeURIComponent(textData));
                const text = await response.text();
                
                resultDiv.innerHTML = `
                    <strong>Response received!</strong><br>
                    Content-Type: ${response.headers.get('content-type') || 'text/plain'}<br>
                    Status: ${response.status}<br>
                    <details>
                        <summary>Response Body (click to expand)</summary>
                        <pre>${text}</pre>
                    </details>
                `;
            } catch (error) {
                resultDiv.textContent = `Error: ${error.message}`;
            } finally {
                button.disabled = false;
                button.textContent = 'Test Text Response';
            }
        }
    </script>
</body>
</html>
