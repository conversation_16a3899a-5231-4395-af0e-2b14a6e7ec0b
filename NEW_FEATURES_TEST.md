# 🆕 新功能测试指南

## 📋 新增功能概述

本次更新添加了两个重要功能：

1. **域名变化控制 + Preserve Log开关**
   - 默认情况下，切换域名时会清空请求列表
   - 开启Preserve Log后，切换域名也不会清空列表
   - 页面路径变化不会影响列表

2. **捕获开关控制**
   - 可以随时开启/关闭请求捕获
   - 关闭时不会捕获新的请求
   - 开启时恢复正常捕获

## 🎛️ 界面变化

### 新增控制按钮
- **▶️ Start / ⏸️ Stop**: 控制是否捕获请求
- **📌 Preserve Log**: 控制域名切换时是否保留日志

### 新增状态指示器
- **绿色圆点 + "Capture: On"**: 捕获功能已开启
- **红色圆点 + "Capture: Off"**: 捕获功能已关闭
- **Domain: [域名]**: 显示当前页面域名

## 🧪 测试步骤

### 测试1: 捕获开关功能

1. **初始状态验证**
   - 打开DevTools面板
   - 确认显示"▶️ Start"按钮和绿色状态点
   - 确认显示"Capture: On"

2. **关闭捕获测试**
   - 点击"⏸️ Stop"按钮
   - 确认按钮变为"▶️ Start"
   - 确认状态点变为红色，显示"Capture: Off"
   - 在测试页面点击API测试按钮
   - **验证**: 不应该有新请求出现在列表中

3. **开启捕获测试**
   - 点击"▶️ Start"按钮
   - 确认按钮变为"⏸️ Stop"
   - 确认状态点变为绿色，显示"Capture: On"
   - 在测试页面点击API测试按钮
   - **验证**: 新请求应该正常出现在列表中

### 测试2: Preserve Log功能

1. **默认行为测试（Preserve Log关闭）**
   - 确认"📌 Preserve Log"按钮未激活（灰色）
   - 在当前域名下捕获一些请求
   - 导航到不同域名的网站（如从localhost切换到google.com）
   - **验证**: 请求列表应该被清空

2. **Preserve Log开启测试**
   - 点击"📌 Preserve Log"按钮激活（变为绿色）
   - 在当前域名下捕获一些请求
   - 导航到不同域名的网站
   - **验证**: 请求列表应该保持不变

3. **页面路径变化测试**
   - 在同一域名下切换不同页面路径
   - 例如：从 `/page1` 切换到 `/page2`
   - **验证**: 无论Preserve Log开关状态如何，列表都不应该被清空

### 测试3: 域名检测功能

1. **域名显示测试**
   - 访问不同的网站
   - 观察"Domain: [域名]"显示是否正确更新

2. **域名变化日志测试**
   - 打开浏览器控制台
   - 切换不同域名的网站
   - **验证**: 应该看到类似日志：
     ```
     🌐 Domain changed from "localhost" to "google.com"
     🗑️ Clearing requests due to domain change (preserve log disabled)
     ```

### 测试4: 设置持久化测试

1. **设置保存测试**
   - 调整Preserve Log和Capture开关
   - 刷新DevTools面板
   - **验证**: 设置应该保持不变

2. **跨页面设置测试**
   - 在一个页面设置开关状态
   - 导航到另一个页面
   - 重新打开DevTools面板
   - **验证**: 设置应该保持一致

### 测试5: 综合功能测试

1. **复杂场景测试**
   - 开启捕获，关闭Preserve Log
   - 在域名A捕获一些请求
   - 切换到域名B（列表应该清空）
   - 关闭捕获
   - 在域名B进行网络请求（不应该被捕获）
   - 开启Preserve Log
   - 开启捕获
   - 在域名B捕获一些请求
   - 切换回域名A（列表应该保持）

## ✅ 预期结果

### 捕获开关功能
- ✅ 可以随时开启/关闭请求捕获
- ✅ 关闭时新请求不会被添加到列表
- ✅ 状态指示器正确显示当前状态
- ✅ 设置在页面刷新后保持

### Preserve Log功能
- ✅ 默认情况下域名切换会清空列表
- ✅ 开启后域名切换不会清空列表
- ✅ 页面路径变化不影响列表
- ✅ 设置状态正确保存和恢复

### UI改进
- ✅ 按钮状态清晰显示当前设置
- ✅ 状态指示器实时反映捕获状态
- ✅ 域名信息正确显示
- ✅ 界面响应流畅

## 🐛 可能的问题

如果遇到问题，请检查：

1. **设置不保存**: 检查浏览器控制台是否有存储相关错误
2. **域名检测不准确**: 确保在正确的页面上下文中测试
3. **按钮状态不更新**: 刷新DevTools面板重试
4. **捕获开关不生效**: 检查background script是否正确接收消息

## 📝 测试记录

请在测试时记录：
- [ ] 捕获开关基本功能
- [ ] Preserve Log基本功能  
- [ ] 域名变化检测
- [ ] 设置持久化
- [ ] UI状态更新
- [ ] 综合场景测试

---

**注意**: 测试前请确保重新加载插件，并使用最新构建的版本。
