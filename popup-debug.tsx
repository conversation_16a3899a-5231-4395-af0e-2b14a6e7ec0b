import React from "react";

function IndexPopup() {
  console.log("Popup component is rendering");
  
  return (
    <div style={{ 
      width: '400px', 
      height: '300px', 
      padding: '20px',
      backgroundColor: 'white',
      fontFamily: 'Arial, sans-serif'
    }}>
      <h1 style={{ color: 'black', margin: '0 0 20px 0' }}>
        Debug Version
      </h1>
      <p style={{ color: 'green', marginBottom: '10px' }}>
        ✅ React is working
      </p>
      <p style={{ color: 'blue', marginBottom: '10px' }}>
        ✅ Popup is rendering
      </p>
      <button 
        onClick={() => {
          console.log("Button clicked");
          alert("But<PERSON> works!");
        }}
        style={{
          padding: '10px 20px',
          backgroundColor: '#007cba',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer'
        }}
      >
        Test Button
      </button>
      <div style={{ marginTop: '20px', fontSize: '12px', color: '#666' }}>
        Time: {new Date().toLocaleTimeString()}
      </div>
    </div>
  );
}

export default IndexPopup;
