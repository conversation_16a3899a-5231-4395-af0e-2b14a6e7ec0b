var e,t;"function"==typeof(e=globalThis.define)&&(t=e,e=null),function(t,n,r,o,s){var i="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{},a="function"==typeof i[o]&&i[o],l=a.cache||{},c="undefined"!=typeof module&&"function"==typeof module.require&&module.require.bind(module);function d(e,n){if(!l[e]){if(!t[e]){var r="function"==typeof i[o]&&i[o];if(!n&&r)return r(e,!0);if(a)return a(e,!0);if(c&&"string"==typeof e)return c(e);var s=Error("Cannot find module '"+e+"'");throw s.code="MODULE_NOT_FOUND",s}p.resolve=function(n){var r=t[e][1][n];return null!=r?r:n},p.cache={};var u=l[e]=new d.Module(e);t[e][0].call(u.exports,p,u,u.exports,this)}return l[e].exports;function p(e){var t=p.resolve(e);return!1===t?{}:d(t)}}d.isParcelRequire=!0,d.Module=function(e){this.id=e,this.bundle=d,this.exports={}},d.modules=t,d.cache=l,d.parent=a,d.register=function(e,n){t[e]=[function(e,t){t.exports=n},{}]},Object.defineProperty(d,"root",{get:function(){return i[o]}}),i[o]=d;for(var u=0;u<n.length;u++)d(n[u]);if(r){var p=d(r);"object"==typeof exports&&"undefined"!=typeof module?module.exports=p:"function"==typeof e&&e.amd?e(function(){return p}):s&&(this[s]=p)}}({"4gaGD":[function(e,t,n){if("undefined"!=typeof chrome&&chrome.runtime&&chrome.runtime.id){function r(){return`req_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}function o(e){if(!function(){try{return!!(chrome&&chrome.runtime&&chrome.runtime.id)}catch(e){return!1}}()){console.log("Extension context invalidated, skipping message");return}try{chrome.runtime.sendMessage({type:"INTERCEPTED_REQUEST",data:e},e=>{if(chrome.runtime.lastError){if(chrome.runtime.lastError.message&&chrome.runtime.lastError.message.includes("Extension context invalidated")){console.log("Extension context invalidated during message send");return}console.warn("Error sending intercepted request:",chrome.runtime.lastError.message)}}),e.status&&void 0!==e.responseBody&&chrome.runtime.sendMessage({type:"DEVTOOLS_REQUEST_COMPLETE",data:{...e,type:"devtools",duration:e.status?Date.now()-e.timestamp:void 0}},e=>{chrome.runtime.lastError&&chrome.runtime.lastError.message&&!chrome.runtime.lastError.message.includes("Extension context invalidated")&&console.debug("DevTools message send failed (panel likely closed):",chrome.runtime.lastError.message)})}catch(e){if(e instanceof Error&&e.message.includes("Extension context invalidated")){console.log("Extension context invalidated");return}console.error("Error sending intercepted request:",e)}}let e=window.fetch;window.fetch=async function(...t){let n;let s=Date.now(),i=r(),[a,l]=t,c="string"==typeof a?a:a.url,d=l?.method||"GET",u={};l?.headers&&(l.headers instanceof Headers?l.headers.forEach((e,t)=>{u[t]=e}):Array.isArray(l.headers)?l.headers.forEach(([e,t])=>{u[e]=t}):Object.entries(l.headers).forEach(([e,t])=>{u[e]=t})),l?.body&&(n="string"==typeof l.body?l.body:l.body instanceof FormData?"[FormData]":l.body instanceof URLSearchParams?l.body.toString():"[Binary Data]");try{let r;let a=await e.apply(this,t),l=a.clone(),p={};a.headers.forEach((e,t)=>{p[t]=e});try{let e=a.headers.get("content-type")||"",t=a.headers.get("content-length");if(e.includes("application/json")||e.includes("text/")||e.includes("application/xml")||e.includes("application/javascript")||e.includes("application/x-www-form-urlencoded")){let t=await l.text();if(e.includes("application/json")&&t)try{let e=JSON.parse(t);r=JSON.stringify(e,null,2)}catch{r=t}else r=t}else if(e.includes("image/")||e.includes("video/")||e.includes("audio/")||e.includes("application/octet-stream"))r=`[${e}] - ${t||"Unknown"} bytes`;else try{let t=await l.text();r=t||`[${e||"Unknown Content Type"}]`}catch{r=`[${e||"Binary Data"}] - ${t||"Unknown"} bytes`}}catch(e){r=`[Error reading response body: ${e instanceof Error?e.message:"Unknown error"}]`}let f={id:i,url:c,method:d,timestamp:s,requestHeaders:u,requestBody:n,responseHeaders:p,responseBody:r,status:a.status,statusText:a.statusText};return o(f),a}catch(t){let e={id:i,url:c,method:d,timestamp:s,requestHeaders:u,requestBody:n,status:0,statusText:t instanceof Error?t.message:"Network Error"};throw o(e),t}};let t=XMLHttpRequest.prototype.open,n=XMLHttpRequest.prototype.send;XMLHttpRequest.prototype.open=function(e,n,...o){return this._interceptorData={id:r(),method:e.toUpperCase(),url:n,timestamp:Date.now()},t.apply(this,[e,n,...o])},XMLHttpRequest.prototype.send=function(e){let t=this._interceptorData;if(!t)return n.apply(this,[e]);e&&("string"==typeof e?t.requestBody=e:e instanceof FormData?t.requestBody="[FormData]":t.requestBody="[Binary Data]"),t.requestHeaders={};let r=this.onreadystatechange;return this.onreadystatechange=function(){if(this.readyState===XMLHttpRequest.DONE){let e;let n={},r=this.getAllResponseHeaders();r&&r.split("\r\n").forEach(e=>{let[t,r]=e.split(": ");t&&r&&(n[t]=r)});try{let t=this.getResponseHeader("content-type")||"",n=this.getResponseHeader("content-length");if(t.includes("application/json")||t.includes("text/")||t.includes("application/xml")||t.includes("application/javascript")||t.includes("application/x-www-form-urlencoded")){let n=this.responseText;if(t.includes("application/json")&&n)try{let t=JSON.parse(n);e=JSON.stringify(t,null,2)}catch{e=n}else e=n}else if(t.includes("image/")||t.includes("video/")||t.includes("audio/")||t.includes("application/octet-stream"))e=`[${t}] - ${n||"Unknown"} bytes`;else try{e=this.responseText||`[${t||"Unknown Content Type"}]`}catch{e=`[${t||"Binary Data"}] - ${n||"Unknown"} bytes`}}catch(t){e=`[Error reading response body: ${t instanceof Error?t.message:"Unknown error"}]`}let s={...t,responseHeaders:n,responseBody:e,status:this.status,statusText:this.statusText};o(s)}r&&r.apply(this)},n.apply(this,[e])},console.log("API Interceptor content script loaded")}else console.log("Extension context not available, content script will not run")},{}]},["4gaGD"],"4gaGD","parcelRequireff35"),globalThis.define=t;