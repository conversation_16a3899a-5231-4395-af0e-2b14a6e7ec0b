var e,t;"function"==typeof(e=globalThis.define)&&(t=e,e=null),function(t,n,o,s,r){var i="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{},a="function"==typeof i[s]&&i[s],c=a.cache||{},l="undefined"!=typeof module&&"function"==typeof module.require&&module.require.bind(module);function d(e,n){if(!c[e]){if(!t[e]){var o="function"==typeof i[s]&&i[s];if(!n&&o)return o(e,!0);if(a)return a(e,!0);if(l&&"string"==typeof e)return l(e);var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}p.resolve=function(n){var o=t[e][1][n];return null!=o?o:n},p.cache={};var u=c[e]=new d.Module(e);t[e][0].call(u.exports,p,u,u.exports,this)}return c[e].exports;function p(e){var t=p.resolve(e);return!1===t?{}:d(t)}}d.isParcelRequire=!0,d.Module=function(e){this.id=e,this.bundle=d,this.exports={}},d.modules=t,d.cache=c,d.parent=a,d.register=function(e,n){t[e]=[function(e,t){t.exports=n},{}]},Object.defineProperty(d,"root",{get:function(){return i[s]}}),i[s]=d;for(var u=0;u<n.length;u++)d(n[u]);if(o){var p=d(o);"object"==typeof exports&&"undefined"!=typeof module?module.exports=p:"function"==typeof e&&e.amd?e(function(){return p}):r&&(this[r]=p)}}({"4gaGD":[function(e,t,n){if("undefined"!=typeof chrome&&chrome.runtime&&chrome.runtime.id){function o(){return`req_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}function s(e){if(!function(){try{return!!(chrome&&chrome.runtime&&chrome.runtime.id)}catch(e){return!1}}()){console.log("Extension context invalidated, skipping message");return}try{chrome.runtime.sendMessage({type:"INTERCEPTED_REQUEST",data:e}).catch(e=>{if(e.message&&e.message.includes("Extension context invalidated")){console.log("Extension context invalidated during message send");return}console.warn("Error sending intercepted request:",e)}),e.status&&void 0!==e.responseBody&&chrome.runtime.sendMessage({type:"DEVTOOLS_REQUEST_COMPLETE",data:{...e,type:"devtools",duration:e.status?Date.now()-e.timestamp:void 0}}).catch(e=>{e&&!e.message?.includes("Extension context invalidated")&&console.debug("DevTools message send failed (panel likely closed):",e.message)})}catch(e){if(e instanceof Error&&e.message.includes("Extension context invalidated")){console.log("Extension context invalidated");return}console.error("Error sending intercepted request:",e)}}let e=window.fetch;window.fetch=async function(...t){let n;let r=Date.now(),i=o(),[a,c]=t,l="string"==typeof a?a:a.url,d=c?.method||"GET",u={};c?.headers&&(c.headers instanceof Headers?c.headers.forEach((e,t)=>{u[t]=e}):Array.isArray(c.headers)?c.headers.forEach(([e,t])=>{u[e]=t}):Object.entries(c.headers).forEach(([e,t])=>{u[e]=t})),c?.body&&(n="string"==typeof c.body?c.body:c.body instanceof FormData?"[FormData]":c.body instanceof URLSearchParams?c.body.toString():"[Binary Data]");try{let o;let a=await e.apply(this,t),c=a.clone(),p={};a.headers.forEach((e,t)=>{p[t]=e});try{let e=a.headers.get("content-type")||"",t=a.headers.get("content-length");if(e.includes("application/json")||e.includes("text/")||e.includes("application/xml")||e.includes("application/javascript")||e.includes("application/x-www-form-urlencoded")){let t=await c.text();if(e.includes("application/json")&&t)try{let e=JSON.parse(t);o=JSON.stringify(e,null,2)}catch{o=t}else o=t}else if(e.includes("image/")||e.includes("video/")||e.includes("audio/")||e.includes("application/octet-stream"))o=`[${e}] - ${t||"Unknown"} bytes`;else try{let t=await c.text();o=t||`[${e||"Unknown Content Type"}]`}catch{o=`[${e||"Binary Data"}] - ${t||"Unknown"} bytes`}}catch(e){o=`[Error reading response body: ${e instanceof Error?e.message:"Unknown error"}]`}let f={id:i,url:l,method:d,timestamp:r,requestHeaders:u,requestBody:n,responseHeaders:p,responseBody:o,status:a.status,statusText:a.statusText};return s(f),a}catch(t){let e={id:i,url:l,method:d,timestamp:r,requestHeaders:u,requestBody:n,status:0,statusText:t instanceof Error?t.message:"Network Error"};throw s(e),t}};let t=XMLHttpRequest.prototype.open,n=XMLHttpRequest.prototype.send;XMLHttpRequest.prototype.open=function(e,n,...s){return this._interceptorData={id:o(),method:e.toUpperCase(),url:n,timestamp:Date.now()},t.apply(this,[e,n,...s])},XMLHttpRequest.prototype.send=function(e){let t=this._interceptorData;if(!t)return n.apply(this,[e]);e&&("string"==typeof e?t.requestBody=e:e instanceof FormData?t.requestBody="[FormData]":t.requestBody="[Binary Data]"),t.requestHeaders={};let o=this.onreadystatechange;return this.onreadystatechange=function(){if(this.readyState===XMLHttpRequest.DONE){let e;let n={},o=this.getAllResponseHeaders();o&&o.split("\r\n").forEach(e=>{let[t,o]=e.split(": ");t&&o&&(n[t]=o)});try{let t=this.getResponseHeader("content-type")||"",n=this.getResponseHeader("content-length");if(t.includes("application/json")||t.includes("text/")||t.includes("application/xml")||t.includes("application/javascript")||t.includes("application/x-www-form-urlencoded")){let n=this.responseText;if(t.includes("application/json")&&n)try{let t=JSON.parse(n);e=JSON.stringify(t,null,2)}catch{e=n}else e=n}else if(t.includes("image/")||t.includes("video/")||t.includes("audio/")||t.includes("application/octet-stream"))e=`[${t}] - ${n||"Unknown"} bytes`;else try{e=this.responseText||`[${t||"Unknown Content Type"}]`}catch{e=`[${t||"Binary Data"}] - ${n||"Unknown"} bytes`}}catch(t){e=`[Error reading response body: ${t instanceof Error?t.message:"Unknown error"}]`}let r={...t,responseHeaders:n,responseBody:e,status:this.status,statusText:this.statusText};s(r)}o&&o.apply(this)},n.apply(this,[e])},console.log("API Interceptor content script loaded")}else console.log("Extension context not available, content script will not run")},{}]},["4gaGD"],"4gaGD","parcelRequireff35"),globalThis.define=t;