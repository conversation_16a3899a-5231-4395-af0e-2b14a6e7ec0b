var e,t;"function"==typeof(e=globalThis.define)&&(t=e,e=null),function(t,n,o,r,i){var s="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{},a="function"==typeof s[r]&&s[r],c=a.cache||{},d="undefined"!=typeof module&&"function"==typeof module.require&&module.require.bind(module);function u(e,n){if(!c[e]){if(!t[e]){var o="function"==typeof s[r]&&s[r];if(!n&&o)return o(e,!0);if(a)return a(e,!0);if(d&&"string"==typeof e)return d(e);var i=Error("Cannot find module '"+e+"'");throw i.code="MODULE_NOT_FOUND",i}p.resolve=function(n){var o=t[e][1][n];return null!=o?o:n},p.cache={};var l=c[e]=new u.Module(e);t[e][0].call(l.exports,p,l,l.exports,this)}return c[e].exports;function p(e){var t=p.resolve(e);return!1===t?{}:u(t)}}u.isParcelRequire=!0,u.Module=function(e){this.id=e,this.bundle=u,this.exports={}},u.modules=t,u.cache=c,u.parent=a,u.register=function(e,n){t[e]=[function(e,t){t.exports=n},{}]},Object.defineProperty(u,"root",{get:function(){return s[r]}}),s[r]=u;for(var l=0;l<n.length;l++)u(n[l]);if(o){var p=u(o);"object"==typeof exports&&"undefined"!=typeof module?module.exports=p:"function"==typeof e&&e.amd?e(function(){return p}):i&&(this[i]=p)}}({"4gaGD":[function(e,t,n){if("undefined"!=typeof chrome&&chrome.runtime&&chrome.runtime.id){function o(){return`req_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}function r(e){if(!function(){try{return!!(chrome&&chrome.runtime&&chrome.runtime.id)}catch(e){return!1}}()){console.log("Extension context invalidated, skipping message");return}try{chrome.runtime.sendMessage({type:"INTERCEPTED_REQUEST",data:e}).catch(e=>{if(e.message&&e.message.includes("Extension context invalidated")){console.log("Extension context invalidated during message send");return}console.warn("Error sending intercepted request:",e)})}catch(e){if(e instanceof Error&&e.message.includes("Extension context invalidated")){console.log("Extension context invalidated");return}console.error("Error sending intercepted request:",e)}}let e=window.fetch;window.fetch=async function(...t){let n;let i=Date.now(),s=o(),[a,c]=t,d="string"==typeof a?a:a.url,u=c?.method||"GET",l={};c?.headers&&(c.headers instanceof Headers?c.headers.forEach((e,t)=>{l[t]=e}):Array.isArray(c.headers)?c.headers.forEach(([e,t])=>{l[e]=t}):Object.entries(c.headers).forEach(([e,t])=>{l[e]=t})),c?.body&&(n="string"==typeof c.body?c.body:c.body instanceof FormData?"[FormData]":c.body instanceof URLSearchParams?c.body.toString():"[Binary Data]");try{let o;let a=await e.apply(this,t),c=a.clone(),p={};a.headers.forEach((e,t)=>{p[t]=e});try{let e=a.headers.get("content-type")||"";o=e.includes("application/json")||e.includes("text/")||e.includes("application/xml")?await c.text():`[${e||"Binary Data"}]`}catch(e){o="[Error reading response body]"}let f={id:s,url:d,method:u,timestamp:i,requestHeaders:l,requestBody:n,responseHeaders:p,responseBody:o,status:a.status,statusText:a.statusText};return r(f),a}catch(t){let e={id:s,url:d,method:u,timestamp:i,requestHeaders:l,requestBody:n,status:0,statusText:t instanceof Error?t.message:"Network Error"};throw r(e),t}};let t=XMLHttpRequest.prototype.open,n=XMLHttpRequest.prototype.send;XMLHttpRequest.prototype.open=function(e,n,...r){return this._interceptorData={id:o(),method:e.toUpperCase(),url:n,timestamp:Date.now()},t.apply(this,[e,n,...r])},XMLHttpRequest.prototype.send=function(e){let t=this._interceptorData;if(!t)return n.apply(this,[e]);e&&("string"==typeof e?t.requestBody=e:e instanceof FormData?t.requestBody="[FormData]":t.requestBody="[Binary Data]"),t.requestHeaders={};let o=this.onreadystatechange;return this.onreadystatechange=function(){if(this.readyState===XMLHttpRequest.DONE){let e;let n={},o=this.getAllResponseHeaders();o&&o.split("\r\n").forEach(e=>{let[t,o]=e.split(": ");t&&o&&(n[t]=o)});try{let t=this.getResponseHeader("content-type")||"";e=t.includes("application/json")||t.includes("text/")||t.includes("application/xml")?this.responseText:`[${t||"Binary Data"}]`}catch(t){e="[Error reading response body]"}let i={...t,responseHeaders:n,responseBody:e,status:this.status,statusText:this.statusText};r(i)}o&&o.apply(this)},n.apply(this,[e])},console.log("API Interceptor content script loaded")}else console.log("Extension context not available, content script will not run")},{}]},["4gaGD"],"4gaGD","parcelRequireff35"),globalThis.define=t;