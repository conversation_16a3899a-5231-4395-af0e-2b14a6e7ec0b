var e,t;"function"==typeof(e=globalThis.define)&&(t=e,e=null),function(t,r,s,o,a){var n="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{},i="function"==typeof n[o]&&n[o],u=i.cache||{},c="undefined"!=typeof module&&"function"==typeof module.require&&module.require.bind(module);function l(e,r){if(!u[e]){if(!t[e]){var s="function"==typeof n[o]&&n[o];if(!r&&s)return s(e,!0);if(i)return i(e,!0);if(c&&"string"==typeof e)return c(e);var a=Error("Cannot find module '"+e+"'");throw a.code="MODULE_NOT_FOUND",a}g.resolve=function(r){var s=t[e][1][r];return null!=s?s:r},g.cache={};var d=u[e]=new l.Module(e);t[e][0].call(d.exports,g,d,d.exports,this)}return u[e].exports;function g(e){var t=g.resolve(e);return!1===t?{}:l(t)}}l.isParcelRequire=!0,l.Module=function(e){this.id=e,this.bundle=l,this.exports={}},l.modules=t,l.cache=u,l.parent=i,l.register=function(e,r){t[e]=[function(e,t){t.exports=r},{}]},Object.defineProperty(l,"root",{get:function(){return n[o]}}),n[o]=l;for(var d=0;d<r.length;d++)l(r[d]);if(s){var g=l(s);"object"==typeof exports&&"undefined"!=typeof module?module.exports=g:"function"==typeof e&&e.amd?e(function(){return g}):a&&(this[a]=g)}}({kgW6q:[function(e,t,r){e("../../../background")},{"../../../background":"8VaxY"}],"8VaxY":[function(e,t,r){var s=e("./utils/storage");console.log("API Interceptor background script starting...");let o=new Map,a=!1;async function n(e){if(a)try{await (0,s.StorageManager).storeRequest(e);try{chrome.runtime.sendMessage({type:"NEW_REQUEST",data:e}).catch(e=>{e&&!e.message?.includes("Extension context invalidated")&&console.debug("Message send failed (popup likely closed):",e.message)})}catch(e){console.debug("Failed to send message to popup:",e)}if("devtools"===e.type||"fetch"===e.type||"xmlhttprequest"===e.type)try{chrome.runtime.sendMessage({type:"DEVTOOLS_REQUEST",data:e}).catch(e=>{e&&!e.message?.includes("Extension context invalidated")&&console.debug("DevTools message send failed (panel likely closed):",e.message)})}catch(e){console.debug("Failed to send message to DevTools panel:",e)}}catch(e){console.error("Error storing request data:",e)}}async function i(e){try{let t;console.log("Background: Replaying request to",e.url);let r={};e.headers&&e.headers.forEach(e=>{["host","user-agent","accept-encoding","connection","upgrade-insecure-requests","sec-fetch-site","sec-fetch-mode","sec-fetch-dest","sec-ch-ua","sec-ch-ua-mobile","sec-ch-ua-platform","origin","referer","content-length"].includes(e.name.toLowerCase())||(r[e.name]=e.value||"")});let s={method:e.method,headers:r,mode:"cors",credentials:"omit"};e.body&&["POST","PUT","PATCH"].includes(e.method)&&(s.body=e.body),console.log("Background: Fetch options:",s);let o=await fetch(e.url,s),a={status:o.status,statusText:o.statusText,headers:Object.fromEntries(o.headers.entries())},n=o.headers.get("content-type")||"";if(n.includes("application/json"))try{let e=await o.json();t=JSON.stringify(e,null,2)}catch(e){t=await o.text()}else t=n.includes("text/")||n.includes("application/xml")?await o.text():`[${n||"Binary Data"}] - ${o.headers.get("content-length")||"Unknown"} bytes`;return{success:!0,responseInfo:a,responseBody:`Status: ${o.status} ${o.statusText}

${t}`}}catch(e){throw console.error("Background: Error replaying request:",e),e}}chrome.webRequest.onBeforeRequest.addListener(e=>{let t=`req_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,r="xmlhttprequest"===e.type||"fetch"===e.type||"main_frame"===e.type||"sub_frame"===e.type||e.url.includes("/api/")||e.url.includes(".json")||e.url.includes("/graphql")||"GET"!==e.method,s={id:t,url:e.url,method:e.method,timestamp:Date.now(),requestHeaders:[],requestBody:function(e){if(e.requestBody){if(e.requestBody.formData)return JSON.stringify(e.requestBody.formData);if(e.requestBody.raw){let t=new TextDecoder;return e.requestBody.raw.map(e=>t.decode(e.bytes)).join("")}}}(e),type:r?"devtools":e.type,tabId:e.tabId};o.set(e.requestId,s)},{urls:["<all_urls>"]},["requestBody"]),chrome.webRequest.onBeforeSendHeaders.addListener(e=>{let t=o.get(e.requestId);t&&(t.requestHeaders=e.requestHeaders||[])},{urls:["<all_urls>"]},["requestHeaders"]),chrome.webRequest.onHeadersReceived.addListener(e=>{let t=o.get(e.requestId);t&&(t.responseHeaders=e.responseHeaders||[],t.status=e.statusCode,t.statusText=e.statusLine)},{urls:["<all_urls>"]},["responseHeaders"]),chrome.webRequest.onCompleted.addListener(e=>{let t=o.get(e.requestId);t&&(t.duration=Date.now()-t.timestamp,t.status=e.statusCode,t.statusText=e.statusLine,n(t),o.delete(e.requestId))},{urls:["<all_urls>"]}),chrome.webRequest.onErrorOccurred.addListener(e=>{let t=o.get(e.requestId);t&&(t.duration=Date.now()-t.timestamp,t.status=0,t.statusText=e.error,n(t),o.delete(e.requestId))},{urls:["<all_urls>"]}),chrome.runtime.onMessage.addListener((e,t,r)=>{try{if("GET_REQUESTS"===e.type)return(0,s.StorageManager).getRequests().then(e=>{r({requests:e})}),!0;if("CLEAR_REQUESTS"===e.type)return(0,s.StorageManager).clearRequests().then(()=>{r({success:!0})}),!0;if("EXPORT_REQUESTS"===e.type)return(0,s.StorageManager).exportRequests().then(e=>{r({data:e})}).catch(e=>{r({error:e.message})}),!0;if("IMPORT_REQUESTS"===e.type)return(0,s.StorageManager).importRequests(e.data).then(e=>{r({success:!0,importedCount:e})}).catch(e=>{r({error:e.message})}),!0;if("GET_STORAGE_STATS"===e.type)return(0,s.StorageManager).getStorageStats().then(e=>{r({stats:e})}),!0;if("GET_CAPTURE_STATUS"===e.type)return r({isCapturing:a}),!0;if("SET_CAPTURE_STATUS"===e.type)return a=e.isCapturing,r({success:!0}),!0;if("DEVTOOLS_REQUEST"===e.type){let t=e.data;return n(t),!0}if("DEVTOOLS_REQUEST_COMPLETE"===e.type){let t=e.data;n(t);try{chrome.runtime.sendMessage({type:"DEVTOOLS_REQUEST_COMPLETE",data:t}).catch(e=>{e&&!e.message?.includes("Extension context invalidated")&&console.debug("DevTools message send failed (panel likely closed):",e.message)})}catch(e){console.debug("Failed to send message to DevTools panel:",e)}return!0}if("STORE_DEVTOOLS_REQUEST"===e.type)return n(e.data),!0;if("CLEAR_DEVTOOLS_REQUESTS"===e.type)return(0,s.StorageManager).clearRequests().then(()=>{r({success:!0})}),!0;if("GET_DEVTOOLS_REQUESTS"===e.type)return(0,s.StorageManager).getRequests().then(e=>{let t=e.filter(e=>"devtools"===e.type||"fetch"===e.type||"xmlhttprequest"===e.type);r({requests:t})}),!0;if("INTERCEPTED_REQUEST"===e.type){let s=e.data,o={id:s.id,url:s.url,method:s.method,timestamp:s.timestamp,requestHeaders:Object.entries(s.requestHeaders||{}).map(([e,t])=>({name:e,value:t})),requestBody:s.requestBody,responseHeaders:Object.entries(s.responseHeaders||{}).map(([e,t])=>({name:e,value:t})),responseBody:s.responseBody,status:s.status,statusText:s.statusText,type:"fetch",tabId:t.tab?.id||-1,duration:s.status?Date.now()-s.timestamp:void 0};return n(o),r({success:!0}),!0}if("REPLAY_REQUEST"===e.type)return i(e.requestData).then(e=>r(e)).catch(e=>r({error:e.message})),!0}catch(e){console.error("Error handling message:",e);try{r({error:"Internal error processing message"})}catch(e){console.error("Error sending error response:",e)}return!1}}),chrome.runtime.onStartup.addListener(()=>{console.log("Extension startup detected")}),chrome.runtime.onSuspend.addListener(()=>{console.log("Extension is being suspended"),console.log("Extension context has been invalidated - this is normal during development")}),chrome.runtime.onInstalled.addListener(e=>{console.log("Extension installed/updated:",e.reason),"install"===e.reason?console.log("First time installation"):"update"===e.reason&&console.log("Extension updated from version:",e.previousVersion)}),console.log("API Interceptor background script loaded and ready")},{"./utils/storage":"fmp01"}],fmp01:[function(e,t,r){var s=e("@parcel/transformer-js/src/esmodule-helpers.js");s.defineInteropFlag(r),s.export(r,"StorageManager",()=>StorageManager);class StorageManager{static{this.STORAGE_KEY="apiRequests"}static{this.MAX_REQUESTS=1e3}static{this.SETTINGS_KEY="apiInterceptorSettings"}static async getRequests(){try{let e=await chrome.storage.local.get([this.STORAGE_KEY]);return e[this.STORAGE_KEY]||[]}catch(e){return console.error("Error getting requests from storage:",e),[]}}static async storeRequest(e){try{let t=await this.getRequests(),r=[e,...t].slice(0,this.MAX_REQUESTS);await chrome.storage.local.set({[this.STORAGE_KEY]:r})}catch(e){console.error("Error storing request:",e)}}static async clearRequests(){try{await chrome.storage.local.set({[this.STORAGE_KEY]:[]})}catch(e){console.error("Error clearing requests:",e)}}static async getStorageStats(){try{let e=await chrome.storage.local.getBytesInUse(),t=chrome.storage.local.QUOTA_BYTES;return{used:e,total:t}}catch(e){return console.error("Error getting storage stats:",e),{used:0,total:5242880}}}static async exportRequests(){try{let e=await this.getRequests();return{version:"1.0",timestamp:new Date().toISOString(),requestCount:e.length,requests:e}}catch(e){throw console.error("Error exporting requests:",e),e}}static async importRequests(e){try{if(!e.requests||!Array.isArray(e.requests))throw Error("Invalid import data format");let t=await this.getRequests(),r=e.requests,s=[...r,...t],o=s.filter((e,t,r)=>t===r.findIndex(t=>t.id===e.id)).slice(0,this.MAX_REQUESTS);return await chrome.storage.local.set({[this.STORAGE_KEY]:o}),r.length}catch(e){throw console.error("Error importing requests:",e),e}}static async getSettings(){try{let e=await chrome.storage.local.get([this.SETTINGS_KEY]);return{maxRequests:this.MAX_REQUESTS,autoCapture:!0,captureResponseBody:!1,filterDomains:[],...e[this.SETTINGS_KEY]}}catch(e){return console.error("Error getting settings:",e),{maxRequests:this.MAX_REQUESTS,autoCapture:!0,captureResponseBody:!1,filterDomains:[]}}}static async saveSettings(e){try{let t=await this.getSettings(),r={...t,...e};await chrome.storage.local.set({[this.SETTINGS_KEY]:r})}catch(e){console.error("Error saving settings:",e)}}static async cleanOldRequests(e=7){try{let t=await this.getRequests(),r=Date.now()-864e5*e,s=t.filter(e=>e.timestamp>r);return await chrome.storage.local.set({[this.STORAGE_KEY]:s}),t.length-s.length}catch(e){return console.error("Error cleaning old requests:",e),0}}static async searchRequests(e){try{let t=await this.getRequests(),r=e.toLowerCase();return t.filter(e=>e.url.toLowerCase().includes(r)||e.method.toLowerCase().includes(r)||(e.status?.toString()||"").includes(r)||e.type.toLowerCase().includes(r))}catch(e){return console.error("Error searching requests:",e),[]}}static async getRequestsByDomain(e){try{let t=await this.getRequests();return t.filter(t=>{try{let r=new URL(t.url);return r.hostname===e}catch{return!1}})}catch(e){return console.error("Error getting requests by domain:",e),[]}}static async getUniqueDomains(){try{let e=await this.getRequests(),t=new Set;return e.forEach(e=>{try{let r=new URL(e.url);t.add(r.hostname)}catch{}}),Array.from(t).sort()}catch(e){return console.error("Error getting unique domains:",e),[]}}}},{"@parcel/transformer-js/src/esmodule-helpers.js":"hbR2Q"}],hbR2Q:[function(e,t,r){r.interopDefault=function(e){return e&&e.__esModule?e:{default:e}},r.defineInteropFlag=function(e){Object.defineProperty(e,"__esModule",{value:!0})},r.exportAll=function(e,t){return Object.keys(e).forEach(function(r){"default"===r||"__esModule"===r||t.hasOwnProperty(r)||Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[r]}})}),t},r.export=function(e,t,r){Object.defineProperty(e,t,{enumerable:!0,get:r})}},{}]},["kgW6q"],"kgW6q","parcelRequireff35"),globalThis.define=t;