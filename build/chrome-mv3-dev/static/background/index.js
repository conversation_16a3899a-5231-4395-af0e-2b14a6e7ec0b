(function(define){var __define; typeof define === "function" && (__define=define,define=null);
// modules are defined as an array
// [ module function, map of requires ]
//
// map of requires is short require name -> numeric require
//
// anything defined in a previous bundle is accessed via the
// orig method which is the require for previous bundles

(function (modules, entry, mainEntry, parcelRequireName, globalName) {
  /* eslint-disable no-undef */
  var globalObject =
    typeof globalThis !== 'undefined'
      ? globalThis
      : typeof self !== 'undefined'
      ? self
      : typeof window !== 'undefined'
      ? window
      : typeof global !== 'undefined'
      ? global
      : {};
  /* eslint-enable no-undef */

  // Save the require from previous bundle to this closure if any
  var previousRequire =
    typeof globalObject[parcelRequireName] === 'function' &&
    globalObject[parcelRequireName];

  var cache = previousRequire.cache || {};
  // Do not use `require` to prevent Webpack from trying to bundle this call
  var nodeRequire =
    typeof module !== 'undefined' &&
    typeof module.require === 'function' &&
    module.require.bind(module);

  function newRequire(name, jumped) {
    if (!cache[name]) {
      if (!modules[name]) {
        // if we cannot find the module within our internal map or
        // cache jump to the current global require ie. the last bundle
        // that was added to the page.
        var currentRequire =
          typeof globalObject[parcelRequireName] === 'function' &&
          globalObject[parcelRequireName];
        if (!jumped && currentRequire) {
          return currentRequire(name, true);
        }

        // If there are other bundles on this page the require from the
        // previous one is saved to 'previousRequire'. Repeat this as
        // many times as there are bundles until the module is found or
        // we exhaust the require chain.
        if (previousRequire) {
          return previousRequire(name, true);
        }

        // Try the node require function if it exists.
        if (nodeRequire && typeof name === 'string') {
          return nodeRequire(name);
        }

        var err = new Error("Cannot find module '" + name + "'");
        err.code = 'MODULE_NOT_FOUND';
        throw err;
      }

      localRequire.resolve = resolve;
      localRequire.cache = {};

      var module = (cache[name] = new newRequire.Module(name));

      modules[name][0].call(
        module.exports,
        localRequire,
        module,
        module.exports,
        this
      );
    }

    return cache[name].exports;

    function localRequire(x) {
      var res = localRequire.resolve(x);
      return res === false ? {} : newRequire(res);
    }

    function resolve(x) {
      var id = modules[name][1][x];
      return id != null ? id : x;
    }
  }

  function Module(moduleName) {
    this.id = moduleName;
    this.bundle = newRequire;
    this.exports = {};
  }

  newRequire.isParcelRequire = true;
  newRequire.Module = Module;
  newRequire.modules = modules;
  newRequire.cache = cache;
  newRequire.parent = previousRequire;
  newRequire.register = function (id, exports) {
    modules[id] = [
      function (require, module) {
        module.exports = exports;
      },
      {},
    ];
  };

  Object.defineProperty(newRequire, 'root', {
    get: function () {
      return globalObject[parcelRequireName];
    },
  });

  globalObject[parcelRequireName] = newRequire;

  for (var i = 0; i < entry.length; i++) {
    newRequire(entry[i]);
  }

  if (mainEntry) {
    // Expose entry point to Node, AMD or browser globals
    // Based on https://github.com/ForbesLindesay/umd/blob/master/template.js
    var mainExports = newRequire(mainEntry);

    // CommonJS
    if (typeof exports === 'object' && typeof module !== 'undefined') {
      module.exports = mainExports;

      // RequireJS
    } else if (typeof define === 'function' && define.amd) {
      define(function () {
        return mainExports;
      });

      // <script>
    } else if (globalName) {
      this[globalName] = mainExports;
    }
  }
})({"ljBRi":[function(require,module,exports) {
var u = globalThis.process?.argv || [];
var h = ()=>globalThis.process?.env || {};
var B = new Set(u), _ = (e)=>B.has(e), G = u.filter((e)=>e.startsWith("--") && e.includes("=")).map((e)=>e.split("=")).reduce((e, [t, o])=>(e[t] = o, e), {});
var U = _("--dry-run"), g = ()=>_("--verbose") || h().VERBOSE === "true", N = g();
var m = (e = "", ...t)=>console.log(e.padEnd(9), "|", ...t);
var y = (...e)=>console.error("\uD83D\uDD34 ERROR".padEnd(9), "|", ...e), v = (...e)=>m("\uD83D\uDD35 INFO", ...e), f = (...e)=>m("\uD83D\uDFE0 WARN", ...e), M = 0, i = (...e)=>g() && m(`\u{1F7E1} ${M++}`, ...e);
var b = ()=>{
    let e = globalThis.browser?.runtime || globalThis.chrome?.runtime, t = ()=>setInterval(e.getPlatformInfo, 24e3);
    e.onStartup.addListener(t), t();
};
var n = {
    "isContentScript": false,
    "isBackground": true,
    "isReact": false,
    "runtimes": [
        "background-service-runtime"
    ],
    "host": "localhost",
    "port": 1815,
    "entryFilePath": "/Users/<USER>/Projects/api-interceptor-extension/.plasmo/static/background/index.ts",
    "bundleId": "c338908e704c91f1",
    "envHash": "d99a5ffa57acd638",
    "verbose": "false",
    "secure": false,
    "serverPort": 55971
};
module.bundle.HMR_BUNDLE_ID = n.bundleId;
globalThis.process = {
    argv: [],
    env: {
        VERBOSE: n.verbose
    }
};
var D = module.bundle.Module;
function H(e) {
    D.call(this, e), this.hot = {
        data: module.bundle.hotData[e],
        _acceptCallbacks: [],
        _disposeCallbacks: [],
        accept: function(t) {
            this._acceptCallbacks.push(t || function() {});
        },
        dispose: function(t) {
            this._disposeCallbacks.push(t);
        }
    }, module.bundle.hotData[e] = void 0;
}
module.bundle.Module = H;
module.bundle.hotData = {};
var c = globalThis.browser || globalThis.chrome || null;
function R() {
    return !n.host || n.host === "0.0.0.0" ? location.protocol.indexOf("http") === 0 ? location.hostname : "localhost" : n.host;
}
function x() {
    return !n.host || n.host === "0.0.0.0" ? "localhost" : n.host;
}
function d() {
    return n.port || location.port;
}
var P = "__plasmo_runtime_page_", S = "__plasmo_runtime_script_";
var O = `${n.secure ? "https" : "http"}://${R()}:${d()}/`;
async function k(e = 1470) {
    for(;;)try {
        await fetch(O);
        break;
    } catch  {
        await new Promise((o)=>setTimeout(o, e));
    }
}
if (c.runtime.getManifest().manifest_version === 3) {
    let e = c.runtime.getURL("/__plasmo_hmr_proxy__?url=");
    globalThis.addEventListener("fetch", function(t) {
        let o = t.request.url;
        if (o.startsWith(e)) {
            let s = new URL(decodeURIComponent(o.slice(e.length)));
            s.hostname === n.host && s.port === `${n.port}` ? (s.searchParams.set("t", Date.now().toString()), t.respondWith(fetch(s).then((r)=>new Response(r.body, {
                    headers: {
                        "Content-Type": r.headers.get("Content-Type") ?? "text/javascript"
                    }
                })))) : t.respondWith(new Response("Plasmo HMR", {
                status: 200,
                statusText: "Testing"
            }));
        }
    });
}
function E(e, t) {
    let { modules: o } = e;
    return o ? !!o[t] : !1;
}
function C(e = d()) {
    let t = x();
    return `${n.secure || location.protocol === "https:" && !/localhost|127.0.0.1|0.0.0.0/.test(t) ? "wss" : "ws"}://${t}:${e}/`;
}
function L(e) {
    typeof e.message == "string" && y("[plasmo/parcel-runtime]: " + e.message);
}
function T(e) {
    if (typeof globalThis.WebSocket > "u") return;
    let t = new WebSocket(C(Number(d()) + 1));
    return t.addEventListener("message", async function(o) {
        let s = JSON.parse(o.data);
        await e(s);
    }), t.addEventListener("error", L), t;
}
function A(e) {
    if (typeof globalThis.WebSocket > "u") return;
    let t = new WebSocket(C());
    return t.addEventListener("message", async function(o) {
        let s = JSON.parse(o.data);
        if (s.type === "update" && await e(s.assets), s.type === "error") for (let r of s.diagnostics.ansi){
            let l = r.codeframe || r.stack;
            f("[plasmo/parcel-runtime]: " + r.message + `
` + l + `

` + r.hints.join(`
`));
        }
    }), t.addEventListener("error", L), t.addEventListener("open", ()=>{
        v(`[plasmo/parcel-runtime]: Connected to HMR server for ${n.entryFilePath}`);
    }), t.addEventListener("close", ()=>{
        f(`[plasmo/parcel-runtime]: Connection to the HMR server is closed for ${n.entryFilePath}`);
    }), t;
}
var w = module.bundle.parent, a = {
    buildReady: !1,
    bgChanged: !1,
    csChanged: !1,
    pageChanged: !1,
    scriptPorts: new Set,
    pagePorts: new Set
};
async function p(e = !1) {
    if (e || a.buildReady && a.pageChanged) {
        i("BGSW Runtime - reloading Page");
        for (let t of a.pagePorts)t.postMessage(null);
    }
    if (e || a.buildReady && (a.bgChanged || a.csChanged)) {
        i("BGSW Runtime - reloading CS");
        let t = await c?.tabs.query({
            active: !0
        });
        for (let o of a.scriptPorts){
            let s = t.some((r)=>r.id === o.sender.tab?.id);
            o.postMessage({
                __plasmo_cs_active_tab__: s
            });
        }
        c.runtime.reload();
    }
}
if (!w || !w.isParcelRequire) {
    b();
    let e = A(async (t)=>{
        i("BGSW Runtime - On HMR Update"), a.bgChanged ||= t.filter((s)=>s.envHash === n.envHash).some((s)=>E(module.bundle, s.id));
        let o = t.find((s)=>s.type === "json");
        if (o) {
            let s = new Set(t.map((l)=>l.id)), r = Object.values(o.depsByBundle).map((l)=>Object.values(l)).flat();
            a.bgChanged ||= r.every((l)=>s.has(l));
        }
        p();
    });
    e.addEventListener("open", ()=>{
        let t = setInterval(()=>e.send("ping"), 24e3);
        e.addEventListener("close", ()=>clearInterval(t));
    }), e.addEventListener("close", async ()=>{
        await k(), p(!0);
    });
}
T(async (e)=>{
    switch(i("BGSW Runtime - On Build Repackaged"), e.type){
        case "build_ready":
            a.buildReady ||= !0, p();
            break;
        case "cs_changed":
            a.csChanged ||= !0, p();
            break;
    }
});
c.runtime.onConnect.addListener(function(e) {
    let t = e.name.startsWith(P), o = e.name.startsWith(S);
    if (t || o) {
        let s = t ? a.pagePorts : a.scriptPorts;
        s.add(e), e.onDisconnect.addListener(()=>{
            s.delete(e);
        }), e.onMessage.addListener(function(r) {
            i("BGSW Runtime - On source changed", r), r.__plasmo_cs_changed__ && (a.csChanged ||= !0), r.__plasmo_page_changed__ && (a.pageChanged ||= !0), p();
        });
    }
});
c.runtime.onMessage.addListener(function(t) {
    return t.__plasmo_full_reload__ && (i("BGSW Runtime - On top-level code changed"), p()), !0;
});

},{}],"8oeFb":[function(require,module,exports) {
var _background = require("../../../background");

},{"../../../background":"14rpM"}],"14rpM":[function(require,module,exports) {
// API Interceptor Background Script
// This script intercepts all network requests and stores them for display
var _storage = require("./utils/storage");
// Log background script startup
console.log("API Interceptor background script starting...");
// Utility function to check if extension context is valid
function isExtensionContextValid() {
    try {
        return !!(chrome && chrome.runtime && chrome.runtime.id);
    } catch (error) {
        return false;
    }
}
// Handle extension context invalidation gracefully
function handleContextInvalidation() {
    console.log("Extension context has been invalidated - this is normal during development");
// Clear any intervals or timeouts if needed
// The extension will be reloaded automatically
}
// Store for ongoing requests
const pendingRequests = new Map();
// Capture status - default to false, user needs to click start
let isCapturing = false;
// Generate unique request ID
function generateRequestId() {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}
// Get request body from details
function getRequestBody(details) {
    if (!details.requestBody) return undefined;
    if (details.requestBody.formData) return JSON.stringify(details.requestBody.formData);
    if (details.requestBody.raw) {
        const decoder = new TextDecoder();
        return details.requestBody.raw.map((data)=>decoder.decode(data.bytes)).join("");
    }
    return undefined;
}
// Store request data
async function storeRequestData(requestData) {
    // Only store if capturing is enabled
    if (!isCapturing) return;
    try {
        await (0, _storage.StorageManager).storeRequest(requestData);
        // Notify popup if it's open
        try {
            chrome.runtime.sendMessage({
                type: "NEW_REQUEST",
                data: requestData
            }).catch((error)=>{
                // Ignore errors if popup is not open or extension context is invalid
                if (error && !error.message?.includes("Extension context invalidated")) console.debug("Message send failed (popup likely closed):", error.message);
            });
        } catch (error) {
            // Handle synchronous errors
            console.debug("Failed to send message to popup:", error);
        }
    } catch (error) {
        console.error("Error storing request data:", error);
    }
}
// Listen for request start
chrome.webRequest.onBeforeRequest.addListener((details)=>{
    const requestId = generateRequestId();
    const requestData = {
        id: requestId,
        url: details.url,
        method: details.method,
        timestamp: Date.now(),
        requestHeaders: [],
        requestBody: getRequestBody(details),
        type: details.type,
        tabId: details.tabId
    };
    pendingRequests.set(details.requestId, requestData);
}, {
    urls: [
        "<all_urls>"
    ]
}, [
    "requestBody"
]);
// Listen for request headers
chrome.webRequest.onBeforeSendHeaders.addListener((details)=>{
    const requestData = pendingRequests.get(details.requestId);
    if (requestData) requestData.requestHeaders = details.requestHeaders || [];
}, {
    urls: [
        "<all_urls>"
    ]
}, [
    "requestHeaders"
]);
// Listen for response headers
chrome.webRequest.onHeadersReceived.addListener((details)=>{
    const requestData = pendingRequests.get(details.requestId);
    if (requestData) {
        requestData.responseHeaders = details.responseHeaders || [];
        requestData.status = details.statusCode;
        requestData.statusText = details.statusLine;
    }
}, {
    urls: [
        "<all_urls>"
    ]
}, [
    "responseHeaders"
]);
// Listen for request completion
chrome.webRequest.onCompleted.addListener((details)=>{
    const requestData = pendingRequests.get(details.requestId);
    if (requestData) {
        requestData.duration = Date.now() - requestData.timestamp;
        requestData.status = details.statusCode;
        requestData.statusText = details.statusLine;
        // Store the completed request
        storeRequestData(requestData);
        // Clean up
        pendingRequests.delete(details.requestId);
    }
}, {
    urls: [
        "<all_urls>"
    ]
});
// Listen for request errors
chrome.webRequest.onErrorOccurred.addListener((details)=>{
    const requestData = pendingRequests.get(details.requestId);
    if (requestData) {
        requestData.duration = Date.now() - requestData.timestamp;
        requestData.status = 0;
        requestData.statusText = details.error;
        // Store the failed request
        storeRequestData(requestData);
        // Clean up
        pendingRequests.delete(details.requestId);
    }
}, {
    urls: [
        "<all_urls>"
    ]
});
// Handle messages from popup and content scripts
chrome.runtime.onMessage.addListener((message, sender, sendResponse)=>{
    try {
        if (message.type === "GET_REQUESTS") {
            (0, _storage.StorageManager).getRequests().then((requests)=>{
                sendResponse({
                    requests
                });
            });
            return true; // Keep message channel open for async response
        }
        if (message.type === "CLEAR_REQUESTS") {
            (0, _storage.StorageManager).clearRequests().then(()=>{
                sendResponse({
                    success: true
                });
            });
            return true;
        }
        if (message.type === "EXPORT_REQUESTS") {
            (0, _storage.StorageManager).exportRequests().then((data)=>{
                sendResponse({
                    data
                });
            }).catch((error)=>{
                sendResponse({
                    error: error.message
                });
            });
            return true;
        }
        if (message.type === "IMPORT_REQUESTS") {
            (0, _storage.StorageManager).importRequests(message.data).then((count)=>{
                sendResponse({
                    success: true,
                    importedCount: count
                });
            }).catch((error)=>{
                sendResponse({
                    error: error.message
                });
            });
            return true;
        }
        if (message.type === "GET_STORAGE_STATS") {
            (0, _storage.StorageManager).getStorageStats().then((stats)=>{
                sendResponse({
                    stats
                });
            });
            return true;
        }
        // Handle capture status
        if (message.type === "GET_CAPTURE_STATUS") {
            sendResponse({
                isCapturing
            });
            return true;
        }
        if (message.type === "SET_CAPTURE_STATUS") {
            isCapturing = message.isCapturing;
            sendResponse({
                success: true
            });
            return true;
        }
        // Handle DevTools requests
        if (message.type === "DEVTOOLS_REQUEST") {
            const devtoolsData = message.data;
            storeRequestData(devtoolsData);
            return true;
        }
        if (message.type === "DEVTOOLS_REQUEST_COMPLETE") {
            const devtoolsData = message.data;
            storeRequestData(devtoolsData);
            // Forward to all DevTools panels
            try {
                chrome.runtime.sendMessage({
                    type: "DEVTOOLS_REQUEST_COMPLETE",
                    data: devtoolsData
                }).catch((error)=>{
                    // Ignore errors if no panels are listening or extension context is invalid
                    if (error && !error.message?.includes("Extension context invalidated")) console.debug("DevTools message send failed (panel likely closed):", error.message);
                });
            } catch (error) {
                console.debug("Failed to send message to DevTools panel:", error);
            }
            return true;
        }
        if (message.type === "STORE_DEVTOOLS_REQUEST") {
            storeRequestData(message.data);
            return true;
        }
        if (message.type === "CLEAR_DEVTOOLS_REQUESTS") {
            (0, _storage.StorageManager).clearRequests().then(()=>{
                sendResponse({
                    success: true
                });
            });
            return true;
        }
        // Handle intercepted requests from content script
        if (message.type === "INTERCEPTED_REQUEST") {
            const interceptedData = message.data;
            // Convert to our RequestData format
            const requestData = {
                id: interceptedData.id,
                url: interceptedData.url,
                method: interceptedData.method,
                timestamp: interceptedData.timestamp,
                requestHeaders: Object.entries(interceptedData.requestHeaders || {}).map(([name, value])=>({
                        name,
                        value
                    })),
                requestBody: interceptedData.requestBody,
                responseHeaders: Object.entries(interceptedData.responseHeaders || {}).map(([name, value])=>({
                        name,
                        value
                    })),
                responseBody: interceptedData.responseBody,
                status: interceptedData.status,
                statusText: interceptedData.statusText,
                type: "fetch",
                tabId: sender.tab?.id || -1,
                duration: interceptedData.status ? Date.now() - interceptedData.timestamp : undefined
            };
            // Store the intercepted request
            storeRequestData(requestData);
            sendResponse({
                success: true
            });
            return true;
        }
        // Handle replay request
        if (message.type === "REPLAY_REQUEST") {
            handleReplayRequest(message.requestData).then((result)=>sendResponse(result)).catch((error)=>sendResponse({
                    error: error.message
                }));
            return true; // Keep message channel open for async response
        }
    } catch (error) {
        console.error("Error handling message:", error);
        try {
            sendResponse({
                error: "Internal error processing message"
            });
        } catch (responseError) {
            console.error("Error sending error response:", responseError);
        }
        return false;
    }
});
// Replay request function
async function handleReplayRequest(requestData) {
    try {
        console.log("Background: Replaying request to", requestData.url);
        // Prepare headers
        const headers = {};
        if (requestData.headers) requestData.headers.forEach((header)=>{
            // Skip some headers that browsers handle automatically or cause issues
            const skipHeaders = [
                "host",
                "user-agent",
                "accept-encoding",
                "connection",
                "upgrade-insecure-requests",
                "sec-fetch-site",
                "sec-fetch-mode",
                "sec-fetch-dest",
                "sec-ch-ua",
                "sec-ch-ua-mobile",
                "sec-ch-ua-platform",
                "origin",
                "referer",
                "content-length"
            ];
            if (!skipHeaders.includes(header.name.toLowerCase())) headers[header.name] = header.value || "";
        });
        const fetchOptions = {
            method: requestData.method,
            headers: headers,
            mode: "cors",
            credentials: "omit"
        };
        // Add body for POST/PUT/PATCH requests
        if (requestData.body && [
            "POST",
            "PUT",
            "PATCH"
        ].includes(requestData.method)) fetchOptions.body = requestData.body;
        console.log("Background: Fetch options:", fetchOptions);
        const response = await fetch(requestData.url, fetchOptions);
        // Get response info
        const responseInfo = {
            status: response.status,
            statusText: response.statusText,
            headers: Object.fromEntries(response.headers.entries())
        };
        // Try to get response body
        const contentType = response.headers.get("content-type") || "";
        let responseBody;
        if (contentType.includes("application/json")) try {
            const jsonData = await response.json();
            responseBody = JSON.stringify(jsonData, null, 2);
        } catch (e) {
            responseBody = await response.text();
        }
        else if (contentType.includes("text/") || contentType.includes("application/xml")) responseBody = await response.text();
        else responseBody = `[${contentType || "Binary Data"}] - ${response.headers.get("content-length") || "Unknown"} bytes`;
        return {
            success: true,
            responseInfo,
            responseBody: `Status: ${response.status} ${response.statusText}\n\n${responseBody}`
        };
    } catch (error) {
        console.error("Background: Error replaying request:", error);
        throw error;
    }
}
// Handle extension startup and shutdown
chrome.runtime.onStartup.addListener(()=>{
    console.log("Extension startup detected");
});
chrome.runtime.onSuspend.addListener(()=>{
    console.log("Extension is being suspended");
    handleContextInvalidation();
});
// Handle extension installation and updates
chrome.runtime.onInstalled.addListener((details)=>{
    console.log("Extension installed/updated:", details.reason);
    if (details.reason === "install") console.log("First time installation");
    else if (details.reason === "update") console.log("Extension updated from version:", details.previousVersion);
});
console.log("API Interceptor background script loaded and ready");

},{"./utils/storage":"6E8Wy"}],"6E8Wy":[function(require,module,exports) {
// Storage utility functions for API Interceptor Extension
var parcelHelpers = require("@parcel/transformer-js/src/esmodule-helpers.js");
parcelHelpers.defineInteropFlag(exports);
parcelHelpers.export(exports, "StorageManager", ()=>StorageManager);
class StorageManager {
    static #_ = (()=>{
        this.STORAGE_KEY = "apiRequests";
    })();
    static #_1 = (()=>{
        this.MAX_REQUESTS = 1000;
    })();
    static #_2 = (()=>{
        this.SETTINGS_KEY = "apiInterceptorSettings";
    })();
    // Get all stored requests
    static async getRequests() {
        try {
            const result = await chrome.storage.local.get([
                this.STORAGE_KEY
            ]);
            return result[this.STORAGE_KEY] || [];
        } catch (error) {
            console.error("Error getting requests from storage:", error);
            return [];
        }
    }
    // Store a new request
    static async storeRequest(request) {
        try {
            const existingRequests = await this.getRequests();
            const updatedRequests = [
                request,
                ...existingRequests
            ].slice(0, this.MAX_REQUESTS);
            await chrome.storage.local.set({
                [this.STORAGE_KEY]: updatedRequests
            });
        } catch (error) {
            console.error("Error storing request:", error);
        }
    }
    // Clear all requests
    static async clearRequests() {
        try {
            await chrome.storage.local.set({
                [this.STORAGE_KEY]: []
            });
        } catch (error) {
            console.error("Error clearing requests:", error);
        }
    }
    // Get storage usage statistics
    static async getStorageStats() {
        try {
            const storageInfo = await chrome.storage.local.getBytesInUse();
            const quota = chrome.storage.local.QUOTA_BYTES;
            return {
                used: storageInfo,
                total: quota
            };
        } catch (error) {
            console.error("Error getting storage stats:", error);
            return {
                used: 0,
                total: 5242880 // 5MB default quota
            };
        }
    }
    // Export requests to JSON
    static async exportRequests() {
        try {
            const requests = await this.getRequests();
            return {
                version: "1.0",
                timestamp: new Date().toISOString(),
                requestCount: requests.length,
                requests: requests
            };
        } catch (error) {
            console.error("Error exporting requests:", error);
            throw error;
        }
    }
    // Import requests from JSON
    static async importRequests(importData) {
        try {
            if (!importData.requests || !Array.isArray(importData.requests)) throw new Error("Invalid import data format");
            const existingRequests = await this.getRequests();
            const importedRequests = importData.requests;
            // Merge and deduplicate requests
            const allRequests = [
                ...importedRequests,
                ...existingRequests
            ];
            const uniqueRequests = allRequests.filter((request, index, self)=>index === self.findIndex((r)=>r.id === request.id)).slice(0, this.MAX_REQUESTS);
            await chrome.storage.local.set({
                [this.STORAGE_KEY]: uniqueRequests
            });
            return importedRequests.length;
        } catch (error) {
            console.error("Error importing requests:", error);
            throw error;
        }
    }
    // Get settings
    static async getSettings() {
        try {
            const result = await chrome.storage.local.get([
                this.SETTINGS_KEY
            ]);
            return {
                maxRequests: this.MAX_REQUESTS,
                autoCapture: true,
                captureResponseBody: false,
                filterDomains: [],
                ...result[this.SETTINGS_KEY]
            };
        } catch (error) {
            console.error("Error getting settings:", error);
            return {
                maxRequests: this.MAX_REQUESTS,
                autoCapture: true,
                captureResponseBody: false,
                filterDomains: []
            };
        }
    }
    // Save settings
    static async saveSettings(settings) {
        try {
            const currentSettings = await this.getSettings();
            const updatedSettings = {
                ...currentSettings,
                ...settings
            };
            await chrome.storage.local.set({
                [this.SETTINGS_KEY]: updatedSettings
            });
        } catch (error) {
            console.error("Error saving settings:", error);
        }
    }
    // Clean old requests (older than specified days)
    static async cleanOldRequests(daysOld = 7) {
        try {
            const requests = await this.getRequests();
            const cutoffTime = Date.now() - daysOld * 86400000;
            const filteredRequests = requests.filter((request)=>request.timestamp > cutoffTime);
            await chrome.storage.local.set({
                [this.STORAGE_KEY]: filteredRequests
            });
            return requests.length - filteredRequests.length;
        } catch (error) {
            console.error("Error cleaning old requests:", error);
            return 0;
        }
    }
    // Search requests
    static async searchRequests(query) {
        try {
            const requests = await this.getRequests();
            const searchTerm = query.toLowerCase();
            return requests.filter((request)=>request.url.toLowerCase().includes(searchTerm) || request.method.toLowerCase().includes(searchTerm) || (request.status?.toString() || "").includes(searchTerm) || request.type.toLowerCase().includes(searchTerm));
        } catch (error) {
            console.error("Error searching requests:", error);
            return [];
        }
    }
    // Get requests by domain
    static async getRequestsByDomain(domain) {
        try {
            const requests = await this.getRequests();
            return requests.filter((request)=>{
                try {
                    const url = new URL(request.url);
                    return url.hostname === domain;
                } catch  {
                    return false;
                }
            });
        } catch (error) {
            console.error("Error getting requests by domain:", error);
            return [];
        }
    }
    // Get unique domains from stored requests
    static async getUniqueDomains() {
        try {
            const requests = await this.getRequests();
            const domains = new Set();
            requests.forEach((request)=>{
                try {
                    const url = new URL(request.url);
                    domains.add(url.hostname);
                } catch  {
                // Ignore invalid URLs
                }
            });
            return Array.from(domains).sort();
        } catch (error) {
            console.error("Error getting unique domains:", error);
            return [];
        }
    }
}

},{"@parcel/transformer-js/src/esmodule-helpers.js":"5G9Z5"}],"5G9Z5":[function(require,module,exports) {
exports.interopDefault = function(a) {
    return a && a.__esModule ? a : {
        default: a
    };
};
exports.defineInteropFlag = function(a) {
    Object.defineProperty(a, "__esModule", {
        value: true
    });
};
exports.exportAll = function(source, dest) {
    Object.keys(source).forEach(function(key) {
        if (key === "default" || key === "__esModule" || dest.hasOwnProperty(key)) return;
        Object.defineProperty(dest, key, {
            enumerable: true,
            get: function() {
                return source[key];
            }
        });
    });
    return dest;
};
exports.export = function(dest, destName, get) {
    Object.defineProperty(dest, destName, {
        enumerable: true,
        get: get
    });
};

},{}]},["ljBRi","8oeFb"], "8oeFb", "parcelRequireff35")

//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
 globalThis.define=__define;  })(globalThis.define);