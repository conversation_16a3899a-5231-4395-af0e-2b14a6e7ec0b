// DevTools Panel JavaScript for API Interceptor
console.log("🎨 API Interceptor DevTools Panel loaded");

let requests = [];
let selectedRequest = null;
let currentDomain = '';
let preserveLog = false;
let captureEnabled = true;

// Initialize panel
function initPanel() {
    console.log("🚀 DevTools panel initialized");
    loadSettings();
    setupMessageListener();
    loadStoredRequests();
    updateRequestCount();
    setupEventListeners();
    updateCurrentDomain();
    updateUI();

    // Set up periodic refresh to catch any missed requests (less frequent)
    setInterval(() => {
        console.log("🔄 Periodic refresh - checking for new requests");
        checkDomainChange();
        loadStoredRequests();
    }, 10000); // Refresh every 10 seconds (reduced frequency)
}

// Load settings from storage
function loadSettings() {
    chrome.storage.local.get(['preserveLog', 'captureEnabled'], function(result) {
        preserveLog = result.preserveLog || false;
        captureEnabled = result.captureEnabled !== false; // Default to true
        console.log(`📋 Settings loaded: preserveLog=${preserveLog}, captureEnabled=${captureEnabled}`);
    });
}

// Save settings to storage
function saveSettings() {
    chrome.storage.local.set({
        preserveLog: preserveLog,
        captureEnabled: captureEnabled
    }, function() {
        console.log(`💾 Settings saved: preserveLog=${preserveLog}, captureEnabled=${captureEnabled}`);
    });
}

// Get current domain from inspected window
function updateCurrentDomain() {
    if (chrome.devtools && chrome.devtools.inspectedWindow) {
        chrome.devtools.inspectedWindow.eval('window.location.hostname', function(hostname) {
            if (hostname && hostname !== currentDomain) {
                const previousDomain = currentDomain;
                currentDomain = hostname;
                console.log(`🌐 Domain changed from "${previousDomain}" to "${currentDomain}"`);

                if (previousDomain && !preserveLog) {
                    console.log("🗑️ Clearing requests due to domain change (preserve log disabled)");
                    clearRequestsInternal();
                }
            }
        });
    }
}

// Check for domain changes
function checkDomainChange() {
    updateCurrentDomain();
}

// Setup event listeners for buttons
function setupEventListeners() {
    const refreshBtn = document.getElementById('refreshBtn');
    const clearBtn = document.getElementById('clearBtn');
    const exportBtn = document.getElementById('exportBtn');
    const preserveLogBtn = document.getElementById('preserveLogBtn');
    const captureBtn = document.getElementById('captureBtn');

    if (refreshBtn) {
        refreshBtn.addEventListener('click', forceRefresh);
    }

    if (clearBtn) {
        clearBtn.addEventListener('click', clearRequests);
    }

    if (exportBtn) {
        exportBtn.addEventListener('click', exportRequests);
    }

    if (preserveLogBtn) {
        preserveLogBtn.addEventListener('click', togglePreserveLog);
    }

    if (captureBtn) {
        captureBtn.addEventListener('click', toggleCapture);
    }
}

// Setup message listener for new requests
function setupMessageListener() {
    console.log("🔧 Setting up message listener for DevTools panel");
    
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        console.log("📨 Panel received message:", message.type, message);

        if (message.type === 'DEVTOOLS_REQUEST_COMPLETE') {
            console.log("✅ Processing DEVTOOLS_REQUEST_COMPLETE message");
            addRequest(message.data);
        } else if (message.type === 'DEVTOOLS_REQUEST') {
            console.log("✅ Processing DEVTOOLS_REQUEST message");
            addRequest(message.data);
        } else if (message.type === 'NEW_REQUEST' && message.data.type === 'devtools') {
            console.log("✅ Processing NEW_REQUEST message for DevTools");
            addRequest(message.data);
        } else {
            console.log("ℹ️ Ignoring message type:", message.type);
        }

        return true; // Keep message channel open
    });
    
    console.log("✅ Message listener setup complete");
}

// Load existing requests from storage
async function loadStoredRequests() {
    try {
        chrome.runtime.sendMessage({ type: 'GET_DEVTOOLS_REQUESTS' }, function(response) {
            if (chrome.runtime.lastError) {
                console.error("❌ Chrome runtime error:", chrome.runtime.lastError.message);
                return;
            }

            if (response && response.requests) {
                // Only update if we have different data and more requests
                if (response.requests.length >= requests.length ||
                    JSON.stringify(response.requests) !== JSON.stringify(requests)) {
                    requests = response.requests;
                    renderRequestList();
                    updateRequestCount();
                    console.log(`📋 Loaded ${response.requests.length} DevTools requests`);
                }
            } else {
                // Fallback to general request method
                chrome.runtime.sendMessage({ type: 'GET_REQUESTS' }, function(fallbackResponse) {
                    if (fallbackResponse && fallbackResponse.requests) {
                        const devtoolsRequests = fallbackResponse.requests.filter(req =>
                            req.type === 'devtools' || req.type === 'fetch' || req.type === 'xmlhttprequest'
                        );

                        // Only update if we have more requests or different data
                        if (devtoolsRequests.length >= requests.length ||
                            JSON.stringify(devtoolsRequests) !== JSON.stringify(requests)) {
                            requests = devtoolsRequests;
                            renderRequestList();
                            updateRequestCount();
                            console.log(`📋 Loaded ${devtoolsRequests.length} DevTools requests (fallback)`);
                        }
                    } else {
                        // Don't clear the list automatically - only log the issue
                        console.log("⚠️ No data from storage, keeping existing requests");
                    }
                });
            }
        });
    } catch (error) {
        console.error("❌ Error loading stored requests:", error);
    }
}

// Toggle preserve log setting
function togglePreserveLog() {
    preserveLog = !preserveLog;
    saveSettings();
    updateUI();
    console.log(`🔄 Preserve log ${preserveLog ? 'enabled' : 'disabled'}`);
}

// Toggle capture setting
function toggleCapture() {
    captureEnabled = !captureEnabled;
    saveSettings();
    updateUI();

    // Notify background script about capture status change
    chrome.runtime.sendMessage({
        type: 'SET_CAPTURE_STATUS',
        isCapturing: captureEnabled
    });

    console.log(`🔄 Capture ${captureEnabled ? 'enabled' : 'disabled'}`);
}

// Update UI elements based on current settings
function updateUI() {
    const preserveLogBtn = document.getElementById('preserveLogBtn');
    const captureBtn = document.getElementById('captureBtn');
    const domainInfo = document.getElementById('domainInfo');
    const captureStatus = document.getElementById('captureStatus');
    const captureStatusText = document.getElementById('captureStatusText');

    if (preserveLogBtn) {
        preserveLogBtn.textContent = '📌 Preserve Log';
        preserveLogBtn.className = preserveLog ? 'btn active' : 'btn';
        preserveLogBtn.title = preserveLog ? 'Preserve log across domain changes (enabled)' : 'Clear log on domain change (disabled)';
    }

    if (captureBtn) {
        captureBtn.textContent = captureEnabled ? '⏸️ Stop' : '▶️ Start';
        captureBtn.className = captureEnabled ? 'btn active' : 'btn';
        captureBtn.title = captureEnabled ? 'Stop capturing requests' : 'Start capturing requests';
    }

    if (captureStatus) {
        captureStatus.className = captureEnabled ? 'status-dot active' : 'status-dot inactive';
    }

    if (captureStatusText) {
        captureStatusText.textContent = captureEnabled ? 'Capture: On' : 'Capture: Off';
    }

    if (domainInfo) {
        domainInfo.textContent = currentDomain ? `Domain: ${currentDomain}` : 'Domain: Unknown';
    }
}

// Add new request to the list
function addRequest(requestData) {
    // Don't add requests if capture is disabled
    if (!captureEnabled) {
        console.log("⏸️ Capture disabled, ignoring request:", requestData.url);
        return;
    }

    console.log("➕ Adding new request:", requestData.url);

    // Check if request already exists (avoid duplicates)
    const existingIndex = requests.findIndex(req =>
        req.id === requestData.id ||
        (req.url === requestData.url &&
         req.method === requestData.method &&
         Math.abs(req.timestamp - requestData.timestamp) < 1000) // Within 1 second
    );

    if (existingIndex !== -1) {
        // Update existing request with new data (might have more complete info)
        requests[existingIndex] = { ...requests[existingIndex], ...requestData };
        console.log("🔄 Updated existing request:", requestData.url);
    } else {
        // Add to beginning of array
        requests.unshift(requestData);
        console.log("✅ Added new request:", requestData.url);
    }

    // Keep only latest 1000 requests
    if (requests.length > 1000) {
        requests = requests.slice(0, 1000);
    }

    renderRequestList();
    updateRequestCount();
}

// Update request count display
function updateRequestCount() {
    const countElement = document.getElementById('requestCount');
    if (countElement) {
        countElement.textContent = `${requests.length} requests`;
    }
}

// Render request list
function renderRequestList() {
    const listElement = document.getElementById('requestList');
    
    if (requests.length === 0) {
        listElement.innerHTML = `
            <div class="empty-state">
                🔍 No requests captured yet.<br>
                Navigate to any page to see network requests with response bodies.
            </div>
        `;
        return;
    }
    
    const html = requests.map(request => {
        const methodClass = `method-${request.method.toLowerCase()}`;
        const isSelected = selectedRequest && selectedRequest.id === request.id;
        const statusClass = getStatusClass(request.status);
        
        return `
            <div class="request-item ${isSelected ? 'selected' : ''}" data-request-id="${request.id}">
                <div class="request-url">
                    <span class="request-method ${methodClass}">${request.method}</span>
                    ${request.url}
                </div>
                <div class="request-status">
                    Status: <span class="${statusClass}">${request.status || 'Pending'}</span> | 
                    ${request.duration ? `${Math.round(request.duration)}ms` : 'N/A'} |
                    ${new Date(request.timestamp).toLocaleTimeString()} |
                    ${request.responseBody ? '📦 Has Response' : '📭 No Response'}
                </div>
            </div>
        `;
    }).join('');
    
    listElement.innerHTML = html;
    
    // Add event listeners to request items
    const requestItems = listElement.querySelectorAll('.request-item');
    requestItems.forEach(item => {
        item.addEventListener('click', () => {
            const requestId = item.getAttribute('data-request-id');
            if (requestId) {
                selectRequest(requestId);
            }
        });
    });
}

// Get status class for styling
function getStatusClass(status) {
    if (!status) return '';
    if (status >= 200 && status < 300) return 'status-success';
    if (status >= 400) return 'status-error';
    if (status >= 300) return 'status-warning';
    return '';
}

// Select a request
function selectRequest(requestId) {
    selectedRequest = requests.find(req => req.id === requestId);
    if (selectedRequest) {
        console.log("👆 Selected request:", selectedRequest.url);
        renderRequestDetails(selectedRequest);
        renderRequestList(); // Re-render to update selection
    }
}

// Render request details
function renderRequestDetails(request) {
    const detailsElement = document.getElementById('requestDetails');

    const requestHeadersHtml = formatHeaders(request.requestHeaders);
    const responseHeadersHtml = formatHeaders(request.responseHeaders);

    const html = `
        <div class="details-content">
            <div class="details-section">
                <div class="details-title">🌐 Request URL</div>
                <div class="details-value">${request.url}</div>
            </div>

            <div class="details-section">
                <div class="details-title">📋 Request Info</div>
                <div class="details-value">Method: ${request.method}
Status: ${request.status || 'Pending'} ${request.statusText || ''}
Duration: ${request.duration ? `${Math.round(request.duration)}ms` : 'N/A'}
MIME Type: ${request.mimeType || 'unknown'}
Size: ${request.size ? `${request.size} bytes` : 'unknown'}
Timestamp: ${new Date(request.timestamp).toISOString()}</div>
            </div>

            <div class="details-section">
                <div class="details-title">📤 Request Headers</div>
                <div class="details-value">${requestHeadersHtml}</div>
            </div>

            ${request.requestBody ? `
                <div class="details-section">
                    <div class="details-title">📝 Request Body</div>
                    <div class="details-value">${formatJson(request.requestBody)}</div>
                </div>
            ` : ''}

            <div class="details-section">
                <div class="details-title">📥 Response Headers</div>
                <div class="details-value">${responseHeadersHtml}</div>
            </div>

            <div class="details-section response-body-section">
                <div class="details-title">📦 Response Body ${request.responseBody ? '✅' : '❌'}</div>
                <div class="details-value">${request.responseBody ? formatJson(request.responseBody) : 'No response body captured'}</div>
            </div>
        </div>
    `;

    detailsElement.innerHTML = html;
}

// Format headers for display
function formatHeaders(headers) {
    if (!headers || headers.length === 0) {
        return 'No headers';
    }

    return headers.map(header => `${header.name}: ${header.value}`).join('\n');
}

// Format JSON if possible
function formatJson(text) {
    if (!text) return 'Empty';

    try {
        const parsed = JSON.parse(text);
        return JSON.stringify(parsed, null, 2);
    } catch {
        return text;
    }
}

// Clear all requests (internal function)
function clearRequestsInternal() {
    requests = [];
    selectedRequest = null;
    renderRequestList();
    updateRequestCount();

    document.getElementById('requestDetails').innerHTML = `
        <div class="empty-state">
            👆 Select a request to view details
        </div>
    `;
}

// Clear all requests (user action)
function clearRequests() {
    console.log("🗑️ Clearing all requests (user action)");
    clearRequestsInternal();

    // Clear from storage
    chrome.runtime.sendMessage({ type: 'CLEAR_REQUESTS' }, function(response) {
        if (chrome.runtime.lastError) {
            console.error("❌ Error clearing requests:", chrome.runtime.lastError.message);
        } else {
            console.log("✅ Successfully cleared requests from storage");
        }
    });
}

// Export requests
function exportRequests() {
    console.log("📥 Exporting requests");
    const dataStr = JSON.stringify(requests, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);

    const link = document.createElement('a');
    link.href = url;
    link.download = `api-interceptor-devtools-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
}

// Force refresh requests
function forceRefresh() {
    console.log("🔄 Force refreshing requests");
    loadStoredRequests();
}

// Make initPanel available globally for devtools.html
window.initPanel = initPanel;

// Handle page navigation - preserve requests across page changes
window.addEventListener('beforeunload', function() {
    console.log("📄 Page navigation detected, preserving request data");
    // Data is already stored in chrome.storage, so it will persist
});

// Handle visibility changes to refresh data when panel becomes visible
document.addEventListener('visibilitychange', function() {
    if (!document.hidden) {
        console.log("👁️ Panel became visible, refreshing data");
        loadStoredRequests();
    }
});

// Initialize when script loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initPanel);
} else {
    initPanel();
}

console.log("✅ DevTools panel script setup complete");
