<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Interceptor Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #555;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #007cba;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 API Interceptor Test Page</h1>
        <p>This page contains various API requests to test the Chrome extension's DevTools panel functionality.</p>
        
        <div class="test-section">
            <h3>📡 JSON API Tests</h3>
            <button onclick="testJSONAPI()">Test JSONPlaceholder API</button>
            <button onclick="testPostRequest()">Test POST Request</button>
            <button onclick="testPutRequest()">Test PUT Request</button>
            <div id="json-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🔄 XMLHttpRequest Tests</h3>
            <button onclick="testXHR()">Test XHR GET</button>
            <button onclick="testXHRPost()">Test XHR POST</button>
            <div id="xhr-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🌍 External API Tests</h3>
            <button onclick="testExternalAPI()">Test GitHub API</button>
            <button onclick="testWeatherAPI()">Test Weather API</button>
            <div id="external-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>❌ Error Tests</h3>
            <button onclick="test404()">Test 404 Error</button>
            <button onclick="testNetworkError()">Test Network Error</button>
            <div id="error-result" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        // Helper function to display results
        function showResult(elementId, message, isError = false) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = isError ? 'result error' : 'result';
            element.innerHTML = message;
        }

        // JSON API Tests
        async function testJSONAPI() {
            try {
                const response = await fetch('https://jsonplaceholder.typicode.com/posts/1');
                const data = await response.json();
                showResult('json-result', `✅ Success: Fetched post "${data.title}"`);
            } catch (error) {
                showResult('json-result', `❌ Error: ${error.message}`, true);
            }
        }

        async function testPostRequest() {
            try {
                const response = await fetch('https://jsonplaceholder.typicode.com/posts', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        title: 'Test Post',
                        body: 'This is a test post from API Interceptor',
                        userId: 1
                    })
                });
                const data = await response.json();
                showResult('json-result', `✅ POST Success: Created post with ID ${data.id}`);
            } catch (error) {
                showResult('json-result', `❌ POST Error: ${error.message}`, true);
            }
        }

        async function testPutRequest() {
            try {
                const response = await fetch('https://jsonplaceholder.typicode.com/posts/1', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        id: 1,
                        title: 'Updated Post',
                        body: 'This post has been updated',
                        userId: 1
                    })
                });
                const data = await response.json();
                showResult('json-result', `✅ PUT Success: Updated post "${data.title}"`);
            } catch (error) {
                showResult('json-result', `❌ PUT Error: ${error.message}`, true);
            }
        }

        // XMLHttpRequest Tests
        function testXHR() {
            const xhr = new XMLHttpRequest();
            xhr.open('GET', 'https://jsonplaceholder.typicode.com/users/1');
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        const data = JSON.parse(xhr.responseText);
                        showResult('xhr-result', `✅ XHR Success: Fetched user "${data.name}"`);
                    } else {
                        showResult('xhr-result', `❌ XHR Error: Status ${xhr.status}`, true);
                    }
                }
            };
            xhr.send();
        }

        function testXHRPost() {
            const xhr = new XMLHttpRequest();
            xhr.open('POST', 'https://jsonplaceholder.typicode.com/posts');
            xhr.setRequestHeader('Content-Type', 'application/json');
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 201) {
                        const data = JSON.parse(xhr.responseText);
                        showResult('xhr-result', `✅ XHR POST Success: Created post with ID ${data.id}`);
                    } else {
                        showResult('xhr-result', `❌ XHR POST Error: Status ${xhr.status}`, true);
                    }
                }
            };
            xhr.send(JSON.stringify({
                title: 'XHR Test Post',
                body: 'This is a test post via XMLHttpRequest',
                userId: 1
            }));
        }

        // External API Tests
        async function testExternalAPI() {
            try {
                const response = await fetch('https://api.github.com/users/octocat');
                const data = await response.json();
                showResult('external-result', `✅ GitHub API Success: User "${data.login}" has ${data.public_repos} public repos`);
            } catch (error) {
                showResult('external-result', `❌ GitHub API Error: ${error.message}`, true);
            }
        }

        async function testWeatherAPI() {
            try {
                // Using a free weather API that doesn't require API key
                const response = await fetch('https://api.open-meteo.com/v1/forecast?latitude=52.52&longitude=13.41&current_weather=true');
                const data = await response.json();
                showResult('external-result', `✅ Weather API Success: Current temperature is ${data.current_weather.temperature}°C`);
            } catch (error) {
                showResult('external-result', `❌ Weather API Error: ${error.message}`, true);
            }
        }

        // Error Tests
        async function test404() {
            try {
                const response = await fetch('https://jsonplaceholder.typicode.com/posts/999999');
                if (!response.ok) {
                    showResult('error-result', `✅ 404 Test Success: Got expected ${response.status} status`);
                } else {
                    showResult('error-result', `❌ 404 Test Failed: Expected 404 but got ${response.status}`, true);
                }
            } catch (error) {
                showResult('error-result', `❌ 404 Test Error: ${error.message}`, true);
            }
        }

        async function testNetworkError() {
            try {
                const response = await fetch('https://this-domain-does-not-exist-12345.com/api/test');
                showResult('error-result', `❌ Network Error Test Failed: Request should have failed`, true);
            } catch (error) {
                showResult('error-result', `✅ Network Error Test Success: Got expected error - ${error.message}`);
            }
        }

        // Auto-run some tests on page load
        window.addEventListener('load', function() {
            console.log('🚀 API Interceptor Test Page loaded');
            console.log('📋 Open DevTools and go to the "API Interceptor" tab to see captured requests');
            
            // Run a simple test automatically
            setTimeout(() => {
                testJSONAPI();
            }, 1000);
        });
    </script>
</body>
</html>
