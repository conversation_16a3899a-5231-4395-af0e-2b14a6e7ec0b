import React, { useState, useEffect } from "react";
import type { RequestData } from "./types";

function OptionsPage() {
  const [requests, setRequests] = useState<RequestData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [storageStats, setStorageStats] = useState({ used: 0, total: 0 });
  const [importFile, setImportFile] = useState<File | null>(null);
  const [importStatus, setImportStatus] = useState<string>('');
  const [selectedTab, setSelectedTab] = useState<'requests' | 'settings'>('requests');
  const [maxRequests, setMaxRequests] = useState<number>(1000);
  const [retentionDays, setRetentionDays] = useState<number>(7);
  const [autoCleanEnabled, setAutoCleanEnabled] = useState<boolean>(true);

  // Load requests and storage stats
  useEffect(() => {
    loadRequests();
    loadStorageStats();
    loadSettings();
  }, []);

  // Load requests from storage
  const loadRequests = async () => {
    setIsLoading(true);
    try {
      const response = await chrome.runtime.sendMessage({ type: 'GET_REQUESTS' });
      const requestData = response.requests || [];
      setRequests(requestData);
    } catch (error) {
      console.error('Error loading requests:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Load storage stats
  const loadStorageStats = async () => {
    try {
      const response = await chrome.runtime.sendMessage({ type: 'GET_STORAGE_STATS' });
      if (response.stats) {
        setStorageStats(response.stats);
      }
    } catch (error) {
      console.error('Error loading storage stats:', error);
    }
  };

  // Load settings
  const loadSettings = async () => {
    try {
      const settings = await chrome.storage.local.get(['maxRequests', 'retentionDays', 'autoCleanEnabled']);
      if (settings.maxRequests) setMaxRequests(settings.maxRequests);
      if (settings.retentionDays) setRetentionDays(settings.retentionDays);
      if (settings.autoCleanEnabled !== undefined) setAutoCleanEnabled(settings.autoCleanEnabled);
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  };

  // Save settings
  const saveSettings = async () => {
    try {
      await chrome.storage.local.set({
        maxRequests,
        retentionDays,
        autoCleanEnabled
      });
      alert('Settings saved successfully');
    } catch (error) {
      console.error('Error saving settings:', error);
      alert('Error saving settings');
    }
  };

  // Clear all requests
  const clearRequests = async () => {
    if (!confirm('Are you sure you want to clear all requests?')) return;
    
    try {
      await chrome.runtime.sendMessage({ type: 'CLEAR_REQUESTS' });
      setRequests([]);
      loadStorageStats();
      alert('All requests cleared successfully');
    } catch (error) {
      console.error('Error clearing requests:', error);
      alert('Error clearing requests');
    }
  };

  // Export requests as JSON
  const exportRequests = async () => {
    try {
      const response = await chrome.runtime.sendMessage({ type: 'EXPORT_REQUESTS' });
      if (response.error) {
        throw new Error(response.error);
      }
      
      const dataStr = JSON.stringify(response.data, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = `api-requests-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error exporting requests:', error);
      alert('Error exporting requests');
    }
  };

  // Handle file selection for import
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setImportFile(e.target.files[0]);
    }
  };

  // Import requests from file
  const importRequests = async () => {
    if (!importFile) {
      setImportStatus('Please select a file to import');
      return;
    }
    
    try {
      setImportStatus('Reading file...');
      const fileContent = await importFile.text();
      const data = JSON.parse(fileContent);
      
      setImportStatus('Importing requests...');
      const response = await chrome.runtime.sendMessage({ 
        type: 'IMPORT_REQUESTS', 
        data 
      });
      
      if (response.error) {
        throw new Error(response.error);
      }
      
      setImportStatus(`Successfully imported ${response.importedCount} requests`);
      loadRequests();
      loadStorageStats();
    } catch (error) {
      console.error('Error importing requests:', error);
      setImportStatus(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  return (
    <div style={{ 
      fontFamily: 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      maxWidth: '1200px',
      margin: '0 auto',
      padding: '24px',
      color: '#111827'
    }}>
      <h1 style={{ fontSize: '24px', fontWeight: 'bold', marginBottom: '24px' }}>
        API Interceptor - Management Console
      </h1>
      
      {/* Tabs */}
      <div style={{ display: 'flex', borderBottom: '1px solid #e5e7eb', marginBottom: '24px' }}>
        <button 
          onClick={() => setSelectedTab('requests')}
          style={{
            padding: '12px 24px',
            fontSize: '16px',
            fontWeight: selectedTab === 'requests' ? 'bold' : 'normal',
            color: selectedTab === 'requests' ? '#2563eb' : '#6b7280',
            borderBottom: selectedTab === 'requests' ? '2px solid #2563eb' : 'none',
            background: 'none',
            border: 'none',
            cursor: 'pointer'
          }}
        >
          Requests
        </button>
        <button 
          onClick={() => setSelectedTab('settings')}
          style={{
            padding: '12px 24px',
            fontSize: '16px',
            fontWeight: selectedTab === 'settings' ? 'bold' : 'normal',
            color: selectedTab === 'settings' ? '#2563eb' : '#6b7280',
            borderBottom: selectedTab === 'settings' ? '2px solid #2563eb' : 'none',
            background: 'none',
            border: 'none',
            cursor: 'pointer'
          }}
        >
          Settings
        </button>
      </div>
      
      {/* Requests Tab */}
      {selectedTab === 'requests' && (
        <div>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
            <div>
              <h2 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '8px' }}>Request Management</h2>
              <p style={{ color: '#6b7280', fontSize: '14px' }}>
                Total Requests: {requests.length} | 
                Storage Used: {(storageStats.used / (1024 * 1024)).toFixed(2)} MB / 
                {(storageStats.total / (1024 * 1024)).toFixed(2)} MB
              </p>
            </div>
            <div style={{ display: 'flex', gap: '12px' }}>
              <button
                onClick={loadRequests}
                disabled={isLoading}
                style={{
                  padding: '8px 16px',
                  fontSize: '14px',
                  backgroundColor: '#f3f4f6',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  cursor: isLoading ? 'not-allowed' : 'pointer'
                }}
              >
                {isLoading ? 'Loading...' : 'Refresh'}
              </button>
              <button
                onClick={exportRequests}
                disabled={requests.length === 0}
                style={{
                  padding: '8px 16px',
                  fontSize: '14px',
                  backgroundColor: '#f0f9ff',
                  border: '1px solid #bae6fd',
                  borderRadius: '6px',
                  cursor: requests.length === 0 ? 'not-allowed' : 'pointer',
                  color: '#0369a1'
                }}
              >
                Export All
              </button>
              <button
                onClick={clearRequests}
                style={{
                  padding: '8px 16px',
                  fontSize: '14px',
                  backgroundColor: '#fef2f2',
                  color: '#b91c1c',
                  border: '1px solid #fecaca',
                  borderRadius: '6px',
                  cursor: 'pointer'
                }}
              >
                Clear All
              </button>
            </div>
          </div>
          
          {/* Import Section */}
          <div style={{ 
            backgroundColor: '#f9fafb', 
            padding: '16px', 
            borderRadius: '8px',
            marginBottom: '24px'
          }}>
            <h3 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px' }}>Import Requests</h3>
            <div style={{ display: 'flex', gap: '12px', alignItems: 'center' }}>
              <input 
                type="file" 
                accept=".json" 
                onChange={handleFileChange}
                style={{ flex: 1 }}
              />
              <button
                onClick={importRequests}
                disabled={!importFile}
                style={{
                  padding: '8px 16px',
                  fontSize: '14px',
                  backgroundColor: importFile ? '#f0fdf4' : '#f3f4f6',
                  border: '1px solid ' + (importFile ? '#86efac' : '#d1d5db'),
                  borderRadius: '6px',
                  cursor: importFile ? 'pointer' : 'not-allowed',
                  color: importFile ? '#166534' : '#6b7280'
                }}
              >
                Import
              </button>
            </div>
            {importStatus && (
              <p style={{ 
                marginTop: '8px', 
                fontSize: '14px',
                color: importStatus.startsWith('Error') ? '#b91c1c' : 
                       importStatus.startsWith('Success') ? '#166534' : '#6b7280'
              }}>
                {importStatus}
              </p>
            )}
          </div>
          
          {/* Request List */}
          <div>
            <h3 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px' }}>Recent Requests</h3>
            {isLoading ? (
              <p>Loading requests...</p>
            ) : requests.length === 0 ? (
              <p>No requests captured yet</p>
            ) : (
              <div style={{ overflowX: 'auto' }}>
                <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                  <thead>
                    <tr style={{ backgroundColor: '#f3f4f6' }}>
                      <th style={{ padding: '12px', textAlign: 'left', fontSize: '14px', fontWeight: '600' }}>Method</th>
                      <th style={{ padding: '12px', textAlign: 'left', fontSize: '14px', fontWeight: '600' }}>Status</th>
                      <th style={{ padding: '12px', textAlign: 'left', fontSize: '14px', fontWeight: '600' }}>URL</th>
                      <th style={{ padding: '12px', textAlign: 'left', fontSize: '14px', fontWeight: '600' }}>Time</th>
                      <th style={{ padding: '12px', textAlign: 'left', fontSize: '14px', fontWeight: '600' }}>Duration</th>
                    </tr>
                  </thead>
                  <tbody>
                    {requests.slice(0, 100).map((request) => (
                      <tr key={request.id} style={{ borderBottom: '1px solid #e5e7eb' }}>
                        <td style={{ padding: '12px', fontSize: '14px' }}>{request.method}</td>
                        <td style={{ padding: '12px', fontSize: '14px' }}>{request.status || 'ERR'}</td>
                        <td style={{ padding: '12px', fontSize: '14px', maxWidth: '500px', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                          {request.url}
                        </td>
                        <td style={{ padding: '12px', fontSize: '14px' }}>
                          {new Date(request.timestamp).toLocaleString()}
                        </td>
                        <td style={{ padding: '12px', fontSize: '14px' }}>
                          {request.duration ? `${request.duration}ms` : '-'}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                {requests.length > 100 && (
                  <p style={{ textAlign: 'center', padding: '12px', color: '#6b7280', fontSize: '14px' }}>
                    Showing 100 of {requests.length} requests
                  </p>
                )}
              </div>
            )}
          </div>
        </div>
      )}
      
      {/* Settings Tab */}
      {selectedTab === 'settings' && (
        <div>
          <h2 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '24px' }}>Extension Settings</h2>
          
          <div style={{ 
            backgroundColor: '#f9fafb', 
            padding: '24px', 
            borderRadius: '8px',
            marginBottom: '24px'
          }}>
            <div style={{ marginBottom: '16px' }}>
              <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', marginBottom: '8px' }}>
                Maximum Requests to Store
              </label>
              <input
                type="number"
                value={maxRequests}
                onChange={(e) => setMaxRequests(parseInt(e.target.value) || 1000)}
                min="100"
                max="10000"
                style={{
                  padding: '8px 12px',
                  fontSize: '14px',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  width: '200px'
                }}
              />
              <p style={{ fontSize: '12px', color: '#6b7280', marginTop: '4px' }}>
                Older requests will be removed when this limit is reached
              </p>
            </div>
            
            <div style={{ marginBottom: '16px' }}>
              <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', marginBottom: '8px' }}>
                Data Retention Period (days)
              </label>
              <input
                type="number"
                value={retentionDays}
                onChange={(e) => setRetentionDays(parseInt(e.target.value) || 7)}
                min="1"
                max="30"
                style={{
                  padding: '8px 12px',
                  fontSize: '14px',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  width: '200px'
                }}
              />
              <p style={{ fontSize: '12px', color: '#6b7280', marginTop: '4px' }}>
                Requests older than this will be automatically removed
              </p>
            </div>
            
            <div style={{ marginBottom: '16px' }}>
              <label style={{ display: 'flex', alignItems: 'center', fontSize: '14px', fontWeight: '500', cursor: 'pointer' }}>
                <input
                  type="checkbox"
                  checked={autoCleanEnabled}
                  onChange={(e) => setAutoCleanEnabled(e.target.checked)}
                  style={{ marginRight: '8px' }}
                />
                Enable Automatic Cleanup
              </label>
              <p style={{ fontSize: '12px', color: '#6b7280', marginTop: '4px', marginLeft: '20px' }}>
                Automatically remove old requests based on the settings above
              </p>
            </div>
            
            <button
              onClick={saveSettings}
              style={{
                padding: '8px 16px',
                fontSize: '14px',
                backgroundColor: '#f0fdf4',
                border: '1px solid #86efac',
                borderRadius: '6px',
                cursor: 'pointer',
                color: '#166534',
                marginTop: '8px'
              }}
            >
              Save Settings
            </button>
          </div>
          
          <div style={{ 
            backgroundColor: '#f9fafb', 
            padding: '24px', 
            borderRadius: '8px'
          }}>
            <h3 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px' }}>Storage Information</h3>
            <p style={{ fontSize: '14px', marginBottom: '8px' }}>
              Used: {(storageStats.used / (1024 * 1024)).toFixed(2)} MB
            </p>
            <p style={{ fontSize: '14px', marginBottom: '8px' }}>
              Available: {((storageStats.total - storageStats.used) / (1024 * 1024)).toFixed(2)} MB
            </p>
            <p style={{ fontSize: '14px', marginBottom: '8px' }}>
              Total: {(storageStats.total / (1024 * 1024)).toFixed(2)} MB
            </p>
            <div style={{ 
              height: '8px', 
              backgroundColor: '#e5e7eb', 
              borderRadius: '4px', 
              overflow: 'hidden',
              marginTop: '16px'
            }}>
              <div style={{ 
                height: '100%', 
                width: `${(storageStats.used / storageStats.total) * 100}%`, 
                backgroundColor: (storageStats.used / storageStats.total) > 0.9 ? '#ef4444' : 
                                (storageStats.used / storageStats.total) > 0.7 ? '#f59e0b' : '#10b981'
              }} />
            </div>
            <p style={{ fontSize: '12px', color: '#6b7280', marginTop: '8px' }}>
              {((storageStats.used / storageStats.total) * 100).toFixed(1)}% used
            </p>
          </div>
        </div>
      )}
    </div>
  );
}

export default OptionsPage;
