// Content script to intercept fetch and XMLHttpRequest responses
// This script runs in the context of web pages to capture response bodies

// Early exit if extension context is invalid
if (typeof chrome === 'undefined' || !chrome.runtime || !chrome.runtime.id) {
  console.log('Extension context not available, content script will not run');
  // Don't throw an error, just exit silently
} else {

interface InterceptedRequest {
  id: string;
  url: string;
  method: string;
  timestamp: number;
  requestHeaders?: Record<string, string>;
  requestBody?: string;
  responseHeaders?: Record<string, string>;
  responseBody?: string;
  status?: number;
  statusText?: string;
}

// Generate unique request ID
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// Check if extension context is still valid
function isExtensionContextValid(): boolean {
  try {
    return !!(chrome && chrome.runtime && chrome.runtime.id);
  } catch (error) {
    return false;
  }
}

// Send intercepted data to background script
function sendToBackground(data: InterceptedRequest) {
  // Check if extension context is still valid
  if (!isExtensionContextValid()) {
    console.log('Extension context invalidated, skipping message');
    return;
  }

  try {
    // Send to background script for general storage
    chrome.runtime.sendMessage({
      type: 'INTERCEPTED_REQUEST',
      data: data
    }).catch((error) => {
      // Check for specific extension context errors
      if (error.message && error.message.includes('Extension context invalidated')) {
        console.log('Extension context invalidated during message send');
        return;
      }
      console.warn('Error sending intercepted request:', error);
    });

    // Also send directly to DevTools panel if response is complete
    if (data.status && data.responseBody !== undefined) {
      chrome.runtime.sendMessage({
        type: 'DEVTOOLS_REQUEST_COMPLETE',
        data: {
          ...data,
          type: 'devtools',
          duration: data.status ? Date.now() - data.timestamp : undefined
        }
      }).catch((error) => {
        // Ignore errors if DevTools panel is not open
        if (error && !error.message?.includes('Extension context invalidated')) {
          console.debug('DevTools message send failed (panel likely closed):', error.message);
        }
      });
    }
  } catch (error) {
    // Handle synchronous errors
    if (error instanceof Error && error.message.includes('Extension context invalidated')) {
      console.log('Extension context invalidated');
      return;
    }
    console.error('Error sending intercepted request:', error);
  }
}

// Intercept fetch API
const originalFetch = window.fetch;
window.fetch = async function(...args: Parameters<typeof fetch>): Promise<Response> {
  const startTime = Date.now();
  const requestId = generateRequestId();
  
  // Parse request details
  const [input, init] = args;
  const url = typeof input === 'string' ? input : input.url;
  const method = init?.method || 'GET';
  
  // Get request headers
  const requestHeaders: Record<string, string> = {};
  if (init?.headers) {
    if (init.headers instanceof Headers) {
      init.headers.forEach((value, key) => {
        requestHeaders[key] = value;
      });
    } else if (Array.isArray(init.headers)) {
      init.headers.forEach(([key, value]) => {
        requestHeaders[key] = value;
      });
    } else {
      Object.entries(init.headers).forEach(([key, value]) => {
        requestHeaders[key] = value;
      });
    }
  }
  
  // Get request body
  let requestBody: string | undefined;
  if (init?.body) {
    if (typeof init.body === 'string') {
      requestBody = init.body;
    } else if (init.body instanceof FormData) {
      requestBody = '[FormData]';
    } else if (init.body instanceof URLSearchParams) {
      requestBody = init.body.toString();
    } else {
      requestBody = '[Binary Data]';
    }
  }
  
  try {
    // Make the actual request
    const response = await originalFetch.apply(this, args);
    
    // Clone response to read body without consuming it
    const responseClone = response.clone();
    
    // Get response headers
    const responseHeaders: Record<string, string> = {};
    response.headers.forEach((value, key) => {
      responseHeaders[key] = value;
    });
    
    // Try to read response body
    let responseBody: string | undefined;
    try {
      const contentType = response.headers.get('content-type') || '';
      const contentLength = response.headers.get('content-length');

      // Try to read text-based responses
      if (contentType.includes('application/json') ||
          contentType.includes('text/') ||
          contentType.includes('application/xml') ||
          contentType.includes('application/javascript') ||
          contentType.includes('application/x-www-form-urlencoded')) {

        const text = await responseClone.text();

        // Format JSON for better readability
        if (contentType.includes('application/json') && text) {
          try {
            const parsed = JSON.parse(text);
            responseBody = JSON.stringify(parsed, null, 2);
          } catch {
            responseBody = text;
          }
        } else {
          responseBody = text;
        }
      } else if (contentType.includes('image/') ||
                 contentType.includes('video/') ||
                 contentType.includes('audio/') ||
                 contentType.includes('application/octet-stream')) {
        responseBody = `[${contentType}] - ${contentLength || 'Unknown'} bytes`;
      } else {
        // Try to read as text anyway for unknown content types
        try {
          const text = await responseClone.text();
          responseBody = text || `[${contentType || 'Unknown Content Type'}]`;
        } catch {
          responseBody = `[${contentType || 'Binary Data'}] - ${contentLength || 'Unknown'} bytes`;
        }
      }
    } catch (error) {
      responseBody = `[Error reading response body: ${error instanceof Error ? error.message : 'Unknown error'}]`;
    }
    
    // Send intercepted data
    const interceptedData: InterceptedRequest = {
      id: requestId,
      url: url,
      method: method,
      timestamp: startTime,
      requestHeaders,
      requestBody,
      responseHeaders,
      responseBody,
      status: response.status,
      statusText: response.statusText
    };
    
    sendToBackground(interceptedData);
    
    return response;
  } catch (error) {
    // Send error data
    const interceptedData: InterceptedRequest = {
      id: requestId,
      url: url,
      method: method,
      timestamp: startTime,
      requestHeaders,
      requestBody,
      status: 0,
      statusText: error instanceof Error ? error.message : 'Network Error'
    };
    
    sendToBackground(interceptedData);
    
    throw error;
  }
};

// Intercept XMLHttpRequest
const originalXHROpen = XMLHttpRequest.prototype.open;
const originalXHRSend = XMLHttpRequest.prototype.send;

XMLHttpRequest.prototype.open = function(method: string, url: string, ...args: any[]) {
  (this as any)._interceptorData = {
    id: generateRequestId(),
    method: method.toUpperCase(),
    url: url,
    timestamp: Date.now()
  };
  
  return originalXHROpen.apply(this, [method, url, ...args]);
};

XMLHttpRequest.prototype.send = function(body?: Document | XMLHttpRequestBodyInit | null) {
  const interceptorData = (this as any)._interceptorData;
  if (!interceptorData) {
    return originalXHRSend.apply(this, [body]);
  }
  
  // Store request body
  if (body) {
    if (typeof body === 'string') {
      interceptorData.requestBody = body;
    } else if (body instanceof FormData) {
      interceptorData.requestBody = '[FormData]';
    } else {
      interceptorData.requestBody = '[Binary Data]';
    }
  }
  
  // Store request headers
  interceptorData.requestHeaders = {};
  
  // Listen for response
  const originalOnReadyStateChange = this.onreadystatechange;
  this.onreadystatechange = function() {
    if (this.readyState === XMLHttpRequest.DONE) {
      // Get response headers
      const responseHeaders: Record<string, string> = {};
      const headerString = this.getAllResponseHeaders();
      if (headerString) {
        headerString.split('\r\n').forEach(line => {
          const [key, value] = line.split(': ');
          if (key && value) {
            responseHeaders[key] = value;
          }
        });
      }
      
      // Get response body
      let responseBody: string | undefined;
      try {
        const contentType = this.getResponseHeader('content-type') || '';
        const contentLength = this.getResponseHeader('content-length');

        if (contentType.includes('application/json') ||
            contentType.includes('text/') ||
            contentType.includes('application/xml') ||
            contentType.includes('application/javascript') ||
            contentType.includes('application/x-www-form-urlencoded')) {

          const text = this.responseText;

          // Format JSON for better readability
          if (contentType.includes('application/json') && text) {
            try {
              const parsed = JSON.parse(text);
              responseBody = JSON.stringify(parsed, null, 2);
            } catch {
              responseBody = text;
            }
          } else {
            responseBody = text;
          }
        } else if (contentType.includes('image/') ||
                   contentType.includes('video/') ||
                   contentType.includes('audio/') ||
                   contentType.includes('application/octet-stream')) {
          responseBody = `[${contentType}] - ${contentLength || 'Unknown'} bytes`;
        } else {
          // Try to read as text anyway
          try {
            responseBody = this.responseText || `[${contentType || 'Unknown Content Type'}]`;
          } catch {
            responseBody = `[${contentType || 'Binary Data'}] - ${contentLength || 'Unknown'} bytes`;
          }
        }
      } catch (error) {
        responseBody = `[Error reading response body: ${error instanceof Error ? error.message : 'Unknown error'}]`;
      }
      
      // Send intercepted data
      const finalData: InterceptedRequest = {
        ...interceptorData,
        responseHeaders,
        responseBody,
        status: this.status,
        statusText: this.statusText
      };
      
      sendToBackground(finalData);
    }
    
    if (originalOnReadyStateChange) {
      originalOnReadyStateChange.apply(this);
    }
  };
  
  return originalXHRSend.apply(this, [body]);
};

console.log('API Interceptor content script loaded');

} // End of extension context check
