// Type definitions for API Interceptor Extension

export interface RequestData {
  id: string;
  url: string;
  method: string;
  timestamp: number;
  requestHeaders: chrome.webRequest.HttpHeader[];
  requestBody?: string;
  responseHeaders?: chrome.webRequest.HttpHeader[];
  responseBody?: string;
  status?: number;
  statusText?: string;
  type: string;
  tabId: number;
  duration?: number;
}

export interface MessageTypes {
  GET_REQUESTS: {
    type: 'GET_REQUESTS';
  };
  CLEAR_REQUESTS: {
    type: 'CLEAR_REQUESTS';
  };
  NEW_REQUEST: {
    type: 'NEW_REQUEST';
    data: RequestData;
  };
}

export interface StorageData {
  apiRequests: RequestData[];
}

export interface RequestFilter {
  method?: string;
  status?: number;
  url?: string;
  type?: string;
}

export interface RequestStats {
  total: number;
  success: number;
  error: number;
  pending: number;
  averageResponseTime: number;
}
