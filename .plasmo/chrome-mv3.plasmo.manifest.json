{"icons": {"16": "./gen-assets/icon16.plasmo.png", "32": "./gen-assets/icon32.plasmo.png", "48": "./gen-assets/icon48.plasmo.png", "64": "./gen-assets/icon64.plasmo.png", "128": "./gen-assets/icon128.plasmo.png"}, "manifest_version": 3, "action": {"default_icon": {"16": "./gen-assets/icon16.plasmo.png", "32": "./gen-assets/icon32.plasmo.png", "48": "./gen-assets/icon48.plasmo.png", "64": "./gen-assets/icon64.plasmo.png", "128": "./gen-assets/icon128.plasmo.png"}, "default_popup": "./popup.html"}, "version": "0.0.1", "author": "Plasmo Corp. <<EMAIL>>", "name": "Api interceptor extension", "description": "A Chrome extension to intercept and display all API requests with beautiful UI", "background": {"service_worker": "./static/background/index.ts"}, "options_ui": {"page": "./options.html", "open_in_tab": true}, "devtools_page": "devtools.html", "permissions": ["webRequest", "storage", "activeTab"], "content_scripts": [{"matches": ["<all_urls>"], "js": ["../content.ts"]}], "host_permissions": ["https://*/*", "http://*/*"], "options_page": "options.html"}