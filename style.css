@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 active:bg-primary-800;
  }
  
  .btn-secondary {
    @apply btn bg-gray-100 text-gray-900 hover:bg-gray-200 active:bg-gray-300;
  }
  
  .btn-ghost {
    @apply btn hover:bg-gray-100 hover:text-gray-900;
  }
  
  .btn-sm {
    @apply h-8 px-3 text-xs;
  }
  
  .btn-md {
    @apply h-9 px-4 py-2;
  }
  
  .btn-lg {
    @apply h-10 px-8;
  }
  
  .input {
    @apply flex h-9 w-full rounded-md border border-gray-300 bg-white px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }
  
  .card {
    @apply rounded-lg border border-gray-200 bg-white shadow-sm;
  }
  
  .status-badge {
    @apply inline-flex items-center rounded-full px-2 py-1 text-xs font-medium;
  }
  
  .status-success {
    @apply status-badge bg-success-100 text-success-800;
  }
  
  .status-error {
    @apply status-badge bg-error-100 text-error-800;
  }
  
  .status-warning {
    @apply status-badge bg-warning-100 text-warning-800;
  }
  
  .status-info {
    @apply status-badge bg-primary-100 text-primary-800;
  }
  
  .method-get {
    @apply bg-success-100 text-success-800;
  }
  
  .method-post {
    @apply bg-primary-100 text-primary-800;
  }
  
  .method-put {
    @apply bg-warning-100 text-warning-800;
  }
  
  .method-delete {
    @apply bg-error-100 text-error-800;
  }
  
  .method-patch {
    @apply bg-purple-100 text-purple-800;
  }
  
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(203 213 225) transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgb(203 213 225);
    border-radius: 3px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgb(148 163 184);
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}
