hoistPattern:
  - '*'
hoistedDependencies:
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.28.0':
    '@babel/compat-data': private
  '@babel/core@7.28.0':
    '@babel/core': private
  '@babel/generator@7.28.0':
    '@babel/generator': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-globals@7.28.0':
    '@babel/helper-globals': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.27.3(@babel/core@7.28.0)':
    '@babel/helper-module-transforms': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helpers@7.27.6':
    '@babel/helpers': private
  '@babel/parser@7.28.0':
    '@babel/parser': private
  '@babel/runtime@7.27.6':
    '@babel/runtime': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.28.0':
    '@babel/traverse': private
  '@babel/types@7.28.1':
    '@babel/types': private
  '@esbuild/darwin-arm64@0.18.20':
    '@esbuild/darwin-arm64': private
  '@expo/spawn-async@1.7.2':
    '@expo/spawn-async': private
  '@img/sharp-darwin-arm64@0.33.5':
    '@img/sharp-darwin-arm64': private
  '@img/sharp-libvips-darwin-arm64@1.0.4':
    '@img/sharp-libvips-darwin-arm64': private
  '@inquirer/checkbox@4.2.0(@types/node@20.11.5)':
    '@inquirer/checkbox': private
  '@inquirer/confirm@5.1.14(@types/node@20.11.5)':
    '@inquirer/confirm': private
  '@inquirer/core@10.1.15(@types/node@20.11.5)':
    '@inquirer/core': private
  '@inquirer/editor@4.2.15(@types/node@20.11.5)':
    '@inquirer/editor': private
  '@inquirer/expand@4.0.17(@types/node@20.11.5)':
    '@inquirer/expand': private
  '@inquirer/figures@1.0.13':
    '@inquirer/figures': private
  '@inquirer/input@4.2.1(@types/node@20.11.5)':
    '@inquirer/input': private
  '@inquirer/number@3.0.17(@types/node@20.11.5)':
    '@inquirer/number': private
  '@inquirer/password@4.0.17(@types/node@20.11.5)':
    '@inquirer/password': private
  '@inquirer/prompts@7.7.1(@types/node@20.11.5)':
    '@inquirer/prompts': private
  '@inquirer/rawlist@4.1.5(@types/node@20.11.5)':
    '@inquirer/rawlist': private
  '@inquirer/search@3.0.17(@types/node@20.11.5)':
    '@inquirer/search': private
  '@inquirer/select@4.3.1(@types/node@20.11.5)':
    '@inquirer/select': private
  '@inquirer/type@3.0.8(@types/node@20.11.5)':
    '@inquirer/type': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@jridgewell/gen-mapping@0.3.12':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/sourcemap-codec@1.5.4':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.29':
    '@jridgewell/trace-mapping': private
  '@lezer/common@1.2.3':
    '@lezer/common': private
  '@lezer/lr@1.4.2':
    '@lezer/lr': private
  '@lmdb/lmdb-darwin-arm64@2.7.11':
    '@lmdb/lmdb-darwin-arm64': private
  '@mischnic/json-sourcemap@0.1.1':
    '@mischnic/json-sourcemap': private
  '@msgpackr-extract/msgpackr-extract-darwin-arm64@3.0.3':
    '@msgpackr-extract/msgpackr-extract-darwin-arm64': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@parcel/bundler-default@2.9.3(@parcel/core@2.9.3)':
    '@parcel/bundler-default': private
  '@parcel/cache@2.9.3(@parcel/core@2.9.3)':
    '@parcel/cache': private
  '@parcel/codeframe@2.9.3':
    '@parcel/codeframe': private
  '@parcel/compressor-raw@2.9.3(@parcel/core@2.9.3)':
    '@parcel/compressor-raw': private
  '@parcel/config-default@2.9.3(@parcel/core@2.9.3)(@swc/helpers@0.5.17)(postcss@8.5.6)(typescript@5.8.2)':
    '@parcel/config-default': private
  '@parcel/core@2.9.3':
    '@parcel/core': private
  '@parcel/diagnostic@2.9.3':
    '@parcel/diagnostic': private
  '@parcel/events@2.9.3':
    '@parcel/events': private
  '@parcel/fs-search@2.9.3':
    '@parcel/fs-search': private
  '@parcel/fs@2.9.3(@parcel/core@2.9.3)':
    '@parcel/fs': private
  '@parcel/graph@2.9.3':
    '@parcel/graph': private
  '@parcel/hash@2.9.3':
    '@parcel/hash': private
  '@parcel/logger@2.9.3':
    '@parcel/logger': private
  '@parcel/markdown-ansi@2.9.3':
    '@parcel/markdown-ansi': private
  '@parcel/namer-default@2.9.3(@parcel/core@2.9.3)':
    '@parcel/namer-default': private
  '@parcel/node-resolver-core@3.0.3(@parcel/core@2.9.3)':
    '@parcel/node-resolver-core': private
  '@parcel/optimizer-css@2.9.3(@parcel/core@2.9.3)':
    '@parcel/optimizer-css': private
  '@parcel/optimizer-data-url@2.9.3(@parcel/core@2.9.3)':
    '@parcel/optimizer-data-url': private
  '@parcel/optimizer-htmlnano@2.9.3(@parcel/core@2.9.3)(postcss@8.5.6)(typescript@5.8.2)':
    '@parcel/optimizer-htmlnano': private
  '@parcel/optimizer-image@2.9.3(@parcel/core@2.9.3)':
    '@parcel/optimizer-image': private
  '@parcel/optimizer-svgo@2.9.3(@parcel/core@2.9.3)':
    '@parcel/optimizer-svgo': private
  '@parcel/optimizer-swc@2.9.3(@parcel/core@2.9.3)(@swc/helpers@0.5.17)':
    '@parcel/optimizer-swc': private
  '@parcel/package-manager@2.9.3(@parcel/core@2.9.3)':
    '@parcel/package-manager': private
  '@parcel/packager-css@2.9.3(@parcel/core@2.9.3)':
    '@parcel/packager-css': private
  '@parcel/packager-html@2.9.3(@parcel/core@2.9.3)':
    '@parcel/packager-html': private
  '@parcel/packager-js@2.9.3(@parcel/core@2.9.3)':
    '@parcel/packager-js': private
  '@parcel/packager-raw@2.9.3(@parcel/core@2.9.3)':
    '@parcel/packager-raw': private
  '@parcel/packager-svg@2.9.3(@parcel/core@2.9.3)':
    '@parcel/packager-svg': private
  '@parcel/plugin@2.9.3(@parcel/core@2.9.3)':
    '@parcel/plugin': private
  '@parcel/profiler@2.9.3':
    '@parcel/profiler': private
  '@parcel/reporter-bundle-buddy@2.9.3(@parcel/core@2.9.3)':
    '@parcel/reporter-bundle-buddy': private
  '@parcel/reporter-dev-server@2.9.3(@parcel/core@2.9.3)':
    '@parcel/reporter-dev-server': private
  '@parcel/resolver-default@2.9.3(@parcel/core@2.9.3)':
    '@parcel/resolver-default': private
  '@parcel/runtime-browser-hmr@2.9.3(@parcel/core@2.9.3)':
    '@parcel/runtime-browser-hmr': private
  '@parcel/runtime-js@2.8.3(@parcel/core@2.9.3)':
    '@parcel/runtime-js': private
  '@parcel/runtime-react-refresh@2.9.3(@parcel/core@2.9.3)':
    '@parcel/runtime-react-refresh': private
  '@parcel/runtime-service-worker@2.9.3(@parcel/core@2.9.3)':
    '@parcel/runtime-service-worker': private
  '@parcel/source-map@2.1.1':
    '@parcel/source-map': private
  '@parcel/transformer-babel@2.9.3(@parcel/core@2.9.3)':
    '@parcel/transformer-babel': private
  '@parcel/transformer-css@2.9.3(@parcel/core@2.9.3)':
    '@parcel/transformer-css': private
  '@parcel/transformer-graphql@2.9.3(@parcel/core@2.9.3)':
    '@parcel/transformer-graphql': private
  '@parcel/transformer-html@2.9.3(@parcel/core@2.9.3)':
    '@parcel/transformer-html': private
  '@parcel/transformer-image@2.9.3(@parcel/core@2.9.3)':
    '@parcel/transformer-image': private
  '@parcel/transformer-inline-string@2.9.3(@parcel/core@2.9.3)':
    '@parcel/transformer-inline-string': private
  '@parcel/transformer-js@2.9.3(@parcel/core@2.9.3)':
    '@parcel/transformer-js': private
  '@parcel/transformer-json@2.9.3(@parcel/core@2.9.3)':
    '@parcel/transformer-json': private
  '@parcel/transformer-less@2.9.3(@parcel/core@2.9.3)':
    '@parcel/transformer-less': private
  '@parcel/transformer-postcss@2.9.3(@parcel/core@2.9.3)':
    '@parcel/transformer-postcss': private
  '@parcel/transformer-posthtml@2.9.3(@parcel/core@2.9.3)':
    '@parcel/transformer-posthtml': private
  '@parcel/transformer-raw@2.9.3(@parcel/core@2.9.3)':
    '@parcel/transformer-raw': private
  '@parcel/transformer-react-refresh-wrap@2.9.3(@parcel/core@2.9.3)':
    '@parcel/transformer-react-refresh-wrap': private
  '@parcel/transformer-sass@2.9.3(@parcel/core@2.9.3)':
    '@parcel/transformer-sass': private
  '@parcel/transformer-svg-react@2.9.3(@parcel/core@2.9.3)':
    '@parcel/transformer-svg-react': private
  '@parcel/transformer-svg@2.9.3(@parcel/core@2.9.3)':
    '@parcel/transformer-svg': private
  '@parcel/transformer-worklet@2.9.3(@parcel/core@2.9.3)':
    '@parcel/transformer-worklet': private
  '@parcel/types@2.9.3(@parcel/core@2.9.3)':
    '@parcel/types': private
  '@parcel/utils@2.9.3':
    '@parcel/utils': private
  '@parcel/watcher-darwin-arm64@2.5.1':
    '@parcel/watcher-darwin-arm64': private
  '@parcel/watcher@2.5.1':
    '@parcel/watcher': private
  '@parcel/workers@2.9.3(@parcel/core@2.9.3)':
    '@parcel/workers': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@plasmohq/consolidate@0.17.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    '@plasmohq/consolidate': private
  '@plasmohq/init@0.7.0':
    '@plasmohq/init': private
  '@plasmohq/parcel-bundler@0.5.6':
    '@plasmohq/parcel-bundler': private
  '@plasmohq/parcel-compressor-utf8@0.1.1(@parcel/core@2.9.3)':
    '@plasmohq/parcel-compressor-utf8': private
  '@plasmohq/parcel-config@0.42.0(@swc/core@1.13.2(@swc/helpers@0.5.17))(@swc/helpers@0.5.17)(postcss@8.5.6)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)(typescript@5.8.2)':
    '@plasmohq/parcel-config': private
  '@plasmohq/parcel-core@0.1.11':
    '@plasmohq/parcel-core': private
  '@plasmohq/parcel-namer-manifest@0.3.12':
    '@plasmohq/parcel-namer-manifest': private
  '@plasmohq/parcel-optimizer-encapsulate@0.0.8':
    '@plasmohq/parcel-optimizer-encapsulate': private
  '@plasmohq/parcel-optimizer-es@0.4.1(@swc/helpers@0.5.17)':
    '@plasmohq/parcel-optimizer-es': private
  '@plasmohq/parcel-packager@0.6.15':
    '@plasmohq/parcel-packager': private
  '@plasmohq/parcel-resolver-post@0.4.5(@swc/core@1.13.2(@swc/helpers@0.5.17))(postcss@8.5.6)':
    '@plasmohq/parcel-resolver-post': private
  '@plasmohq/parcel-resolver@0.14.1':
    '@plasmohq/parcel-resolver': private
  '@plasmohq/parcel-runtime@0.25.2':
    '@plasmohq/parcel-runtime': private
  '@plasmohq/parcel-transformer-inject-env@0.2.12':
    '@plasmohq/parcel-transformer-inject-env': private
  '@plasmohq/parcel-transformer-inline-css@0.3.11':
    '@plasmohq/parcel-transformer-inline-css': private
  '@plasmohq/parcel-transformer-manifest@0.21.0':
    '@plasmohq/parcel-transformer-manifest': private
  '@plasmohq/parcel-transformer-svelte@0.6.0':
    '@plasmohq/parcel-transformer-svelte': private
  '@plasmohq/parcel-transformer-vue@0.5.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    '@plasmohq/parcel-transformer-vue': private
  '@pnpm/config.env-replace@1.1.0':
    '@pnpm/config.env-replace': private
  '@pnpm/network.ca-file@1.0.2':
    '@pnpm/network.ca-file': private
  '@pnpm/npm-conf@2.3.1':
    '@pnpm/npm-conf': private
  '@sec-ant/readable-stream@0.4.1':
    '@sec-ant/readable-stream': private
  '@sindresorhus/is@7.0.2':
    '@sindresorhus/is': private
  '@svgr/babel-plugin-add-jsx-attribute@6.5.1(@babel/core@7.28.0)':
    '@svgr/babel-plugin-add-jsx-attribute': private
  '@svgr/babel-plugin-remove-jsx-attribute@8.0.0(@babel/core@7.28.0)':
    '@svgr/babel-plugin-remove-jsx-attribute': private
  '@svgr/babel-plugin-remove-jsx-empty-expression@8.0.0(@babel/core@7.28.0)':
    '@svgr/babel-plugin-remove-jsx-empty-expression': private
  '@svgr/babel-plugin-replace-jsx-attribute-value@6.5.1(@babel/core@7.28.0)':
    '@svgr/babel-plugin-replace-jsx-attribute-value': private
  '@svgr/babel-plugin-svg-dynamic-title@6.5.1(@babel/core@7.28.0)':
    '@svgr/babel-plugin-svg-dynamic-title': private
  '@svgr/babel-plugin-svg-em-dimensions@6.5.1(@babel/core@7.28.0)':
    '@svgr/babel-plugin-svg-em-dimensions': private
  '@svgr/babel-plugin-transform-react-native-svg@6.5.1(@babel/core@7.28.0)':
    '@svgr/babel-plugin-transform-react-native-svg': private
  '@svgr/babel-plugin-transform-svg-component@6.5.1(@babel/core@7.28.0)':
    '@svgr/babel-plugin-transform-svg-component': private
  '@svgr/babel-preset@6.5.1(@babel/core@7.28.0)':
    '@svgr/babel-preset': private
  '@svgr/core@6.5.1':
    '@svgr/core': private
  '@svgr/hast-util-to-babel-ast@6.5.1':
    '@svgr/hast-util-to-babel-ast': private
  '@svgr/plugin-jsx@6.5.1(@svgr/core@6.5.1)':
    '@svgr/plugin-jsx': private
  '@svgr/plugin-svgo@6.5.1(@svgr/core@6.5.1)':
    '@svgr/plugin-svgo': private
  '@swc/core-darwin-arm64@1.3.96':
    '@swc/core-darwin-arm64': private
  '@swc/core@1.3.96(@swc/helpers@0.5.17)':
    '@swc/core': private
  '@swc/counter@0.1.3':
    '@swc/counter': private
  '@swc/helpers@0.5.17':
    '@swc/helpers': private
  '@swc/types@0.1.23':
    '@swc/types': private
  '@szmarczak/http-timer@5.0.1':
    '@szmarczak/http-timer': private
  '@trysound/sax@0.2.0':
    '@trysound/sax': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/filesystem@0.0.36':
    '@types/filesystem': private
  '@types/filewriter@0.0.33':
    '@types/filewriter': private
  '@types/har-format@1.2.16':
    '@types/har-format': private
  '@types/http-cache-semantics@4.0.4':
    '@types/http-cache-semantics': private
  '@types/parse-json@4.0.2':
    '@types/parse-json': private
  '@types/prop-types@15.7.15':
    '@types/prop-types': private
  '@types/scheduler@0.26.0':
    '@types/scheduler': private
  '@types/trusted-types@2.0.7':
    '@types/trusted-types': private
  '@vue/compiler-core@3.3.4':
    '@vue/compiler-core': private
  '@vue/compiler-dom@3.3.4':
    '@vue/compiler-dom': private
  '@vue/compiler-sfc@3.3.4':
    '@vue/compiler-sfc': private
  '@vue/compiler-ssr@3.3.4':
    '@vue/compiler-ssr': private
  '@vue/reactivity-transform@3.3.4':
    '@vue/reactivity-transform': private
  '@vue/reactivity@3.3.4':
    '@vue/reactivity': private
  '@vue/runtime-core@3.3.4':
    '@vue/runtime-core': private
  '@vue/runtime-dom@3.3.4':
    '@vue/runtime-dom': private
  '@vue/server-renderer@3.3.4(vue@3.3.4)':
    '@vue/server-renderer': private
  '@vue/shared@3.3.4':
    '@vue/shared': private
  abortcontroller-polyfill@1.7.8:
    abortcontroller-polyfill: private
  acorn@8.15.0:
    acorn: private
  ansi-escapes@4.3.2:
    ansi-escapes: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  any-promise@1.3.0:
    any-promise: private
  anymatch@3.1.3:
    anymatch: private
  argparse@2.0.1:
    argparse: private
  aria-query@5.3.2:
    aria-query: private
  array-union@2.1.0:
    array-union: private
  axobject-query@3.2.4:
    axobject-query: private
  balanced-match@1.0.2:
    balanced-match: private
  base-x@3.0.11:
    base-x: private
  base64-js@1.5.1:
    base64-js: private
  binary-extensions@2.3.0:
    binary-extensions: private
  bluebird@3.7.2:
    bluebird: private
  boolbase@1.0.0:
    boolbase: private
  brace-expansion@2.0.2:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.25.1:
    browserslist: private
  buffer@6.0.3:
    buffer: private
  bundle-require@4.2.1(esbuild@0.18.20):
    bundle-require: private
  cac@6.7.14:
    cac: private
  cacheable-lookup@7.0.0:
    cacheable-lookup: private
  cacheable-request@12.0.1:
    cacheable-request: private
  callsites@3.1.0:
    callsites: private
  camelcase@6.3.0:
    camelcase: private
  caniuse-lite@1.0.30001727:
    caniuse-lite: private
  chalk@5.4.1:
    chalk: private
  change-case@5.4.4:
    change-case: private
  chardet@0.7.0:
    chardet: private
  chokidar@4.0.3:
    chokidar: private
  chrome-trace-event@1.0.4:
    chrome-trace-event: private
  cli-width@4.1.0:
    cli-width: private
  clone@2.1.2:
    clone: private
  code-red@1.0.4:
    code-red: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-string@1.9.1:
    color-string: private
  color@4.2.3:
    color: private
  commander@4.1.1:
    commander: private
  config-chain@1.1.13:
    config-chain: private
  content-security-policy-parser@0.4.1:
    content-security-policy-parser: private
  convert-source-map@2.0.0:
    convert-source-map: private
  copy-anything@2.0.6:
    copy-anything: private
  cosmiconfig@7.1.0:
    cosmiconfig: private
  cross-spawn@7.0.6:
    cross-spawn: private
  crypto-random-string@4.0.0:
    crypto-random-string: private
  css-select@4.3.0:
    css-select: private
  css-tree@2.3.1:
    css-tree: private
  css-what@6.2.2:
    css-what: private
  csso@4.2.0:
    csso: private
  csstype@3.1.3:
    csstype: private
  debug@4.4.1:
    debug: private
  decompress-response@6.0.0:
    decompress-response: private
  deep-extend@0.6.0:
    deep-extend: private
  deepmerge@4.3.1:
    deepmerge: private
  defer-to-connect@2.0.1:
    defer-to-connect: private
  detect-libc@1.0.3:
    detect-libc: private
  dir-glob@3.0.1:
    dir-glob: private
  dom-serializer@1.4.1:
    dom-serializer: private
  domelementtype@2.3.0:
    domelementtype: private
  domhandler@4.3.1:
    domhandler: private
  domutils@2.8.0:
    domutils: private
  dotenv-expand@12.0.1:
    dotenv-expand: private
  dotenv@16.4.7:
    dotenv: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  electron-to-chromium@1.5.190:
    electron-to-chromium: private
  emoji-regex@8.0.0:
    emoji-regex: private
  entities@4.5.0:
    entities: private
  env-paths@2.2.1:
    env-paths: private
  errno@0.1.8:
    errno: private
  error-ex@1.3.2:
    error-ex: private
  esbuild@0.18.20:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  estree-walker@2.0.2:
    estree-walker: private
  events@3.3.0:
    events: private
  execa@5.1.1:
    execa: private
  external-editor@3.1.0:
    external-editor: private
  fast-glob@3.3.3:
    fast-glob: private
  fastq@1.19.1:
    fastq: private
  fflate@0.8.2:
    fflate: private
  fill-range@7.1.1:
    fill-range: private
  foreground-child@3.3.1:
    foreground-child: private
  form-data-encoder@4.1.0:
    form-data-encoder: private
  fs-extra@11.1.1:
    fs-extra: private
  fsevents@2.3.3:
    fsevents: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-port@7.1.0:
    get-port: private
  get-stream@9.0.1:
    get-stream: private
  glob-parent@5.1.2:
    glob-parent: private
  glob@10.4.5:
    glob: private
  globals@13.24.0:
    globals: private
  globby@11.1.0:
    globby: private
  got@14.4.6:
    got: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphql-import-macro@1.0.0:
    graphql-import-macro: private
  graphql@15.10.1:
    graphql: private
  has-flag@4.0.0:
    has-flag: private
  htmlnano@2.1.2(postcss@8.5.6)(svgo@2.8.0)(typescript@5.8.2):
    htmlnano: private
  htmlparser2@7.2.0:
    htmlparser2: private
  http-cache-semantics@4.2.0:
    http-cache-semantics: private
  http2-wrapper@2.2.1:
    http2-wrapper: private
  human-signals@2.1.0:
    human-signals: private
  iconv-lite@0.4.24:
    iconv-lite: private
  ieee754@1.2.1:
    ieee754: private
  ignore@7.0.3:
    ignore: private
  image-size@0.5.5:
    image-size: private
  immutable@5.1.3:
    immutable: private
  import-fresh@3.3.1:
    import-fresh: private
  ini@1.3.8:
    ini: private
  inquirer@12.5.0(@types/node@20.11.5):
    inquirer: private
  is-arrayish@0.3.2:
    is-arrayish: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-extglob@2.1.1:
    is-extglob: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-glob@4.0.3:
    is-glob: private
  is-json@2.0.1:
    is-json: private
  is-number@7.0.0:
    is-number: private
  is-path-inside@4.0.0:
    is-path-inside: private
  is-reference@3.0.3:
    is-reference: private
  is-stream@3.0.0:
    is-stream: private
  is-what@3.14.1:
    is-what: private
  isbinaryfile@4.0.10:
    isbinaryfile: private
  isexe@2.0.0:
    isexe: private
  jackspeak@3.4.3:
    jackspeak: private
  joycon@3.1.1:
    joycon: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: private
  json-schema-to-ts@3.1.1:
    json-schema-to-ts: private
  json5@2.2.3:
    json5: private
  jsonfile@6.1.0:
    jsonfile: private
  keyv@4.5.4:
    keyv: private
  ky@1.8.2:
    ky: private
  less@4.4.0:
    less: private
  lightningcss-darwin-arm64@1.21.8:
    lightningcss-darwin-arm64: private
  lightningcss@1.30.1:
    lightningcss: private
  lilconfig@3.1.3:
    lilconfig: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  lmdb@2.7.11:
    lmdb: private
  load-tsconfig@0.2.5:
    load-tsconfig: private
  locate-character@3.0.0:
    locate-character: private
  lodash.sortby@4.7.0:
    lodash.sortby: private
  loose-envify@1.4.0:
    loose-envify: private
  lowercase-keys@3.0.0:
    lowercase-keys: private
  lru-cache@5.1.1:
    lru-cache: private
  magic-string@0.30.17:
    magic-string: private
  make-dir@2.1.0:
    make-dir: private
  mdn-data@2.0.30:
    mdn-data: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  micromatch@4.0.8:
    micromatch: private
  mime@2.6.0:
    mime: private
  mimic-fn@2.1.0:
    mimic-fn: private
  mimic-response@4.0.0:
    mimic-response: private
  minimatch@9.0.5:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass@7.1.2:
    minipass: private
  mnemonic-id@3.2.7:
    mnemonic-id: private
  ms@2.1.3:
    ms: private
  msgpackr-extract@3.0.3:
    msgpackr-extract: private
  msgpackr@1.11.5:
    msgpackr: private
  mute-stream@2.0.0:
    mute-stream: private
  mz@2.7.0:
    mz: private
  nanoid@3.3.11:
    nanoid: private
  needle@3.3.1:
    needle: private
  node-addon-api@7.1.1:
    node-addon-api: private
  node-gyp-build-optional-packages@5.0.6:
    node-gyp-build-optional-packages: private
  node-object-hash@3.1.1:
    node-object-hash: private
  node-releases@2.0.19:
    node-releases: private
  normalize-path@3.0.0:
    normalize-path: private
  normalize-url@8.0.2:
    normalize-url: private
  npm-run-path@4.0.1:
    npm-run-path: private
  nth-check@2.1.1:
    nth-check: private
  nullthrows@1.1.1:
    nullthrows: private
  object-assign@4.1.1:
    object-assign: private
  onetime@5.1.2:
    onetime: private
  ordered-binary@1.6.0:
    ordered-binary: private
  os-tmpdir@1.0.2:
    os-tmpdir: private
  p-cancelable@4.0.1:
    p-cancelable: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  package-json@10.0.1:
    package-json: private
  parent-module@1.0.1:
    parent-module: private
  parse-json@5.2.0:
    parse-json: private
  parse-node-version@1.0.1:
    parse-node-version: private
  path-key@3.1.1:
    path-key: private
  path-scurry@1.11.1:
    path-scurry: private
  path-type@4.0.0:
    path-type: private
  periscopic@3.1.0:
    periscopic: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@2.3.1:
    picomatch: private
  pify@4.0.1:
    pify: private
  pirates@4.0.7:
    pirates: private
  postcss-load-config@4.0.2(postcss@8.5.6):
    postcss-load-config: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  postcss@8.5.6:
    postcss: private
  posthtml-parser@0.10.2:
    posthtml-parser: private
  posthtml-render@3.0.0:
    posthtml-render: private
  posthtml@0.16.6:
    posthtml: private
  process@0.11.10:
    process: private
  proto-list@1.2.4:
    proto-list: private
  prr@1.0.1:
    prr: private
  punycode@2.3.1:
    punycode: private
  queue-microtask@1.2.3:
    queue-microtask: private
  quick-lru@5.1.1:
    quick-lru: private
  rc@1.2.8:
    rc: private
  react-error-overlay@6.0.9:
    react-error-overlay: private
  react-refresh@0.9.0:
    react-refresh: private
  readdirp@3.6.0:
    readdirp: private
  regenerator-runtime@0.13.11:
    regenerator-runtime: private
  registry-auth-token@5.1.0:
    registry-auth-token: private
  registry-url@6.0.1:
    registry-url: private
  resolve-alpn@1.2.1:
    resolve-alpn: private
  resolve-from@5.0.0:
    resolve-from: private
  responselike@3.0.0:
    responselike: private
  reusify@1.1.0:
    reusify: private
  rollup@3.29.5:
    rollup: private
  run-async@3.0.0:
    run-async: private
  run-parallel@1.2.0:
    run-parallel: private
  rxjs@7.8.2:
    rxjs: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safer-buffer@2.1.2:
    safer-buffer: private
  sass@1.89.2:
    sass: private
  sax@1.4.1:
    sax: private
  scheduler@0.23.2:
    scheduler: private
  semver@7.7.2:
    semver: private
  sharp@0.33.5:
    sharp: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  signal-exit@4.1.0:
    signal-exit: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  slash@3.0.0:
    slash: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map@0.6.1:
    source-map: private
  srcset@4.0.0:
    srcset: private
  stable@0.1.8:
    stable: private
  string-width@4.2.3:
    string-width: private
    string-width-cjs: private
  strip-ansi@6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  strip-final-newline@2.0.0:
    strip-final-newline: private
  strip-json-comments@2.0.1:
    strip-json-comments: private
  sucrase@3.35.0:
    sucrase: private
  supports-color@7.2.0:
    supports-color: private
  svelte@4.2.2:
    svelte: private
  svg-parser@2.0.4:
    svg-parser: private
  svgo@2.8.0:
    svgo: private
  temp-dir@3.0.0:
    temp-dir: private
  tempy@3.1.0:
    tempy: private
  thenify-all@1.6.0:
    thenify-all: private
  thenify@3.3.1:
    thenify: private
  tmp@0.0.33:
    tmp: private
  to-regex-range@5.0.1:
    to-regex-range: private
  tr46@1.0.1:
    tr46: private
  tree-kill@1.2.2:
    tree-kill: private
  ts-algebra@2.0.0:
    ts-algebra: private
  ts-interface-checker@0.1.13:
    ts-interface-checker: private
  tslib@2.8.1:
    tslib: private
  tsup@7.2.0(@swc/core@1.13.2(@swc/helpers@0.5.17))(postcss@8.5.6)(typescript@5.2.2):
    tsup: private
  type-fest@4.41.0:
    type-fest: private
  undici-types@5.26.5:
    undici-types: private
  unique-string@3.0.0:
    unique-string: private
  universalify@2.0.1:
    universalify: private
  update-browserslist-db@1.1.3(browserslist@4.22.1):
    update-browserslist-db: private
  utility-types@3.11.0:
    utility-types: private
  vue@3.3.4:
    vue: private
  weak-lru-cache@1.2.2:
    weak-lru-cache: private
  webidl-conversions@4.0.2:
    webidl-conversions: private
  whatwg-url@7.1.0:
    whatwg-url: private
  which@2.0.2:
    which: private
  wrap-ansi@6.2.0:
    wrap-ansi: private
  wrap-ansi@7.0.0:
    wrap-ansi-cjs: private
  xxhash-wasm@0.4.2:
    xxhash-wasm: private
  yallist@3.1.1:
    yallist: private
  yaml@1.10.2:
    yaml: private
  yoctocolors-cjs@2.1.2:
    yoctocolors-cjs: private
ignoredBuilds:
  - '@swc/core'
  - '@parcel/watcher'
  - msgpackr-extract
  - sharp
  - lmdb
  - esbuild
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.12.4
pendingBuilds: []
prunedAt: Wed, 23 Jul 2025 07:23:00 GMT
publicHoistPattern: []
registries:
  '@bangdao': https://npm.bangdao-tech.com/
  '@jsr': https://npm.jsr.io/
  '@lm': http://npm.bangdao-tech.com/
  '@sitease': http://npm.bangdao-tech.com/
  '@smartube': http://npm.bangdao-tech.com/
  default: https://registry.npmmirror.com/
skipped:
  - '@emnapi/runtime@1.4.5'
  - '@esbuild/android-arm64@0.18.20'
  - '@esbuild/android-arm@0.18.20'
  - '@esbuild/android-x64@0.18.20'
  - '@esbuild/darwin-x64@0.18.20'
  - '@esbuild/freebsd-arm64@0.18.20'
  - '@esbuild/freebsd-x64@0.18.20'
  - '@esbuild/linux-arm64@0.18.20'
  - '@esbuild/linux-arm@0.18.20'
  - '@esbuild/linux-ia32@0.18.20'
  - '@esbuild/linux-loong64@0.18.20'
  - '@esbuild/linux-mips64el@0.18.20'
  - '@esbuild/linux-ppc64@0.18.20'
  - '@esbuild/linux-riscv64@0.18.20'
  - '@esbuild/linux-s390x@0.18.20'
  - '@esbuild/linux-x64@0.18.20'
  - '@esbuild/netbsd-x64@0.18.20'
  - '@esbuild/openbsd-x64@0.18.20'
  - '@esbuild/sunos-x64@0.18.20'
  - '@esbuild/win32-arm64@0.18.20'
  - '@esbuild/win32-ia32@0.18.20'
  - '@esbuild/win32-x64@0.18.20'
  - '@img/sharp-darwin-x64@0.33.5'
  - '@img/sharp-libvips-darwin-x64@1.0.4'
  - '@img/sharp-libvips-linux-arm64@1.0.4'
  - '@img/sharp-libvips-linux-arm@1.0.5'
  - '@img/sharp-libvips-linux-s390x@1.0.4'
  - '@img/sharp-libvips-linux-x64@1.0.4'
  - '@img/sharp-libvips-linuxmusl-arm64@1.0.4'
  - '@img/sharp-libvips-linuxmusl-x64@1.0.4'
  - '@img/sharp-linux-arm64@0.33.5'
  - '@img/sharp-linux-arm@0.33.5'
  - '@img/sharp-linux-s390x@0.33.5'
  - '@img/sharp-linux-x64@0.33.5'
  - '@img/sharp-linuxmusl-arm64@0.33.5'
  - '@img/sharp-linuxmusl-x64@0.33.5'
  - '@img/sharp-wasm32@0.33.5'
  - '@img/sharp-win32-ia32@0.33.5'
  - '@img/sharp-win32-x64@0.33.5'
  - '@lmdb/lmdb-darwin-x64@2.5.2'
  - '@lmdb/lmdb-darwin-x64@2.7.11'
  - '@lmdb/lmdb-linux-arm64@2.5.2'
  - '@lmdb/lmdb-linux-arm64@2.7.11'
  - '@lmdb/lmdb-linux-arm@2.5.2'
  - '@lmdb/lmdb-linux-arm@2.7.11'
  - '@lmdb/lmdb-linux-x64@2.5.2'
  - '@lmdb/lmdb-linux-x64@2.7.11'
  - '@lmdb/lmdb-win32-x64@2.5.2'
  - '@lmdb/lmdb-win32-x64@2.7.11'
  - '@msgpackr-extract/msgpackr-extract-darwin-x64@3.0.3'
  - '@msgpackr-extract/msgpackr-extract-linux-arm64@3.0.3'
  - '@msgpackr-extract/msgpackr-extract-linux-arm@3.0.3'
  - '@msgpackr-extract/msgpackr-extract-linux-x64@3.0.3'
  - '@msgpackr-extract/msgpackr-extract-win32-x64@3.0.3'
  - '@parcel/watcher-android-arm64@2.5.1'
  - '@parcel/watcher-darwin-x64@2.5.1'
  - '@parcel/watcher-freebsd-x64@2.5.1'
  - '@parcel/watcher-linux-arm-glibc@2.5.1'
  - '@parcel/watcher-linux-arm-musl@2.5.1'
  - '@parcel/watcher-linux-arm64-glibc@2.5.1'
  - '@parcel/watcher-linux-arm64-musl@2.5.1'
  - '@parcel/watcher-linux-x64-glibc@2.5.1'
  - '@parcel/watcher-linux-x64-musl@2.5.1'
  - '@parcel/watcher-win32-arm64@2.5.1'
  - '@parcel/watcher-win32-ia32@2.5.1'
  - '@parcel/watcher-win32-x64@2.5.1'
  - '@swc/core-darwin-x64@1.13.2'
  - '@swc/core-darwin-x64@1.3.96'
  - '@swc/core-linux-arm-gnueabihf@1.13.2'
  - '@swc/core-linux-arm-gnueabihf@1.3.96'
  - '@swc/core-linux-arm64-gnu@1.13.2'
  - '@swc/core-linux-arm64-gnu@1.3.96'
  - '@swc/core-linux-arm64-musl@1.13.2'
  - '@swc/core-linux-arm64-musl@1.3.96'
  - '@swc/core-linux-x64-gnu@1.13.2'
  - '@swc/core-linux-x64-gnu@1.3.96'
  - '@swc/core-linux-x64-musl@1.13.2'
  - '@swc/core-linux-x64-musl@1.3.96'
  - '@swc/core-win32-arm64-msvc@1.13.2'
  - '@swc/core-win32-arm64-msvc@1.3.96'
  - '@swc/core-win32-ia32-msvc@1.13.2'
  - '@swc/core-win32-ia32-msvc@1.3.96'
  - '@swc/core-win32-x64-msvc@1.13.2'
  - '@swc/core-win32-x64-msvc@1.3.96'
  - lightningcss-darwin-x64@1.21.8
  - lightningcss-darwin-x64@1.30.1
  - lightningcss-freebsd-x64@1.21.8
  - lightningcss-freebsd-x64@1.30.1
  - lightningcss-linux-arm-gnueabihf@1.21.8
  - lightningcss-linux-arm-gnueabihf@1.30.1
  - lightningcss-linux-arm64-gnu@1.21.8
  - lightningcss-linux-arm64-gnu@1.30.1
  - lightningcss-linux-arm64-musl@1.21.8
  - lightningcss-linux-arm64-musl@1.30.1
  - lightningcss-linux-x64-gnu@1.21.8
  - lightningcss-linux-x64-gnu@1.30.1
  - lightningcss-linux-x64-musl@1.21.8
  - lightningcss-linux-x64-musl@1.30.1
  - lightningcss-win32-arm64-msvc@1.30.1
  - lightningcss-win32-x64-msvc@1.21.8
  - lightningcss-win32-x64-msvc@1.30.1
storeDir: /Users/<USER>/Library/pnpm/store/v3/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
