#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Projects/api-interceptor-extension/node_modules/.pnpm/plasmo@0.90.5_@swc+core@1.13.2_@swc+helpers@0.5.17__@swc+helpers@0.5.17_@types+node@20._4446aa2dde4e0504000bd0c6c130ff0f/node_modules/plasmo/bin/node_modules:/Users/<USER>/Projects/api-interceptor-extension/node_modules/.pnpm/plasmo@0.90.5_@swc+core@1.13.2_@swc+helpers@0.5.17__@swc+helpers@0.5.17_@types+node@20._4446aa2dde4e0504000bd0c6c130ff0f/node_modules/plasmo/node_modules:/Users/<USER>/Projects/api-interceptor-extension/node_modules/.pnpm/plasmo@0.90.5_@swc+core@1.13.2_@swc+helpers@0.5.17__@swc+helpers@0.5.17_@types+node@20._4446aa2dde4e0504000bd0c6c130ff0f/node_modules:/Users/<USER>/Projects/api-interceptor-extension/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Projects/api-interceptor-extension/node_modules/.pnpm/plasmo@0.90.5_@swc+core@1.13.2_@swc+helpers@0.5.17__@swc+helpers@0.5.17_@types+node@20._4446aa2dde4e0504000bd0c6c130ff0f/node_modules/plasmo/bin/node_modules:/Users/<USER>/Projects/api-interceptor-extension/node_modules/.pnpm/plasmo@0.90.5_@swc+core@1.13.2_@swc+helpers@0.5.17__@swc+helpers@0.5.17_@types+node@20._4446aa2dde4e0504000bd0c6c130ff0f/node_modules/plasmo/node_modules:/Users/<USER>/Projects/api-interceptor-extension/node_modules/.pnpm/plasmo@0.90.5_@swc+core@1.13.2_@swc+helpers@0.5.17__@swc+helpers@0.5.17_@types+node@20._4446aa2dde4e0504000bd0c6c130ff0f/node_modules:/Users/<USER>/Projects/api-interceptor-extension/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../bin/index.mjs" "$@"
else
  exec node  "$basedir/../../bin/index.mjs" "$@"
fi
