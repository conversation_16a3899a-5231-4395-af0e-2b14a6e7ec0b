#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Projects/api-interceptor-extension/node_modules/.pnpm/json5@2.2.3/node_modules/json5/lib/node_modules:/Users/<USER>/Projects/api-interceptor-extension/node_modules/.pnpm/json5@2.2.3/node_modules/json5/node_modules:/Users/<USER>/Projects/api-interceptor-extension/node_modules/.pnpm/json5@2.2.3/node_modules:/Users/<USER>/Projects/api-interceptor-extension/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Projects/api-interceptor-extension/node_modules/.pnpm/json5@2.2.3/node_modules/json5/lib/node_modules:/Users/<USER>/Projects/api-interceptor-extension/node_modules/.pnpm/json5@2.2.3/node_modules/json5/node_modules:/Users/<USER>/Projects/api-interceptor-extension/node_modules/.pnpm/json5@2.2.3/node_modules:/Users/<USER>/Projects/api-interceptor-extension/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../json5@2.2.3/node_modules/json5/lib/cli.js" "$@"
else
  exec node  "$basedir/../../../../../json5@2.2.3/node_modules/json5/lib/cli.js" "$@"
fi
