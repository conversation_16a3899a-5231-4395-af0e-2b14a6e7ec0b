#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Projects/api-interceptor-extension/node_modules/.pnpm/image-size@0.5.5/node_modules/image-size/bin/node_modules:/Users/<USER>/Projects/api-interceptor-extension/node_modules/.pnpm/image-size@0.5.5/node_modules/image-size/node_modules:/Users/<USER>/Projects/api-interceptor-extension/node_modules/.pnpm/image-size@0.5.5/node_modules:/Users/<USER>/Projects/api-interceptor-extension/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Projects/api-interceptor-extension/node_modules/.pnpm/image-size@0.5.5/node_modules/image-size/bin/node_modules:/Users/<USER>/Projects/api-interceptor-extension/node_modules/.pnpm/image-size@0.5.5/node_modules/image-size/node_modules:/Users/<USER>/Projects/api-interceptor-extension/node_modules/.pnpm/image-size@0.5.5/node_modules:/Users/<USER>/Projects/api-interceptor-extension/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../image-size/bin/image-size.js" "$@"
else
  exec node  "$basedir/../../../image-size/bin/image-size.js" "$@"
fi
