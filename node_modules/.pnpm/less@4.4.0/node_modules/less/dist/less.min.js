/**
 * Less - Leaner CSS v4.4.0
 * http://lesscss.org
 * 
 * Copyright (c) 2009-2025, <PERSON> <<EMAIL>>
 * Licensed under the Apache-2.0 License.
 *
 * @license Apache-2.0
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).less=t()}(this,(function(){"use strict";function e(e){return e.replace(/^[a-z-]+:\/+?[^/]+/,"").replace(/[?&]livereload=\w+/,"").replace(/^\//,"").replace(/\.[a-zA-Z]+$/,"").replace(/[^.\w-]+/g,"-").replace(/\./g,":")}function t(e,t){if(t)for(var n in t.dataset)if(Object.prototype.hasOwnProperty.call(t.dataset,n))if("env"===n||"dumpLineNumbers"===n||"rootpath"===n||"errorReporting"===n)e[n]=t.dataset[n];else try{e[n]=JSON.parse(t.dataset[n])}catch(e){}}var n=function(t,n,i){var r=i.href||"",s="less:".concat(i.title||e(r)),a=t.getElementById(s),o=!1,l=t.createElement("style");l.setAttribute("type","text/css"),i.media&&l.setAttribute("media",i.media),l.id=s,l.styleSheet||(l.appendChild(t.createTextNode(n)),o=null!==a&&a.childNodes.length>0&&l.childNodes.length>0&&a.firstChild.nodeValue===l.firstChild.nodeValue);var u=t.getElementsByTagName("head")[0];if(null===a||!1===o){var c=i&&i.nextSibling||null;c?c.parentNode.insertBefore(l,c):u.appendChild(l)}if(a&&!1===o&&a.parentNode.removeChild(a),l.styleSheet)try{l.styleSheet.cssText=n}catch(e){throw new Error("Couldn't reassign styleSheet.cssText.")}},i=function(e){var t,n=e.document;return n.currentScript||(t=n.getElementsByTagName("script"))[t.length-1]},r={error:function(e){this._fireEvent("error",e)},warn:function(e){this._fireEvent("warn",e)},info:function(e){this._fireEvent("info",e)},debug:function(e){this._fireEvent("debug",e)},addListener:function(e){this._listeners.push(e)},removeListener:function(e){for(var t=0;t<this._listeners.length;t++)if(this._listeners[t]===e)return void this._listeners.splice(t,1)},_fireEvent:function(e,t){for(var n=0;n<this._listeners.length;n++){var i=this._listeners[n][e];i&&i(t)}},_listeners:[]},s=function(){function e(e,t){this.fileManagers=t||[],e=e||{};for(var n=[],i=n.concat(["encodeBase64","mimeLookup","charsetLookup","getSourceMapGenerator"]),r=0;r<i.length;r++){var s=i[r],a=e[s];a?this[s]=a.bind(e):r<n.length&&this.warn("missing required function in environment - ".concat(s))}}return e.prototype.getFileManager=function(e,t,n,i,s){e||r.warn("getFileManager called with no filename.. Please report this issue. continuing."),void 0===t&&r.warn("getFileManager called with null directory.. Please report this issue. continuing.");var a=this.fileManagers;n.pluginManager&&(a=[].concat(a).concat(n.pluginManager.getFileManagers()));for(var o=a.length-1;o>=0;o--){var l=a[o];if(l[s?"supportsSync":"supports"](e,t,n,i))return l}return null},e.prototype.addFileManager=function(e){this.fileManagers.push(e)},e.prototype.clearFileManagers=function(){this.fileManagers=[]},e}(),a={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgrey:"#a9a9a9",darkgreen:"#006400",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",grey:"#808080",green:"#008000",greenyellow:"#adff2f",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgrey:"#d3d3d3",lightgreen:"#90ee90",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370d8",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#d87093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"},o={length:{m:1,cm:.01,mm:.001,in:.0254,px:.0254/96,pt:.0254/72,pc:.0254/72*12},duration:{s:1,ms:.001},angle:{rad:1/(2*Math.PI),deg:1/360,grad:1/400,turn:1}},l={colors:a,unitConversions:o},u=function(){function e(){this.parent=null,this.visibilityBlocks=void 0,this.nodeVisible=void 0,this.rootNode=null,this.parsed=null}return Object.defineProperty(e.prototype,"currentFileInfo",{get:function(){return this.fileInfo()},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"index",{get:function(){return this.getIndex()},enumerable:!1,configurable:!0}),e.prototype.setParent=function(t,n){function i(t){t&&t instanceof e&&(t.parent=n)}Array.isArray(t)?t.forEach(i):i(t)},e.prototype.getIndex=function(){return this._index||this.parent&&this.parent.getIndex()||0},e.prototype.fileInfo=function(){return this._fileInfo||this.parent&&this.parent.fileInfo()||{}},e.prototype.isRulesetLike=function(){return!1},e.prototype.toCSS=function(e){var t=[];return this.genCSS(e,{add:function(e,n,i){t.push(e)},isEmpty:function(){return 0===t.length}}),t.join("")},e.prototype.genCSS=function(e,t){t.add(this.value)},e.prototype.accept=function(e){this.value=e.visit(this.value)},e.prototype.eval=function(){return this},e.prototype._operate=function(e,t,n,i){switch(t){case"+":return n+i;case"-":return n-i;case"*":return n*i;case"/":return n/i}},e.prototype.fround=function(e,t){var n=e&&e.numPrecision;return n?Number((t+2e-16).toFixed(n)):t},e.compare=function(t,n){if(t.compare&&"Quoted"!==n.type&&"Anonymous"!==n.type)return t.compare(n);if(n.compare)return-n.compare(t);if(t.type===n.type){if(t=t.value,n=n.value,!Array.isArray(t))return t===n?0:void 0;if(t.length===n.length){for(var i=0;i<t.length;i++)if(0!==e.compare(t[i],n[i]))return;return 0}}},e.numericCompare=function(e,t){return e<t?-1:e===t?0:e>t?1:void 0},e.prototype.blocksVisibility=function(){return void 0===this.visibilityBlocks&&(this.visibilityBlocks=0),0!==this.visibilityBlocks},e.prototype.addVisibilityBlock=function(){void 0===this.visibilityBlocks&&(this.visibilityBlocks=0),this.visibilityBlocks=this.visibilityBlocks+1},e.prototype.removeVisibilityBlock=function(){void 0===this.visibilityBlocks&&(this.visibilityBlocks=0),this.visibilityBlocks=this.visibilityBlocks-1},e.prototype.ensureVisibility=function(){this.nodeVisible=!0},e.prototype.ensureInvisibility=function(){this.nodeVisible=!1},e.prototype.isVisible=function(){return this.nodeVisible},e.prototype.visibilityInfo=function(){return{visibilityBlocks:this.visibilityBlocks,nodeVisible:this.nodeVisible}},e.prototype.copyVisibilityInfo=function(e){e&&(this.visibilityBlocks=e.visibilityBlocks,this.nodeVisible=e.nodeVisible)},e}(),c=function(e,t,n){var i=this;Array.isArray(e)?this.rgb=e:e.length>=6?(this.rgb=[],e.match(/.{2}/g).map((function(e,t){t<3?i.rgb.push(parseInt(e,16)):i.alpha=parseInt(e,16)/255}))):(this.rgb=[],e.split("").map((function(e,t){t<3?i.rgb.push(parseInt(e+e,16)):i.alpha=parseInt(e+e,16)/255}))),this.alpha=this.alpha||("number"==typeof t?t:1),void 0!==n&&(this.value=n)};function h(e,t){return Math.min(Math.max(e,0),t)}function f(e){return"#".concat(e.map((function(e){return((e=h(Math.round(e),255))<16?"0":"")+e.toString(16)})).join(""))}c.prototype=Object.assign(new u,{type:"Color",luma:function(){var e=this.rgb[0]/255,t=this.rgb[1]/255,n=this.rgb[2]/255;return.2126*(e=e<=.03928?e/12.92:Math.pow((e+.055)/1.055,2.4))+.7152*(t=t<=.03928?t/12.92:Math.pow((t+.055)/1.055,2.4))+.0722*(n=n<=.03928?n/12.92:Math.pow((n+.055)/1.055,2.4))},genCSS:function(e,t){t.add(this.toCSS(e))},toCSS:function(e,t){var n,i,r,s=e&&e.compress&&!t,a=[];if(i=this.fround(e,this.alpha),this.value)if(0===this.value.indexOf("rgb"))i<1&&(r="rgba");else{if(0!==this.value.indexOf("hsl"))return this.value;r=i<1?"hsla":"hsl"}else i<1&&(r="rgba");switch(r){case"rgba":a=this.rgb.map((function(e){return h(Math.round(e),255)})).concat(h(i,1));break;case"hsla":a.push(h(i,1));case"hsl":n=this.toHSL(),a=[this.fround(e,n.h),"".concat(this.fround(e,100*n.s),"%"),"".concat(this.fround(e,100*n.l),"%")].concat(a)}if(r)return"".concat(r,"(").concat(a.join(",".concat(s?"":" ")),")");if(n=this.toRGB(),s){var o=n.split("");o[1]===o[2]&&o[3]===o[4]&&o[5]===o[6]&&(n="#".concat(o[1]).concat(o[3]).concat(o[5]))}return n},operate:function(e,t,n){for(var i=new Array(3),r=this.alpha*(1-n.alpha)+n.alpha,s=0;s<3;s++)i[s]=this._operate(e,t,this.rgb[s],n.rgb[s]);return new c(i,r)},toRGB:function(){return f(this.rgb)},toHSL:function(){var e,t,n=this.rgb[0]/255,i=this.rgb[1]/255,r=this.rgb[2]/255,s=this.alpha,a=Math.max(n,i,r),o=Math.min(n,i,r),l=(a+o)/2,u=a-o;if(a===o)e=t=0;else{switch(t=l>.5?u/(2-a-o):u/(a+o),a){case n:e=(i-r)/u+(i<r?6:0);break;case i:e=(r-n)/u+2;break;case r:e=(n-i)/u+4}e/=6}return{h:360*e,s:t,l:l,a:s}},toHSV:function(){var e,t,n=this.rgb[0]/255,i=this.rgb[1]/255,r=this.rgb[2]/255,s=this.alpha,a=Math.max(n,i,r),o=Math.min(n,i,r),l=a,u=a-o;if(t=0===a?0:u/a,a===o)e=0;else{switch(a){case n:e=(i-r)/u+(i<r?6:0);break;case i:e=(r-n)/u+2;break;case r:e=(n-i)/u+4}e/=6}return{h:360*e,s:t,v:l,a:s}},toARGB:function(){return f([255*this.alpha].concat(this.rgb))},compare:function(e){return e.rgb&&e.rgb[0]===this.rgb[0]&&e.rgb[1]===this.rgb[1]&&e.rgb[2]===this.rgb[2]&&e.alpha===this.alpha?0:void 0}}),c.fromKeyword=function(e){var t,n=e.toLowerCase();if(a.hasOwnProperty(n)?t=new c(a[n].slice(1)):"transparent"===n&&(t=new c([0,0,0],0)),t)return t.value=e,t};var p=function(){return(p=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e}).apply(this,arguments)};"function"==typeof SuppressedError&&SuppressedError;var v=function(e){this.value=e};v.prototype=Object.assign(new u,{type:"Paren",genCSS:function(e,t){t.add("("),this.value.genCSS(e,t),t.add(")")},eval:function(e){return new v(this.value.eval(e))}});var d={"":!0," ":!0,"|":!0},m=function(e){" "===e?(this.value=" ",this.emptyOrWhitespace=!0):(this.value=e?e.trim():"",this.emptyOrWhitespace=""===this.value)};m.prototype=Object.assign(new u,{type:"Combinator",genCSS:function(e,t){var n=e.compress||d[this.value]?"":" ";t.add(n+this.value+n)}});var g=function(e,t,n,i,r,s){this.combinator=e instanceof m?e:new m(e),this.value="string"==typeof t?t.trim():t||"",this.isVariable=n,this._index=i,this._fileInfo=r,this.copyVisibilityInfo(s),this.setParent(this.combinator,this)};g.prototype=Object.assign(new u,{type:"Element",accept:function(e){var t=this.value;this.combinator=e.visit(this.combinator),"object"==typeof t&&(this.value=e.visit(t))},eval:function(e){return new g(this.combinator,this.value.eval?this.value.eval(e):this.value,this.isVariable,this.getIndex(),this.fileInfo(),this.visibilityInfo())},clone:function(){return new g(this.combinator,this.value,this.isVariable,this.getIndex(),this.fileInfo(),this.visibilityInfo())},genCSS:function(e,t){t.add(this.toCSS(e),this.fileInfo(),this.getIndex())},toCSS:function(e){e=e||{};var t=this.value,n=e.firstSelector;return t instanceof v&&(e.firstSelector=!0),t=t.toCSS?t.toCSS(e):t,e.firstSelector=n,""===t&&"&"===this.combinator.value.charAt(0)?"":this.combinator.toCSS(e)+t}});var y={ALWAYS:0,PARENS_DIVISION:1,PARENS:2},b=0,w=1,x=2;function S(e){return Object.prototype.toString.call(e).slice(8,-1)}function I(e){return"Array"===S(e)}function C(e,t={}){if(I(e))return e.map(e=>C(e,t));if("Object"!==S(n=e)||n.constructor!==Object||Object.getPrototypeOf(n)!==Object.prototype)return e;var n;return[...Object.getOwnPropertyNames(e),...Object.getOwnPropertySymbols(e)].reduce((n,i)=>{if(I(t.props)&&!t.props.includes(i))return n;return function(e,t,n,i,r){const s={}.propertyIsEnumerable.call(i,t)?"enumerable":"nonenumerable";"enumerable"===s&&(e[t]=n),r&&"nonenumerable"===s&&Object.defineProperty(e,t,{value:n,enumerable:!1,writable:!0,configurable:!0})}(n,i,C(e[i],t),e,t.nonenumerable),n},{})}function k(e,t){for(var n=e+1,i=null,r=-1;--n>=0&&"\n"!==t.charAt(n);)r++;return"number"==typeof e&&(i=(t.slice(0,e).match(/\n/g)||"").length),{line:i,column:r}}function A(e){var t,n=e.length,i=new Array(n);for(t=0;t<n;t++)i[t]=e[t];return i}function _(e){var t={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t}function P(e,t){var n=t||{};if(!t._defaults){n={};var i=C(e);n._defaults=i;var r=t?C(t):{};Object.assign(n,i,r)}return n}function E(e,t){if(t&&t._defaults)return t;var n=P(e,t);if(n.strictMath&&(n.math=y.PARENS),n.relativeUrls&&(n.rewriteUrls=x),"string"==typeof n.math)switch(n.math.toLowerCase()){case"always":n.math=y.ALWAYS;break;case"parens-division":n.math=y.PARENS_DIVISION;break;case"strict":case"parens":n.math=y.PARENS;break;default:n.math=y.PARENS}if("string"==typeof n.rewriteUrls)switch(n.rewriteUrls.toLowerCase()){case"off":n.rewriteUrls=b;break;case"local":n.rewriteUrls=w;break;case"all":n.rewriteUrls=x}return n}function R(e,t){void 0===t&&(t=[]);for(var n=0,i=e.length;n<i;n++){var r=e[n];Array.isArray(r)?R(r,t):void 0!==r&&t.push(r)}return t}function M(e){return null==e}var O=Object.freeze({__proto__:null,getLocation:k,copyArray:A,clone:_,defaults:P,copyOptions:E,merge:function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},flattenArray:R,isNullOrUndefined:M}),$=/(<anonymous>|Function):(\d+):(\d+)/,V=function(e,t,n){Error.call(this);var i=e.filename||n;if(this.message=e.message,this.stack=e.stack,t&&i){var r=t.contents[i],s=k(e.index,r),a=s.line,o=s.column,l=e.call&&k(e.call,r).line,u=r?r.split("\n"):"";if(this.type=e.type||"Syntax",this.filename=i,this.index=e.index,this.line="number"==typeof a?a+1:null,this.column=o,!this.line&&this.stack){var c=this.stack.match($),h=new Function("a","throw new Error()"),f=0;try{h()}catch(e){var p=e.stack.match($);f=1-parseInt(p[2])}c&&(c[2]&&(this.line=parseInt(c[2])+f),c[3]&&(this.column=parseInt(c[3])))}this.callLine=l+1,this.callExtract=u[l],this.extract=[u[this.line-2],u[this.line-1],u[this.line]]}};if(void 0===Object.create){var F=function(){};F.prototype=Error.prototype,V.prototype=new F}else V.prototype=Object.create(Error.prototype);V.prototype.constructor=V,V.prototype.toString=function(e){var t;e=e||{};var n=(null!==(t=this.type)&&void 0!==t?t:"").toLowerCase().includes("warning"),i=n?this.type:"".concat(this.type,"Error"),r=n?"yellow":"red",s="",a=this.extract||[],o=[],l=function(e){return e};if(e.stylize){var u=typeof e.stylize;if("function"!==u)throw Error("options.stylize should be a function, got a ".concat(u,"!"));l=e.stylize}if(null!==this.line){if(n||"string"!=typeof a[0]||o.push(l("".concat(this.line-1," ").concat(a[0]),"grey")),"string"==typeof a[1]){var c="".concat(this.line," ");a[1]&&(c+=a[1].slice(0,this.column)+l(l(l(a[1].substr(this.column,1),"bold")+a[1].slice(this.column+1),"red"),"inverse")),o.push(c)}n||"string"!=typeof a[2]||o.push(l("".concat(this.line+1," ").concat(a[2]),"grey")),o="".concat(o.join("\n")+l("","reset"),"\n")}return s+=l("".concat(i,": ").concat(this.message),r),this.filename&&(s+=l(" in ",r)+this.filename),this.line&&(s+=l(" on line ".concat(this.line,", column ").concat(this.column+1,":"),"grey")),s+="\n".concat(o),this.callLine&&(s+="".concat(l("from ",r)+(this.filename||""),"/n"),s+="".concat(l(this.callLine,"grey")," ").concat(this.callExtract,"/n")),s};var L={visitDeeper:!0},j=!1;function D(e){return e}var N=function(){function e(e){this._implementation=e,this._visitInCache={},this._visitOutCache={},j||(!function e(t,n){var i,r;for(i in t)switch(typeof(r=t[i])){case"function":r.prototype&&r.prototype.type&&(r.prototype.typeIndex=n++);break;case"object":n=e(r,n)}return n}(He,1),j=!0)}return e.prototype.visit=function(e){if(!e)return e;var t=e.typeIndex;if(!t)return e.value&&e.value.typeIndex&&this.visit(e.value),e;var n,i=this._implementation,r=this._visitInCache[t],s=this._visitOutCache[t],a=L;if(a.visitDeeper=!0,r||(r=i[n="visit".concat(e.type)]||D,s=i["".concat(n,"Out")]||D,this._visitInCache[t]=r,this._visitOutCache[t]=s),r!==D){var o=r.call(i,e,a);e&&i.isReplacing&&(e=o)}if(a.visitDeeper&&e)if(e.length)for(var l=0,u=e.length;l<u;l++)e[l].accept&&e[l].accept(this);else e.accept&&e.accept(this);return s!=D&&s.call(i,e),e},e.prototype.visitArray=function(e,t){if(!e)return e;var n,i=e.length;if(t||!this._implementation.isReplacing){for(n=0;n<i;n++)this.visit(e[n]);return e}var r=[];for(n=0;n<i;n++){var s=this.visit(e[n]);void 0!==s&&(s.splice?s.length&&this.flatten(s,r):r.push(s))}return r},e.prototype.flatten=function(e,t){var n,i,r,s,a,o;for(t||(t=[]),i=0,n=e.length;i<n;i++)if(void 0!==(r=e[i]))if(r.splice)for(a=0,s=r.length;a<s;a++)void 0!==(o=r[a])&&(o.splice?o.length&&this.flatten(o,t):t.push(o));else t.push(r);return t},e}(),B={},U=function(e,t,n){if(e)for(var i=0;i<n.length;i++)Object.prototype.hasOwnProperty.call(e,n[i])&&(t[n[i]]=e[n[i]])},q=["paths","rewriteUrls","rootpath","strictImports","insecure","dumpLineNumbers","compress","syncImport","chunkInput","mime","useFileCache","processImports","pluginManager","quiet"];B.Parse=function(e){U(e,this,q),"string"==typeof this.paths&&(this.paths=[this.paths])};var T=["paths","compress","math","strictUnits","sourceMap","importMultiple","urlArgs","javascriptEnabled","pluginManager","importantScope","rewriteUrls"];function z(e){return!/^(?:[a-z-]+:|\/|#)/i.test(e)}function G(e){return"."===e.charAt(0)}B.Eval=function(e,t){U(e,this,T),"string"==typeof this.paths&&(this.paths=[this.paths]),this.frames=t||[],this.importantScope=this.importantScope||[]},B.Eval.prototype.enterCalc=function(){this.calcStack||(this.calcStack=[]),this.calcStack.push(!0),this.inCalc=!0},B.Eval.prototype.exitCalc=function(){this.calcStack.pop(),this.calcStack.length||(this.inCalc=!1)},B.Eval.prototype.inParenthesis=function(){this.parensStack||(this.parensStack=[]),this.parensStack.push(!0)},B.Eval.prototype.outOfParenthesis=function(){this.parensStack.pop()},B.Eval.prototype.inCalc=!1,B.Eval.prototype.mathOn=!0,B.Eval.prototype.isMathOn=function(e){return!!this.mathOn&&(!!("/"!==e||this.math===y.ALWAYS||this.parensStack&&this.parensStack.length)&&(!(this.math>y.PARENS_DIVISION)||this.parensStack&&this.parensStack.length))},B.Eval.prototype.pathRequiresRewrite=function(e){return(this.rewriteUrls===w?G:z)(e)},B.Eval.prototype.rewritePath=function(e,t){var n;return t=t||"",n=this.normalizePath(t+e),G(e)&&z(t)&&!1===G(n)&&(n="./".concat(n)),n},B.Eval.prototype.normalizePath=function(e){var t,n=e.split("/").reverse();for(e=[];0!==n.length;)switch(t=n.pop()){case".":break;case"..":0===e.length||".."===e[e.length-1]?e.push(t):e.pop();break;default:e.push(t)}return e.join("/")};var W=function(){function e(e){this.imports=[],this.variableImports=[],this._onSequencerEmpty=e,this._currentDepth=0}return e.prototype.addImport=function(e){var t=this,n={callback:e,args:null,isReady:!1};return this.imports.push(n),function(){n.args=Array.prototype.slice.call(arguments,0),n.isReady=!0,t.tryRun()}},e.prototype.addVariableImport=function(e){this.variableImports.push(e)},e.prototype.tryRun=function(){this._currentDepth++;try{for(;;){for(;this.imports.length>0;){var e=this.imports[0];if(!e.isReady)return;this.imports=this.imports.slice(1),e.callback.apply(null,e.args)}if(0===this.variableImports.length)break;var t=this.variableImports[0];this.variableImports=this.variableImports.slice(1),t()}}finally{this._currentDepth--}0===this._currentDepth&&this._onSequencerEmpty&&this._onSequencerEmpty()},e}(),J=function(e,t){this._visitor=new N(this),this._importer=e,this._finish=t,this.context=new B.Eval,this.importCount=0,this.onceFileDetectionMap={},this.recursionDetector={},this._sequencer=new W(this._onSequencerEmpty.bind(this))};J.prototype={isReplacing:!1,run:function(e){try{this._visitor.visit(e)}catch(e){this.error=e}this.isFinished=!0,this._sequencer.tryRun()},_onSequencerEmpty:function(){this.isFinished&&this._finish(this.error)},visitImport:function(e,t){var n=e.options.inline;if(!e.css||n){var i=new B.Eval(this.context,A(this.context.frames)),r=i.frames[0];this.importCount++,e.isVariableImport()?this._sequencer.addVariableImport(this.processImportNode.bind(this,e,i,r)):this.processImportNode(e,i,r)}t.visitDeeper=!1},processImportNode:function(e,t,n){var i,r=e.options.inline;try{i=e.evalForImport(t)}catch(t){t.filename||(t.index=e.getIndex(),t.filename=e.fileInfo().filename),e.css=!0,e.error=t}if(!i||i.css&&!r)this.importCount--,this.isFinished&&this._sequencer.tryRun();else{i.options.multiple&&(t.importMultiple=!0);for(var s=void 0===i.css,a=0;a<n.rules.length;a++)if(n.rules[a]===e){n.rules[a]=i;break}var o=this.onImported.bind(this,i,t),l=this._sequencer.addImport(o);this._importer.push(i.getPath(),s,i.fileInfo(),i.options,l)}},onImported:function(e,t,n,i,r,s){n&&(n.filename||(n.index=e.getIndex(),n.filename=e.fileInfo().filename),this.error=n);var a=this,o=e.options.inline,l=e.options.isPlugin,u=e.options.optional,c=r||s in a.recursionDetector;if(t.importMultiple||(e.skip=!!c||function(){return s in a.onceFileDetectionMap||(a.onceFileDetectionMap[s]=!0,!1)}),!s&&u&&(e.skip=!0),i&&(e.root=i,e.importedFilename=s,!o&&!l&&(t.importMultiple||!c))){a.recursionDetector[s]=!0;var h=this.context;this.context=t;try{this._visitor.visit(i)}catch(n){this.error=n}this.context=h}a.importCount--,a.isFinished&&a._sequencer.tryRun()},visitDeclaration:function(e,t){"DetachedRuleset"===e.value.type?this.context.frames.unshift(e):t.visitDeeper=!1},visitDeclarationOut:function(e){"DetachedRuleset"===e.value.type&&this.context.frames.shift()},visitAtRule:function(e,t){e.value?this.context.frames.unshift(e):e.declarations&&e.declarations.length?e.isRooted?this.context.frames.unshift(e):this.context.frames.unshift(e.declarations[0]):e.rules&&e.rules.length&&this.context.frames.unshift(e)},visitAtRuleOut:function(e){this.context.frames.shift()},visitMixinDefinition:function(e,t){this.context.frames.unshift(e)},visitMixinDefinitionOut:function(e){this.context.frames.shift()},visitRuleset:function(e,t){this.context.frames.unshift(e)},visitRulesetOut:function(e){this.context.frames.shift()},visitMedia:function(e,t){this.context.frames.unshift(e.rules[0])},visitMediaOut:function(e){this.context.frames.shift()}};var H=function(){function e(e){this.visible=e}return e.prototype.run=function(e){this.visit(e)},e.prototype.visitArray=function(e){if(!e)return e;var t,n=e.length;for(t=0;t<n;t++)this.visit(e[t]);return e},e.prototype.visit=function(e){return e?e.constructor===Array?this.visitArray(e):(!e.blocksVisibility||e.blocksVisibility()||(this.visible?e.ensureVisibility():e.ensureInvisibility(),e.accept(this)),e):e},e}(),K=function(){function e(){this._visitor=new N(this),this.contexts=[],this.allExtendsStack=[[]]}return e.prototype.run=function(e){return(e=this._visitor.visit(e)).allExtends=this.allExtendsStack[0],e},e.prototype.visitDeclaration=function(e,t){t.visitDeeper=!1},e.prototype.visitMixinDefinition=function(e,t){t.visitDeeper=!1},e.prototype.visitRuleset=function(e,t){if(!e.root){var n,i,r,s,a=[],o=e.rules,l=o?o.length:0;for(n=0;n<l;n++)e.rules[n]instanceof He.Extend&&(a.push(o[n]),e.extendOnEveryPath=!0);var u=e.paths;for(n=0;n<u.length;n++){var c=u[n],h=c[c.length-1].extendList;for((s=h?A(h).concat(a):a)&&(s=s.map((function(e){return e.clone()}))),i=0;i<s.length;i++)this.foundExtends=!0,(r=s[i]).findSelfSelectors(c),r.ruleset=e,0===i&&(r.firstExtendOnThisSelectorPath=!0),this.allExtendsStack[this.allExtendsStack.length-1].push(r)}this.contexts.push(e.selectors)}},e.prototype.visitRulesetOut=function(e){e.root||(this.contexts.length=this.contexts.length-1)},e.prototype.visitMedia=function(e,t){e.allExtends=[],this.allExtendsStack.push(e.allExtends)},e.prototype.visitMediaOut=function(e){this.allExtendsStack.length=this.allExtendsStack.length-1},e.prototype.visitAtRule=function(e,t){e.allExtends=[],this.allExtendsStack.push(e.allExtends)},e.prototype.visitAtRuleOut=function(e){this.allExtendsStack.length=this.allExtendsStack.length-1},e}(),Q=function(){function e(){this._visitor=new N(this)}return e.prototype.run=function(e){var t=new K;if(this.extendIndices={},t.run(e),!t.foundExtends)return e;e.allExtends=e.allExtends.concat(this.doExtendChaining(e.allExtends,e.allExtends)),this.allExtendsStack=[e.allExtends];var n=this._visitor.visit(e);return this.checkExtendsForNonMatched(e.allExtends),n},e.prototype.checkExtendsForNonMatched=function(e){var t=this.extendIndices;e.filter((function(e){return!e.hasFoundMatches&&1==e.parent_ids.length})).forEach((function(e){var n="_unknown_";try{n=e.selector.toCSS({})}catch(e){}t["".concat(e.index," ").concat(n)]||(t["".concat(e.index," ").concat(n)]=!0,r.warn("WARNING: extend '".concat(n,"' has no matches")))}))},e.prototype.doExtendChaining=function(e,t,n){var i,r,s,a,o,l,u,c,h=[],f=this;for(n=n||0,i=0;i<e.length;i++)for(r=0;r<t.length;r++)l=e[i],u=t[r],l.parent_ids.indexOf(u.object_id)>=0||(o=[u.selfSelectors[0]],(s=f.findMatch(l,o)).length&&(l.hasFoundMatches=!0,l.selfSelectors.forEach((function(e){var t=u.visibilityInfo();a=f.extendSelector(s,o,e,l.isVisible()),(c=new He.Extend(u.selector,u.option,0,u.fileInfo(),t)).selfSelectors=a,a[a.length-1].extendList=[c],h.push(c),c.ruleset=u.ruleset,c.parent_ids=c.parent_ids.concat(u.parent_ids,l.parent_ids),u.firstExtendOnThisSelectorPath&&(c.firstExtendOnThisSelectorPath=!0,u.ruleset.paths.push(a))}))));if(h.length){if(this.extendChainCount++,n>100){var p="{unable to calculate}",v="{unable to calculate}";try{p=h[0].selfSelectors[0].toCSS(),v=h[0].selector.toCSS()}catch(e){}throw{message:"extend circular reference detected. One of the circular extends is currently:".concat(p,":extend(").concat(v,")")}}return h.concat(f.doExtendChaining(h,t,n+1))}return h},e.prototype.visitDeclaration=function(e,t){t.visitDeeper=!1},e.prototype.visitMixinDefinition=function(e,t){t.visitDeeper=!1},e.prototype.visitSelector=function(e,t){t.visitDeeper=!1},e.prototype.visitRuleset=function(e,t){if(!e.root){var n,i,r,s,a=this.allExtendsStack[this.allExtendsStack.length-1],o=[],l=this;for(r=0;r<a.length;r++)for(i=0;i<e.paths.length;i++)if(s=e.paths[i],!e.extendOnEveryPath){var u=s[s.length-1].extendList;u&&u.length||(n=this.findMatch(a[r],s)).length&&(a[r].hasFoundMatches=!0,a[r].selfSelectors.forEach((function(e){var t;t=l.extendSelector(n,s,e,a[r].isVisible()),o.push(t)})))}e.paths=e.paths.concat(o)}},e.prototype.findMatch=function(e,t){var n,i,r,s,a,o,l,u=e.selector.elements,c=[],h=[];for(n=0;n<t.length;n++)for(i=t[n],r=0;r<i.elements.length;r++)for(s=i.elements[r],(e.allowBefore||0===n&&0===r)&&c.push({pathIndex:n,index:r,matched:0,initialCombinator:s.combinator}),o=0;o<c.length;o++)l=c[o],""===(a=s.combinator.value)&&0===r&&(a=" "),!this.isElementValuesEqual(u[l.matched].value,s.value)||l.matched>0&&u[l.matched].combinator.value!==a?l=null:l.matched++,l&&(l.finished=l.matched===u.length,l.finished&&!e.allowAfter&&(r+1<i.elements.length||n+1<t.length)&&(l=null)),l?l.finished&&(l.length=u.length,l.endPathIndex=n,l.endPathElementIndex=r+1,c.length=0,h.push(l)):(c.splice(o,1),o--);return h},e.prototype.isElementValuesEqual=function(e,t){if("string"==typeof e||"string"==typeof t)return e===t;if(e instanceof He.Attribute)return e.op===t.op&&e.key===t.key&&(e.value&&t.value?(e=e.value.value||e.value)===(t=t.value.value||t.value):!e.value&&!t.value);if(e=e.value,t=t.value,e instanceof He.Selector){if(!(t instanceof He.Selector)||e.elements.length!==t.elements.length)return!1;for(var n=0;n<e.elements.length;n++){if(e.elements[n].combinator.value!==t.elements[n].combinator.value&&(0!==n||(e.elements[n].combinator.value||" ")!==(t.elements[n].combinator.value||" ")))return!1;if(!this.isElementValuesEqual(e.elements[n].value,t.elements[n].value))return!1}return!0}return!1},e.prototype.extendSelector=function(e,t,n,i){var r,s,a,o,l,u=0,c=0,h=[];for(r=0;r<e.length;r++)s=t[(o=e[r]).pathIndex],a=new He.Element(o.initialCombinator,n.elements[0].value,n.elements[0].isVariable,n.elements[0].getIndex(),n.elements[0].fileInfo()),o.pathIndex>u&&c>0&&(h[h.length-1].elements=h[h.length-1].elements.concat(t[u].elements.slice(c)),c=0,u++),l=s.elements.slice(c,o.index).concat([a]).concat(n.elements.slice(1)),u===o.pathIndex&&r>0?h[h.length-1].elements=h[h.length-1].elements.concat(l):(h=h.concat(t.slice(u,o.pathIndex))).push(new He.Selector(l)),u=o.endPathIndex,(c=o.endPathElementIndex)>=t[u].elements.length&&(c=0,u++);return u<t.length&&c>0&&(h[h.length-1].elements=h[h.length-1].elements.concat(t[u].elements.slice(c)),u++),h=(h=h.concat(t.slice(u,t.length))).map((function(e){var t=e.createDerived(e.elements);return i?t.ensureVisibility():t.ensureInvisibility(),t}))},e.prototype.visitMedia=function(e,t){var n=e.allExtends.concat(this.allExtendsStack[this.allExtendsStack.length-1]);n=n.concat(this.doExtendChaining(n,e.allExtends)),this.allExtendsStack.push(n)},e.prototype.visitMediaOut=function(e){var t=this.allExtendsStack.length-1;this.allExtendsStack.length=t},e.prototype.visitAtRule=function(e,t){var n=e.allExtends.concat(this.allExtendsStack[this.allExtendsStack.length-1]);n=n.concat(this.doExtendChaining(n,e.allExtends)),this.allExtendsStack.push(n)},e.prototype.visitAtRuleOut=function(e){var t=this.allExtendsStack.length-1;this.allExtendsStack.length=t},e}(),Z=function(){function e(){this.contexts=[[]],this._visitor=new N(this)}return e.prototype.run=function(e){return this._visitor.visit(e)},e.prototype.visitDeclaration=function(e,t){t.visitDeeper=!1},e.prototype.visitMixinDefinition=function(e,t){t.visitDeeper=!1},e.prototype.visitRuleset=function(e,t){var n,i=this.contexts[this.contexts.length-1],r=[];this.contexts.push(r),e.root||((n=e.selectors)&&(n=n.filter((function(e){return e.getIsOutput()})),e.selectors=n.length?n:n=null,n&&e.joinSelectors(r,i,n)),n||(e.rules=null),e.paths=r)},e.prototype.visitRulesetOut=function(e){this.contexts.length=this.contexts.length-1},e.prototype.visitMedia=function(e,t){var n=this.contexts[this.contexts.length-1];e.rules[0].root=0===n.length||n[0].multiMedia},e.prototype.visitAtRule=function(e,t){var n=this.contexts[this.contexts.length-1];e.declarations&&e.declarations.length?e.declarations[0].root=0===n.length||n[0].multiMedia:e.rules&&e.rules.length&&(e.rules[0].root=e.isRooted||0===n.length||null)},e}(),X=function(){function e(e){this._visitor=new N(this),this._context=e}return e.prototype.containsSilentNonBlockedChild=function(e){var t;if(!e)return!1;for(var n=0;n<e.length;n++)if((t=e[n]).isSilent&&t.isSilent(this._context)&&!t.blocksVisibility())return!0;return!1},e.prototype.keepOnlyVisibleChilds=function(e){e&&e.rules&&(e.rules=e.rules.filter((function(e){return e.isVisible()})))},e.prototype.isEmpty=function(e){return!e||!e.rules||0===e.rules.length},e.prototype.hasVisibleSelector=function(e){return!(!e||!e.paths)&&e.paths.length>0},e.prototype.resolveVisibility=function(e){if(!e.blocksVisibility()){if(this.isEmpty(e))return;return e}var t=e.rules[0];if(this.keepOnlyVisibleChilds(t),!this.isEmpty(t))return e.ensureVisibility(),e.removeVisibilityBlock(),e},e.prototype.isVisibleRuleset=function(e){return!!e.firstRoot||!this.isEmpty(e)&&!(!e.root&&!this.hasVisibleSelector(e))},e}(),Y=function(e){this._visitor=new N(this),this._context=e,this.utils=new X(e)};Y.prototype={isReplacing:!0,run:function(e){return this._visitor.visit(e)},visitDeclaration:function(e,t){if(!e.blocksVisibility()&&!e.variable)return e},visitMixinDefinition:function(e,t){e.frames=[]},visitExtend:function(e,t){},visitComment:function(e,t){if(!e.blocksVisibility()&&!e.isSilent(this._context))return e},visitMedia:function(e,t){var n=e.rules[0].rules;return e.accept(this._visitor),t.visitDeeper=!1,this.utils.resolveVisibility(e,n)},visitImport:function(e,t){if(!e.blocksVisibility())return e},visitAtRule:function(e,t){return e.rules&&e.rules.length?this.visitAtRuleWithBody(e,t):this.visitAtRuleWithoutBody(e,t)},visitAnonymous:function(e,t){if(!e.blocksVisibility())return e.accept(this._visitor),e},visitAtRuleWithBody:function(e,t){var n=function(e){var t=e.rules;return function(e){var t=e.rules;return 1===t.length&&(!t[0].paths||0===t[0].paths.length)}(e)?t[0].rules:t}(e);return e.accept(this._visitor),t.visitDeeper=!1,this.utils.isEmpty(e)||this._mergeRules(e.rules[0].rules),this.utils.resolveVisibility(e,n)},visitAtRuleWithoutBody:function(e,t){if(!e.blocksVisibility()){if("@charset"===e.name){if(this.charset){if(e.debugInfo){var n=new He.Comment("/* ".concat(e.toCSS(this._context).replace(/\n/g,"")," */\n"));return n.debugInfo=e.debugInfo,this._visitor.visit(n)}return}this.charset=!0}return e}},checkValidNodes:function(e,t){if(e)for(var n=0;n<e.length;n++){var i=e[n];if(t&&i instanceof He.Declaration&&!i.variable)throw{message:"Properties must be inside selector blocks. They cannot be in the root",index:i.getIndex(),filename:i.fileInfo()&&i.fileInfo().filename};if(i instanceof He.Call)throw{message:"Function '".concat(i.name,"' did not return a root node"),index:i.getIndex(),filename:i.fileInfo()&&i.fileInfo().filename};if(i.type&&!i.allowRoot)throw{message:"".concat(i.type," node returned by a function is not valid here"),index:i.getIndex(),filename:i.fileInfo()&&i.fileInfo().filename}}},visitRuleset:function(e,t){var n,i=[];if(this.checkValidNodes(e.rules,e.firstRoot),e.root)e.accept(this._visitor),t.visitDeeper=!1;else{this._compileRulesetPaths(e);for(var r=e.rules,s=r?r.length:0,a=0;a<s;)(n=r[a])&&n.rules?(i.push(this._visitor.visit(n)),r.splice(a,1),s--):a++;s>0?e.accept(this._visitor):e.rules=null,t.visitDeeper=!1}return e.rules&&(this._mergeRules(e.rules),this._removeDuplicateRules(e.rules)),this.utils.isVisibleRuleset(e)&&(e.ensureVisibility(),i.splice(0,0,e)),1===i.length?i[0]:i},_compileRulesetPaths:function(e){e.paths&&(e.paths=e.paths.filter((function(e){var t;for(" "===e[0].elements[0].combinator.value&&(e[0].elements[0].combinator=new He.Combinator("")),t=0;t<e.length;t++)if(e[t].isVisible()&&e[t].getIsOutput())return!0;return!1})))},_removeDuplicateRules:function(e){if(e){var t,n,i,r={};for(i=e.length-1;i>=0;i--)if((n=e[i])instanceof He.Declaration)if(r[n.name]){(t=r[n.name])instanceof He.Declaration&&(t=r[n.name]=[r[n.name].toCSS(this._context)]);var s=n.toCSS(this._context);-1!==t.indexOf(s)?e.splice(i,1):t.push(s)}else r[n.name]=n}},_mergeRules:function(e){if(e){for(var t={},n=[],i=0;i<e.length;i++){var r=e[i];if(r.merge){var s=r.name;t[s]?e.splice(i--,1):n.push(t[s]=[]),t[s].push(r)}}n.forEach((function(e){if(e.length>0){var t=e[0],n=[],i=[new He.Expression(n)];e.forEach((function(e){"+"===e.merge&&n.length>0&&i.push(new He.Expression(n=[])),n.push(e.value),t.important=t.important||e.important})),t.value=new He.Value(i)}}))}}};var ee={Visitor:N,ImportVisitor:J,MarkVisibleSelectorsVisitor:H,ExtendVisitor:Q,JoinSelectorVisitor:Z,ToCSSVisitor:Y};var te=function(){var e,t,n,i,r,s,a,o=[],l={};function u(n){for(var i,o,c,h=l.i,f=t,p=l.i-a,v=l.i+s.length-p,d=l.i+=n,m=e;l.i<v;l.i++){if(i=m.charCodeAt(l.i),l.autoCommentAbsorb&&47===i){if("/"===(o=m.charAt(l.i+1))){c={index:l.i,isLineComment:!0};var g=m.indexOf("\n",l.i+2);g<0&&(g=v),l.i=g,c.text=m.substr(c.index,l.i-c.index),l.commentStore.push(c);continue}if("*"===o){var y=m.indexOf("*/",l.i+2);if(y>=0){c={index:l.i,text:m.substr(l.i,y+2-l.i),isLineComment:!1},l.i+=c.text.length-1,l.commentStore.push(c);continue}}break}if(32!==i&&10!==i&&9!==i&&13!==i)break}if(s=s.slice(n+l.i-d+p),a=l.i,!s.length){if(t<r.length-1)return s=r[++t],u(0),!0;l.finished=!0}return h!==l.i||f!==t}return l.save=function(){a=l.i,o.push({current:s,i:l.i,j:t})},l.restore=function(e){(l.i>n||l.i===n&&e&&!i)&&(n=l.i,i=e);var r=o.pop();s=r.current,a=l.i=r.i,t=r.j},l.forget=function(){o.pop()},l.isWhitespace=function(t){var n=l.i+(t||0),i=e.charCodeAt(n);return 32===i||13===i||9===i||10===i},l.$re=function(e){l.i>a&&(s=s.slice(l.i-a),a=l.i);var t=e.exec(s);return t?(u(t[0].length),"string"==typeof t?t:1===t.length?t[0]:t):null},l.$char=function(t){return e.charAt(l.i)!==t?null:(u(1),t)},l.$peekChar=function(t){return e.charAt(l.i)!==t?null:t},l.$str=function(t){for(var n=t.length,i=0;i<n;i++)if(e.charAt(l.i+i)!==t.charAt(i))return null;return u(n),t},l.$quoted=function(t){var n=t||l.i,i=e.charAt(n);if("'"===i||'"'===i){for(var r=e.length,s=n,a=1;a+s<r;a++){switch(e.charAt(a+s)){case"\\":a++;continue;case"\r":case"\n":break;case i:var o=e.substr(s,a+1);return t||0===t?[i,o]:(u(a+1),o)}}return null}},l.$parseUntil=function(t){var n,i="",r=null,s=!1,a=0,o=[],c=[],h=e.length,f=l.i,p=l.i,v=l.i,d=!0;n="string"==typeof t?function(e){return e===t}:function(e){return t.test(e)};do{var m=e.charAt(v);if(0===a&&n(m))(r=e.substr(p,v-p))?c.push(r):c.push(" "),r=c,u(v-f),d=!1;else{if(s){"*"===m&&"/"===e.charAt(v+1)&&(v++,a--,s=!1),v++;continue}switch(m){case"\\":v++,m=e.charAt(v),c.push(e.substr(p,v-p+1)),p=v+1;break;case"/":"*"===e.charAt(v+1)&&(v++,s=!0,a++);break;case"'":case'"':(i=l.$quoted(v))?(c.push(e.substr(p,v-p),i),p=(v+=i[1].length-1)+1):(u(v-f),r=m,d=!1);break;case"{":o.push("}"),a++;break;case"(":o.push(")"),a++;break;case"[":o.push("]"),a++;break;case"}":case")":case"]":var g=o.pop();m===g?a--:(u(v-f),r=g,d=!1)}++v>h&&(d=!1)}}while(d);return r||null},l.autoCommentAbsorb=!0,l.commentStore=[],l.finished=!1,l.peek=function(t){if("string"==typeof t){for(var n=0;n<t.length;n++)if(e.charAt(l.i+n)!==t.charAt(n))return!1;return!0}return t.test(s)},l.peekChar=function(t){return e.charAt(l.i)===t},l.currentChar=function(){return e.charAt(l.i)},l.prevChar=function(){return e.charAt(l.i-1)},l.getInput=function(){return e},l.peekNotNumeric=function(){var t=e.charCodeAt(l.i);return t>57||t<43||47===t||44===t},l.start=function(i,o,c){e=i,l.i=t=a=n=0,r=o?function(e,t){var n,i,r,s,a,o,l,u,c,h=e.length,f=0,p=0,v=[],d=0;function m(t){var n=a-d;n<512&&!t||!n||(v.push(e.slice(d,a+1)),d=a+1)}for(a=0;a<h;a++)if(!((l=e.charCodeAt(a))>=97&&l<=122||l<34))switch(l){case 40:p++,i=a;continue;case 41:if(--p<0)return t("missing opening `(`",a);continue;case 59:p||m();continue;case 123:f++,n=a;continue;case 125:if(--f<0)return t("missing opening `{`",a);f||p||m();continue;case 92:if(a<h-1){a++;continue}return t("unescaped `\\`",a);case 34:case 39:case 96:for(c=0,o=a,a+=1;a<h;a++)if(!((u=e.charCodeAt(a))>96)){if(u==l){c=1;break}if(92==u){if(a==h-1)return t("unescaped `\\`",a);a++}}if(c)continue;return t("unmatched `".concat(String.fromCharCode(l),"`"),o);case 47:if(p||a==h-1)continue;if(47==(u=e.charCodeAt(a+1)))for(a+=2;a<h&&(!((u=e.charCodeAt(a))<=13)||10!=u&&13!=u);a++);else if(42==u){for(r=o=a,a+=2;a<h-1&&(125==(u=e.charCodeAt(a))&&(s=a),42!=u||47!=e.charCodeAt(a+1));a++);if(a==h-1)return t("missing closing `*/`",o);a++}continue;case 42:if(a<h-1&&47==e.charCodeAt(a+1))return t("unmatched `/*`",a);continue}return 0!==f?t(r>n&&s>r?"missing closing `}` or `*/`":"missing closing `}`",n):0!==p?t("missing closing `)`",i):(m(!0),v)}(i,c):[i],s=r[0],u(0)},l.end=function(){var t,r=l.i>=e.length;return l.i<n&&(t=i,l.i=n),{isFinished:r,furthest:l.i,furthestPossibleErrorMessage:t,furthestReachedEnd:l.i>=e.length-1,furthestChar:e[l.i]}},l};var ne=function e(t){return{_data:{},add:function(e,t){e=e.toLowerCase(),this._data.hasOwnProperty(e),this._data[e]=t},addMultiple:function(e){var t=this;Object.keys(e).forEach((function(n){t.add(n,e[n])}))},get:function(e){return this._data[e]||t&&t.get(e)},getLocalFunctions:function(){return this._data},inherit:function(){return e(this)},create:function(t){return e(t)}}}(null),ie={queryInParens:!0},re={queryInParens:!0},se=function(e,t,n,i,r,s){this.value=e,this._index=t,this._fileInfo=n,this.mapLines=i,this.rulesetLike=void 0!==r&&r,this.allowRoot=!0,this.copyVisibilityInfo(s)};se.prototype=Object.assign(new u,{type:"Anonymous",eval:function(){return new se(this.value,this._index,this._fileInfo,this.mapLines,this.rulesetLike,this.visibilityInfo())},compare:function(e){return e.toCSS&&this.toCSS()===e.toCSS()?0:void 0},isRulesetLike:function(){return this.rulesetLike},genCSS:function(e,t){this.nodeVisible=Boolean(this.value),this.nodeVisible&&t.add(this.value,this._fileInfo,this._index,this.mapLines)}});var ae=function e(t,n,i,s){var a;s=s||0;var o=te();function l(e,t){throw new V({index:o.i,filename:i.filename,type:t||"Syntax",message:e},n)}function u(e,s,a){t.quiet||r.warn(new V({index:null!=s?s:o.i,filename:i.filename,type:a?"".concat(a.toUpperCase()," WARNING"):"WARNING",message:e},n).toString())}function c(e,t){var n=e instanceof Function?e.call(a):o.$re(e);if(n)return n;l(t||("string"==typeof e?"expected '".concat(e,"' got '").concat(o.currentChar(),"'"):"unexpected token"))}function h(e,t){if(o.$char(e))return e;l(t||"expected '".concat(e,"' got '").concat(o.currentChar(),"'"))}function f(e){var t=i.filename;return{lineNumber:k(e,o.getInput()).line+1,fileName:t}}return{parserInput:o,imports:n,fileInfo:i,parseNode:function(e,t,r){var l,u=[],c=o;try{c.start(e,!1,(function(e,t){r({message:e,index:t+s})}));for(var h=0,f=void 0;f=t[h];h++)l=a[f](),u.push(l||null);c.end().isFinished?r(null,u):r(!0,null)}catch(e){throw new V({index:e.index+s,message:e.message},n,i.filename)}},parse:function(r,s,u){var c,h,f,p,v=null,d="";if(u&&u.disablePluginRule&&(a.plugin=function(){o.$re(/^@plugin?\s+/)&&l("@plugin statements are not allowed when disablePluginRule is set to true")}),h=u&&u.globalVars?"".concat(e.serializeVars(u.globalVars),"\n"):"",f=u&&u.modifyVars?"\n".concat(e.serializeVars(u.modifyVars)):"",t.pluginManager)for(var m=t.pluginManager.getPreProcessors(),g=0;g<m.length;g++)r=m[g].process(r,{context:t,imports:n,fileInfo:i});(h||u&&u.banner)&&(d=(u&&u.banner?u.banner:"")+h,(p=n.contentsIgnoredChars)[i.filename]=p[i.filename]||0,p[i.filename]+=d.length),r=d+(r=r.replace(/\r\n?/g,"\n")).replace(/^\uFEFF/,"")+f,n.contents[i.filename]=r;try{o.start(r,t.chunkInput,(function(e,t){throw new V({index:t,type:"Parse",message:e,filename:i.filename},n)})),He.Node.prototype.parse=this,c=new He.Ruleset(null,this.parsers.primary()),He.Node.prototype.rootNode=c,c.root=!0,c.firstRoot=!0,c.functionRegistry=ne.inherit()}catch(e){return s(new V(e,n,i.filename))}var y=o.end();if(!y.isFinished){var b=y.furthestPossibleErrorMessage;b||(b="Unrecognised input","}"===y.furthestChar?b+=". Possibly missing opening '{'":")"===y.furthestChar?b+=". Possibly missing opening '('":y.furthestReachedEnd&&(b+=". Possibly missing something")),v=new V({type:"Parse",message:b,index:y.furthest,filename:i.filename},n)}var w=function(e){return(e=v||e||n.error)?(e instanceof V||(e=new V(e,n,i.filename)),s(e)):s(null,c)};if(!1===t.processImports)return w();new ee.ImportVisitor(n,w).run(c)},parsers:a={primary:function(){for(var e,t=this.mixin,n=[];;){for(;e=this.comment();)n.push(e);if(o.finished)break;if(o.peek("}"))break;if(e=this.extendRule())n=n.concat(e);else if(e=t.definition()||this.declaration()||t.call(!1,!1)||this.ruleset()||this.variableCall()||this.entities.call()||this.atrule())n.push(e);else{for(var i=!1;o.$char(";");)i=!0;if(!i)break}}return n},comment:function(){if(o.commentStore.length){var e=o.commentStore.shift();return new He.Comment(e.text,e.isLineComment,e.index+s,i)}},entities:{mixinLookup:function(){return a.mixin.call(!0,!0)},quoted:function(e){var t,n=o.i,r=!1;if(o.save(),o.$char("~"))r=!0;else if(e)return void o.restore();if(t=o.$quoted())return o.forget(),new He.Quoted(t.charAt(0),t.substr(1,t.length-2),r,n+s,i);o.restore()},keyword:function(){var e=o.$char("%")||o.$re(/^\[?(?:[\w-]|\\(?:[A-Fa-f0-9]{1,6} ?|[^A-Fa-f0-9]))+\]?/);if(e)return He.Color.fromKeyword(e)||new He.Keyword(e)},call:function(){var e,t,n,r=o.i;if(!o.peek(/^url\(/i))if(o.save(),e=o.$re(/^([\w-]+|%|~|progid:[\w.]+)\(/)){if(e=e[1],(n=this.customFuncCall(e))&&(t=n.parse())&&n.stop)return o.forget(),t;if(t=this.arguments(t),o.$char(")"))return o.forget(),new He.Call(e,t,r+s,i);o.restore("Could not parse call arguments or missing ')'")}else o.forget()},declarationCall:function(){var e,t,n=o.i;if(o.save(),e=o.$re(/^[\w]+\(/)){e=e.substring(0,e.length-1);var r,a=this.ruleProperty();if(a&&(r=this.value()),a&&r&&(t=[new He.Declaration(a,r,null,null,o.i+s,i,!0)]),o.$char(")"))return o.forget(),new He.Call(e,t,n+s,i);o.restore("Could not parse call arguments or missing ')'")}else o.forget()},customFuncCall:function(e){return{alpha:t(a.ieAlpha,!0),boolean:t(n),if:t(n)}[e.toLowerCase()];function t(e,t){return{parse:e,stop:t}}function n(){return[c(a.condition,"expected condition")]}},arguments:function(e){var t,n,i=e||[],r=[];for(o.save();;){if(e)e=!1;else{if(!(n=a.detachedRuleset()||this.assignment()||a.expression()))break;n.value&&1==n.value.length&&(n=n.value[0]),i.push(n)}o.$char(",")||(o.$char(";")||t)&&(t=!0,n=i.length<1?i[0]:new He.Value(i),r.push(n),i=[])}return o.forget(),t?r:i},literal:function(){return this.dimension()||this.color()||this.quoted()||this.unicodeDescriptor()},assignment:function(){var e,t;if(o.save(),e=o.$re(/^\w+(?=\s?=)/i))if(o.$char("=")){if(t=a.entity())return o.forget(),new He.Assignment(e,t);o.restore()}else o.restore();else o.restore()},url:function(){var e,t=o.i;if(o.autoCommentAbsorb=!1,o.$str("url("))return e=this.quoted()||this.variable()||this.property()||o.$re(/^(?:(?:\\[()'"])|[^()'"])+/)||"",o.autoCommentAbsorb=!0,h(")"),new He.URL(void 0!==e.value||e instanceof He.Variable||e instanceof He.Property?e:new He.Anonymous(e,t),t+s,i);o.autoCommentAbsorb=!0},variable:function(){var e,t,n=o.i;if(o.save(),"@"===o.currentChar()&&(t=o.$re(/^@@?[\w-]+/))){if("("===(e=o.currentChar())||"["===e&&!o.prevChar().match(/^\s/)){var r=a.variableCall(t);if(r)return o.forget(),r}return o.forget(),new He.Variable(t,n+s,i)}o.restore()},variableCurly:function(){var e,t=o.i;if("@"===o.currentChar()&&(e=o.$re(/^@\{([\w-]+)\}/)))return new He.Variable("@".concat(e[1]),t+s,i)},property:function(){var e,t=o.i;if("$"===o.currentChar()&&(e=o.$re(/^\$[\w-]+/)))return new He.Property(e,t+s,i)},propertyCurly:function(){var e,t=o.i;if("$"===o.currentChar()&&(e=o.$re(/^\$\{([\w-]+)\}/)))return new He.Property("$".concat(e[1]),t+s,i)},color:function(){var e;if(o.save(),"#"===o.currentChar()&&(e=o.$re(/^#([A-Fa-f0-9]{8}|[A-Fa-f0-9]{6}|[A-Fa-f0-9]{3,4})([\w.#[])?/))&&!e[2])return o.forget(),new He.Color(e[1],void 0,e[0]);o.restore()},colorKeyword:function(){o.save();var e=o.autoCommentAbsorb;o.autoCommentAbsorb=!1;var t=o.$re(/^[_A-Za-z-][_A-Za-z0-9-]+/);if(o.autoCommentAbsorb=e,t){o.restore();var n=He.Color.fromKeyword(t);return n?(o.$str(t),n):void 0}o.forget()},dimension:function(){if(!o.peekNotNumeric()){var e=o.$re(/^([+-]?\d*\.?\d+)(%|[a-z_]+)?/i);return e?new He.Dimension(e[1],e[2]):void 0}},unicodeDescriptor:function(){var e;if(e=o.$re(/^U\+[0-9a-fA-F?]+(-[0-9a-fA-F?]+)?/))return new He.UnicodeDescriptor(e[0])},javascript:function(){var e,t=o.i;o.save();var n=o.$char("~");if(o.$char("`")){if(e=o.$re(/^[^`]*`/))return o.forget(),new He.JavaScript(e.substr(0,e.length-1),Boolean(n),t+s,i);o.restore("invalid javascript definition")}else o.restore()}},variable:function(){var e;if("@"===o.currentChar()&&(e=o.$re(/^(@[\w-]+)\s*:/)))return e[1]},variableCall:function(e){var t,n=o.i,r=!!e,s=e;if(o.save(),s||"@"===o.currentChar()&&(s=o.$re(/^(@[\w-]+)(\(\s*\))?/))){if(!(t=this.mixin.ruleLookups())&&(r&&"()"!==o.$str("()")||"()"!==s[2]))return void o.restore("Missing '[...]' lookup in variable call");r||(s=s[1]);var l=new He.VariableCall(s,n,i);return!r&&a.end()?(o.forget(),l):(o.forget(),new He.NamespaceValue(l,t,n,i))}o.restore()},extend:function(e){var t,n,r,a,h,f=o.i;if(o.$str(e?"&:extend(":":extend(")){do{r=null,t=null;for(var p=!0;!(r=o.$re(/^(!?all)(?=\s*(\)|,))/))&&(n=this.element());)!p&&n.combinator.value&&u("Targeting complex selectors can have unexpected behavior, and this behavior may change in the future.",f),p=!1,t?t.push(n):t=[n];r=r&&r[1],t||l("Missing target selector for :extend()."),h=new He.Extend(new He.Selector(t),r,f+s,i),a?a.push(h):a=[h]}while(o.$char(","));return c(/^\)/),e&&c(/^;/),a}},extendRule:function(){return this.extend(!0)},mixin:{call:function(e,t){var n,r,l,c,f,p=o.currentChar(),v=!1,d=o.i,m=!1;if("."===p||"#"===p){if(o.save(),r=this.elements()){if(f=o.i,o.$char("(")&&(m=o.isWhitespace(-2),l=this.args(!0).args,h(")"),c=!0,m&&u("Whitespace between a mixin name and parentheses for a mixin call is deprecated",f,"DEPRECATED")),!1!==t&&(n=this.ruleLookups()),!0===t&&!n)return void o.restore();if(e&&!n&&!c)return void o.restore();if(!e&&a.important()&&(v=!0),e||a.end()){o.forget();var g=new He.mixin.Call(r,l,d+s,i,!n&&v);return n?new He.NamespaceValue(g,n):(c||u("Calling a mixin without parentheses is deprecated",f,"DEPRECATED"),g)}}o.restore()}},elements:function(){for(var e,t,n,r,a,l=/^[#.](?:[\w-]|\\(?:[A-Fa-f0-9]{1,6} ?|[^A-Fa-f0-9]))+/;a=o.i,t=o.$re(l);)r=new He.Element(n,t,!1,a+s,i),e?e.push(r):e=[r],n=o.$char(">");return e},args:function(e){var t,n,i,r,s,u,c,h=a.entities,f={args:null,variadic:!1},p=[],v=[],d=[],m=!0;for(o.save();;){if(e)u=a.detachedRuleset()||a.expression();else{if(o.commentStore.length=0,o.$str("...")){f.variadic=!0,o.$char(";")&&!t&&(t=!0),(t?v:d).push({variadic:!0});break}u=h.variable()||h.property()||h.literal()||h.keyword()||this.call(!0)}if(!u||!m)break;r=null,u.throwAwayComments&&u.throwAwayComments(),s=u;var g=null;if(e?u.value&&1==u.value.length&&(g=u.value[0]):g=u,g&&(g instanceof He.Variable||g instanceof He.Property))if(o.$char(":")){if(p.length>0&&(t&&l("Cannot mix ; and , as delimiter types"),n=!0),!(s=a.detachedRuleset()||a.expression())){if(!e)return o.restore(),f.args=[],f;l("could not understand value for named argument")}r=i=g.name}else if(o.$str("...")){if(!e){f.variadic=!0,o.$char(";")&&!t&&(t=!0),(t?v:d).push({name:u.name,variadic:!0});break}c=!0}else e||(i=r=g.name,s=null);s&&p.push(s),d.push({name:r,value:s,expand:c}),o.$char(",")?m=!0:((m=";"===o.$char(";"))||t)&&(n&&l("Cannot mix ; and , as delimiter types"),t=!0,p.length>1&&(s=new He.Value(p)),v.push({name:i,value:s,expand:c}),i=null,p=[],n=!1)}return o.forget(),f.args=t?v:d,f},definition:function(){var e,t,n,i,r=[],s=!1;if(!("."!==o.currentChar()&&"#"!==o.currentChar()||o.peek(/^[^{]*\}/)))if(o.save(),t=o.$re(/^([#.](?:[\w-]|\\(?:[A-Fa-f0-9]{1,6} ?|[^A-Fa-f0-9]))+)\s*\(/)){e=t[1];var l=this.args(!1);if(r=l.args,s=l.variadic,!o.$char(")"))return void o.restore("Missing closing ')'");if(o.commentStore.length=0,o.$str("when")&&(i=c(a.conditions,"expected condition")),n=a.block())return o.forget(),new He.mixin.Definition(e,r,n,i,s);o.restore()}else o.restore()},ruleLookups:function(){var e,t=[];if("["===o.currentChar()){for(;;){if(o.save(),!(e=this.lookupValue())&&""!==e){o.restore();break}t.push(e),o.forget()}return t.length>0?t:void 0}},lookupValue:function(){if(o.save(),o.$char("[")){var e=o.$re(/^(?:[@$]{0,2})[_a-zA-Z0-9-]*/);if(o.$char("]"))return e||""===e?(o.forget(),e):void o.restore();o.restore()}else o.restore()}},entity:function(){var e=this.entities;return this.comment()||e.literal()||e.variable()||e.url()||e.property()||e.call()||e.keyword()||this.mixin.call(!0)||e.javascript()},end:function(){return o.$char(";")||o.peek("}")},ieAlpha:function(){var e;if(o.$re(/^opacity=/i))return(e=o.$re(/^\d+/))||(e=c(a.entities.variable,"Could not parse alpha"),e="@{".concat(e.name.slice(1),"}")),h(")"),new He.Quoted("","alpha(opacity=".concat(e,")"))},element:function(){var e,t,n,r=o.i;if(t=this.combinator(),!(e=o.$re(/^(?:\d+\.\d+|\d+)%/)||o.$re(/^(?:[.#]?|:*)(?:[\w-]|[^\x00-\x9f]|\\(?:[A-Fa-f0-9]{1,6} ?|[^A-Fa-f0-9]))+/)||o.$char("*")||o.$char("&")||this.attribute()||o.$re(/^\([^&()@]+\)/)||o.$re(/^[.#:](?=@)/)||this.entities.variableCurly()))if(o.save(),o.$char("("))if(n=this.selector(!1)){for(var a=[];o.$char(",");)a.push(n),a.push(new se(",")),n=this.selector(!1);a.push(n),o.$char(")")?(e=a.length>1?new He.Paren(new oe(a)):new He.Paren(n),o.forget()):o.restore("Missing closing ')'")}else o.restore("Missing closing ')'");else o.forget();if(e)return new He.Element(t,e,e instanceof He.Variable,r+s,i)},combinator:function(){var e=o.currentChar();if("/"===e){o.save();var t=o.$re(/^\/[a-z]+\//i);if(t)return o.forget(),new He.Combinator(t);o.restore()}if(">"===e||"+"===e||"~"===e||"|"===e||"^"===e){for(o.i++,"^"===e&&"^"===o.currentChar()&&(e="^^",o.i++);o.isWhitespace();)o.i++;return new He.Combinator(e)}return o.isWhitespace(-1)?new He.Combinator(" "):new He.Combinator(null)},selector:function(e){var t,n,r,a,u,h,f,p=o.i;for(e=!1!==e;(e&&(n=this.extend())||e&&(h=o.$str("when"))||(a=this.element()))&&(h?f=c(this.conditions,"expected condition"):f?l("CSS guard can only be used at the end of selector"):n?u=u?u.concat(n):n:(u&&l("Extend can only be used at the end of selector"),r=o.currentChar(),Array.isArray(a)&&a.forEach((function(e){return t.push(e)})),t?t.push(a):t=[a],a=null),"{"!==r&&"}"!==r&&";"!==r&&","!==r&&")"!==r););if(t)return new He.Selector(t,u,f,p+s,i);u&&l("Extend must be used to extend a selector, it cannot be used on its own")},selectors:function(){for(var e,t;(e=this.selector())&&(t?t.push(e):t=[e],o.commentStore.length=0,e.condition&&t.length>1&&l("Guards are only currently allowed on a single selector."),o.$char(","));)e.condition&&l("Guards are only currently allowed on a single selector."),o.commentStore.length=0;return t},attribute:function(){if(o.$char("[")){var e,t,n,i,r=this.entities;return(e=r.variableCurly())||(e=c(/^(?:[_A-Za-z0-9-*]*\|)?(?:[_A-Za-z0-9-]|\\.)+/)),(n=o.$re(/^[|~*$^]?=/))&&(t=r.quoted()||o.$re(/^[0-9]+%/)||o.$re(/^[\w-]+/)||r.variableCurly())&&(i=o.$re(/^[iIsS]/)),h("]"),new He.Attribute(e,n,t,i)}},block:function(){var e;if(o.$char("{")&&(e=this.primary())&&o.$char("}"))return e},blockRuleset:function(){var e=this.block();return e&&(e=new He.Ruleset(null,e)),e},detachedRuleset:function(){var e,t,n;if(o.save(),!o.$re(/^[.#]\(/)||(t=(e=this.mixin.args(!1)).args,n=e.variadic,o.$char(")"))){var i=this.blockRuleset();if(i)return o.forget(),t?new He.mixin.Definition(null,t,i,null,n):new He.DetachedRuleset(i);o.restore()}else o.restore()},ruleset:function(){var e,n,i;if(o.save(),t.dumpLineNumbers&&(i=f(o.i)),(e=this.selectors())&&(n=this.block())){o.forget();var r=new He.Ruleset(e,n,t.strictImports);return t.dumpLineNumbers&&(r.debugInfo=i),r}o.restore()},declaration:function(){var e,t,n,r,a,l,u=o.i,c=o.currentChar();if("."!==c&&"#"!==c&&"&"!==c&&":"!==c)if(o.save(),e=this.variable()||this.ruleProperty()){if((l="string"==typeof e)&&(t=this.detachedRuleset())&&(n=!0),o.commentStore.length=0,!t){if(a=!l&&e.length>1&&e.pop().value,t=e[0].value&&"--"===e[0].value.slice(0,2)?o.$char(";")?new se(""):this.permissiveValue(/[;}]/,!0):this.anonymousValue())return o.forget(),new He.Declaration(e,t,!1,a,u+s,i);t||(t=this.value()),t?r=this.important():l&&(t=this.permissiveValue())}if(t&&(this.end()||n))return o.forget(),new He.Declaration(e,t,r,a,u+s,i);o.restore()}else o.restore()},anonymousValue:function(){var e=o.i,t=o.$re(/^([^.#@$+/'"*`(;{}-]*);/);if(t)return new He.Anonymous(t[1],e+s)},permissiveValue:function(e){var t,n,r,s,a=e||";",c=o.i,h=[];function f(){var e=o.currentChar();return"string"==typeof a?e===a:a.test(e)}if(!f()){s=[];do{(n=this.comment())?s.push(n):((n=this.entity())&&s.push(n),o.peek(",")&&(s.push(new He.Anonymous(",",o.i)),o.$char(",")))}while(n);if(r=f(),s.length>0){if(s=new He.Expression(s),r)return s;h.push(s)," "===o.prevChar()&&h.push(new He.Anonymous(" ",c))}if(o.save(),s=o.$parseUntil(a)){if("string"==typeof s&&l("Expected '".concat(s,"'"),"Parse"),1===s.length&&" "===s[0])return o.forget(),new He.Anonymous("",c);var p=void 0;for(t=0;t<s.length;t++)if(p=s[t],Array.isArray(p))h.push(new He.Quoted(p[0],p[1],!0,c,i));else{t===s.length-1&&(p=p.trim());var v=new He.Quoted("'",p,!0,c,i);/@([\w-]+)/g.test(p)&&u("@[ident] in unknown values will not be evaluated as variables in the future. Use @{[ident]}",c,"DEPRECATED"),/\$([\w-]+)/g.test(p)&&u("$[ident] in unknown values will not be evaluated as property references in the future. Use ${[ident]}",c,"DEPRECATED"),v.variableRegex=/@([\w-]+)|@{([\w-]+)}/g,v.propRegex=/\$([\w-]+)|\${([\w-]+)}/g,h.push(v)}return o.forget(),new He.Expression(h,!0)}o.restore()}},import:function(){var e,t,n=o.i,r=o.$re(/^@import\s+/);if(r){var a=(r?this.importOptions():null)||{};if(e=this.entities.quoted()||this.entities.url())return t=this.mediaFeatures({}),o.$char(";")||(o.i=n,l("missing semi-colon or unrecognised media features on import")),t=t&&new He.Value(t),new He.Import(e,t,a,n+s,i);o.i=n,l("malformed import statement")}},importOptions:function(){var e,t,n,i={};if(!o.$char("("))return null;do{if(e=this.importOption()){switch(n=!0,t=e){case"css":t="less",n=!1;break;case"once":t="multiple",n=!1}if(i[t]=n,!o.$char(","))break}}while(e);return h(")"),i},importOption:function(){var e=o.$re(/^(less|css|multiple|once|inline|reference|optional)/);if(e)return e[1]},mediaFeature:function(e){var t,n,r,a=this.entities,u=[];o.save();do{(t=a.declarationCall.bind(this)()||a.keyword()||a.variable()||a.mixinLookup())?u.push(t):o.$char("(")&&(n=this.property(),o.save(),!n&&e.queryInParens&&o.$re(/^[0-9a-z-]*\s*([<>]=|<=|>=|[<>]|=)/)?(o.restore(),n=this.condition(),o.save(),(r=this.atomicCondition(null,n.rvalue))||o.restore()):(o.restore(),t=this.value()),o.$char(")")?n&&!t?(u.push(new He.Paren(new He.QueryInParens(n.op,n.lvalue,n.rvalue,r?r.op:null,r?r.rvalue:null,n._index))),t=n):n&&t?u.push(new He.Paren(new He.Declaration(n,t,null,null,o.i+s,i,!0))):t?u.push(new He.Paren(t)):l("badly formed media feature definition"):l("Missing closing ')'","Parse"))}while(t);if(o.forget(),u.length>0)return new He.Expression(u)},mediaFeatures:function(e){var t,n=this.entities,i=[];do{if(t=this.mediaFeature(e)){if(i.push(t),!o.$char(","))break}else if((t=n.variable()||n.mixinLookup())&&(i.push(t),!o.$char(",")))break}while(t);return i.length>0?i:null},prepareAndGetNestableAtRule:function(e,n,r,a){var u=this.mediaFeatures(a),c=this.block();c||l("media definitions require block statements after any features"),o.forget();var h=new e(c,u,n+s,i);return t.dumpLineNumbers&&(h.debugInfo=r),h},nestableAtRule:function(){var e,n=o.i;if(t.dumpLineNumbers&&(e=f(n)),o.save(),o.$peekChar("@")){if(o.$str("@media"))return this.prepareAndGetNestableAtRule(He.Media,n,e,ie);if(o.$str("@container"))return this.prepareAndGetNestableAtRule(He.Container,n,e,re)}o.restore()},plugin:function(){var e,t,n,r=o.i;if(o.$re(/^@plugin\s+/)){if(n=(t=this.pluginArgs())?{pluginArgs:t,isPlugin:!0}:{isPlugin:!0},e=this.entities.quoted()||this.entities.url())return o.$char(";")||(o.i=r,l("missing semi-colon on @plugin")),new He.Import(e,null,n,r+s,i);o.i=r,l("malformed @plugin statement")}},pluginArgs:function(){if(o.save(),!o.$char("("))return o.restore(),null;var e=o.$re(/^\s*([^);]+)\)\s*/);return e[1]?(o.forget(),e[1].trim()):(o.restore(),null)},atrule:function(){var e,n,r,a,u,c,h,p=o.i,v=!0,d=!0,m=!1;if("@"===o.currentChar()){if(n=this.import()||this.plugin()||this.nestableAtRule())return n;if(o.save(),e=o.$re(/^@[a-z-]+/)){switch(a=e,"-"==e.charAt(1)&&e.indexOf("-",2)>0&&(a="@".concat(e.slice(e.indexOf("-",2)+1))),a){case"@charset":u=!0,v=!1;break;case"@namespace":c=!0,v=!1;break;case"@keyframes":case"@counter-style":u=!0;break;case"@document":case"@supports":h=!0,d=!1;break;case"@starting-style":case"@layer":d=!1;break;default:h=!0}if(o.commentStore.length=0,u?(n=this.entity())||l("expected ".concat(e," identifier")):c?(n=this.expression())||l("expected ".concat(e," expression")):h&&(n=this.permissiveValue(/^[{;]/),v="{"===o.currentChar(),n?n.value||(n=null):v||";"===o.currentChar()||l("".concat(e," rule is missing block or ending semi-colon"))),v)if(r=this.blockRuleset(),o.save(),r||d||(n=this.entity(),r=this.blockRuleset()),r||d)o.forget();else{o.restore();var g=[];for(n=this.entity();o.$char(",");)g.push(n),n=this.entity();n&&g.length>0?(g.push(n),n=g,m=!0):r=this.blockRuleset()}if(r||m||!v&&n&&o.$char(";"))return o.forget(),new He.AtRule(e,n,r,p+s,i,t.dumpLineNumbers?f(p):null,d);o.restore("at-rule options not recognised")}}},value:function(){var e,t=[],n=o.i;do{if((e=this.expression())&&(t.push(e),!o.$char(",")))break}while(e);if(t.length>0)return new He.Value(t,n+s)},important:function(){if("!"===o.currentChar())return o.$re(/^! *important/)},sub:function(){var e,t;if(o.save(),o.$char("("))return(e=this.addition())&&o.$char(")")?(o.forget(),(t=new He.Expression([e])).parens=!0,t):void o.restore("Expected ')'");o.restore()},colorOperand:function(){o.save();var e=o.$re(/^[lchrgbs]\s+/);if(e)return new He.Keyword(e[0]);o.restore()},multiplication:function(){var e,t,n,i,r;if(e=this.operand()){for(r=o.isWhitespace(-1);!o.peek(/^\/[*/]/);){if(o.save(),!(n=o.$char("/")||o.$char("*"))){var s=o.i;(n=o.$str("./"))&&u("./ operator is deprecated",s,"DEPRECATED")}if(!n){o.forget();break}if(!(t=this.operand())){o.restore();break}o.forget(),e.parensInOp=!0,t.parensInOp=!0,i=new He.Operation(n,[i||e,t],r),r=o.isWhitespace(-1)}return i||e}},addition:function(){var e,t,n,i,r;if(e=this.multiplication()){for(r=o.isWhitespace(-1);(n=o.$re(/^[-+]\s+/)||!r&&(o.$char("+")||o.$char("-")))&&(t=this.multiplication());)e.parensInOp=!0,t.parensInOp=!0,i=new He.Operation(n,[i||e,t],r),r=o.isWhitespace(-1);return i||e}},conditions:function(){var e,t,n,i=o.i;if(e=this.condition(!0)){for(;o.peek(/^,\s*(not\s*)?\(/)&&o.$char(",")&&(t=this.condition(!0));)n=new He.Condition("or",n||e,t,i+s);return n||e}},condition:function(e){var t,n,i;if(t=this.conditionAnd(e)){if(n=o.$str("or")){if(!(i=this.condition(e)))return;t=new He.Condition(n,t,i)}return t}},conditionAnd:function(e){var t,n,i,r,s=this;if(t=(r=s.negatedCondition(e)||s.parenthesisCondition(e))||e?r:s.atomicCondition(e)){if(n=o.$str("and")){if(!(i=this.conditionAnd(e)))return;t=new He.Condition(n,t,i)}return t}},negatedCondition:function(e){if(o.$str("not")){var t=this.parenthesisCondition(e);return t&&(t.negate=!t.negate),t}},parenthesisCondition:function(e){var t;if(o.save(),o.$str("(")){if(t=function(t){var n;if(o.save(),n=t.condition(e)){if(o.$char(")"))return o.forget(),n;o.restore()}else o.restore()}(this))return o.forget(),t;if(t=this.atomicCondition(e)){if(o.$char(")"))return o.forget(),t;o.restore("expected ')' got '".concat(o.currentChar(),"'"))}else o.restore()}else o.restore()},atomicCondition:function(e,t){var n,i,r,a,u=this.entities,c=o.i,h=function(){return this.addition()||u.keyword()||u.quoted()||u.mixinLookup()}.bind(this);if(n=t||h())return o.$char(">")?a=o.$char("=")?">=":">":o.$char("<")?a=o.$char("=")?"<=":"<":o.$char("=")&&(a=o.$char(">")?"=>":o.$char("<")?"=<":"="),a?(i=h())?r=new He.Condition(a,n,i,c+s,!1):l("expected expression"):t||(r=new He.Condition("=",n,new He.Keyword("true"),c+s,!1)),r},operand:function(){var e,t=this.entities;o.peek(/^-[@$(]/)&&(e=o.$char("-"));var n=this.sub()||t.dimension()||t.color()||t.variable()||t.property()||t.call()||t.quoted(!0)||t.colorKeyword()||this.colorOperand()||t.mixinLookup();return e&&(n.parensInOp=!0,n=new He.Negative(n)),n},expression:function(){var e,t,n=[],i=o.i;do{!(e=this.comment())||e.isLineComment?((e=this.addition()||this.entity())instanceof He.Comment&&(e=null),e&&(n.push(e),o.peek(/^\/[/*]/)||(t=o.$char("/"))&&n.push(new He.Anonymous(t,i+s)))):n.push(e)}while(e);if(n.length>0)return new He.Expression(n)},property:function(){var e=o.$re(/^(\*?-?[_a-zA-Z0-9-]+)\s*:/);if(e)return e[1]},ruleProperty:function(){var e,t,n=[],r=[];o.save();var a=o.$re(/^([_a-zA-Z0-9-]+)\s*:/);if(a)return n=[new He.Keyword(a[1])],o.forget(),n;function l(e){var t=o.i,i=o.$re(e);if(i)return r.push(t),n.push(i[1])}for(l(/^(\*?)/);l(/^((?:[\w-]+)|(?:[@$]\{[\w-]+\}))/););if(n.length>1&&l(/^((?:\+_|\+)?)\s*:/)){for(o.forget(),""===n[0]&&(n.shift(),r.shift()),t=0;t<n.length;t++)e=n[t],n[t]="@"!==e.charAt(0)&&"$"!==e.charAt(0)?new He.Keyword(e):"@"===e.charAt(0)?new He.Variable("@".concat(e.slice(2,-1)),r[t]+s,i):new He.Property("$".concat(e.slice(2,-1)),r[t]+s,i);return n}o.restore()}}}};ae.serializeVars=function(e){var t="";for(var n in e)if(Object.hasOwnProperty.call(e,n)){var i=e[n];t+="".concat(("@"===n[0]?"":"@")+n,": ").concat(i).concat(";"===String(i).slice(-1)?"":";")}return t};var oe=function(e,t,n,i,r,s){this.extendList=t,this.condition=n,this.evaldCondition=!n,this._index=i,this._fileInfo=r,this.elements=this.getElements(e),this.mixinElements_=void 0,this.copyVisibilityInfo(s),this.setParent(this.elements,this)};oe.prototype=Object.assign(new u,{type:"Selector",accept:function(e){this.elements&&(this.elements=e.visitArray(this.elements)),this.extendList&&(this.extendList=e.visitArray(this.extendList)),this.condition&&(this.condition=e.visit(this.condition))},createDerived:function(e,t,n){e=this.getElements(e);var i=new oe(e,t||this.extendList,null,this.getIndex(),this.fileInfo(),this.visibilityInfo());return i.evaldCondition=M(n)?this.evaldCondition:n,i.mediaEmpty=this.mediaEmpty,i},getElements:function(e){return e?("string"==typeof e&&new ae(this.parse.context,this.parse.importManager,this._fileInfo,this._index).parseNode(e,["selector"],(function(t,n){if(t)throw new V({index:t.index,message:t.message},this.parse.imports,this._fileInfo.filename);e=n[0].elements})),e):[new g("","&",!1,this._index,this._fileInfo)]},createEmptySelectors:function(){var e=new g("","&",!1,this._index,this._fileInfo),t=[new oe([e],null,null,this._index,this._fileInfo)];return t[0].mediaEmpty=!0,t},match:function(e){var t,n,i=this.elements,r=i.length;if(0===(t=(e=e.mixinElements()).length)||r<t)return 0;for(n=0;n<t;n++)if(i[n].value!==e[n])return 0;return t},mixinElements:function(){if(this.mixinElements_)return this.mixinElements_;var e=this.elements.map((function(e){return e.combinator.value+(e.value.value||e.value)})).join("").match(/[,&#*.\w-]([\w-]|(\\.))*/g);return e?"&"===e[0]&&e.shift():e=[],this.mixinElements_=e},isJustParentSelector:function(){return!this.mediaEmpty&&1===this.elements.length&&"&"===this.elements[0].value&&(" "===this.elements[0].combinator.value||""===this.elements[0].combinator.value)},eval:function(e){var t=this.condition&&this.condition.eval(e),n=this.elements,i=this.extendList;return n=n&&n.map((function(t){return t.eval(e)})),i=i&&i.map((function(t){return t.eval(e)})),this.createDerived(n,i,t)},genCSS:function(e,t){var n;for(e&&e.firstSelector||""!==this.elements[0].combinator.value||t.add(" ",this.fileInfo(),this.getIndex()),n=0;n<this.elements.length;n++)this.elements[n].genCSS(e,t)},getIsOutput:function(){return this.evaldCondition}});var le=function(e){if(!e)throw new Error("Value requires an array argument");Array.isArray(e)?this.value=e:this.value=[e]};le.prototype=Object.assign(new u,{type:"Value",accept:function(e){this.value&&(this.value=e.visitArray(this.value))},eval:function(e){return 1===this.value.length?this.value[0].eval(e):new le(this.value.map((function(t){return t.eval(e)})))},genCSS:function(e,t){var n;for(n=0;n<this.value.length;n++)this.value[n].genCSS(e,t),n+1<this.value.length&&t.add(e&&e.compress?",":", ")}});var ue=function(e){this.value=e};ue.prototype=Object.assign(new u,{type:"Keyword",genCSS:function(e,t){if("%"===this.value)throw{type:"Syntax",message:"Invalid % without number"};t.add(this.value)}}),ue.True=new ue("true"),ue.False=new ue("false");var ce=y;var he=function(e,t,n,i,r,s,a,o){this.name=e,this.value=t instanceof u?t:new le([t?new se(t):null]),this.important=n?" ".concat(n.trim()):"",this.merge=i,this._index=r,this._fileInfo=s,this.inline=a||!1,this.variable=void 0!==o?o:e.charAt&&"@"===e.charAt(0),this.allowRoot=!0,this.setParent(this.value,this)};function fe(e){return"/* line ".concat(e.debugInfo.lineNumber,", ").concat(e.debugInfo.fileName," */\n")}function pe(e){var t=e.debugInfo.fileName;return/^[a-z]+:\/\//i.test(t)||(t="file://".concat(t)),"@media -sass-debug-info{filename{font-family:".concat(t.replace(/([.:/\\])/g,(function(e){return"\\"==e&&(e="/"),"\\".concat(e)})),"}line{font-family:\\00003").concat(e.debugInfo.lineNumber,"}}\n")}function ve(e,t,n){var i="";if(e.dumpLineNumbers&&!e.compress)switch(e.dumpLineNumbers){case"comments":i=fe(t);break;case"mediaquery":i=pe(t);break;case"all":i=fe(t)+(n||"")+pe(t)}return i}he.prototype=Object.assign(new u,{type:"Declaration",genCSS:function(e,t){t.add(this.name+(e.compress?":":": "),this.fileInfo(),this.getIndex());try{this.value.genCSS(e,t)}catch(e){throw e.index=this._index,e.filename=this._fileInfo.filename,e}t.add(this.important+(this.inline||e.lastRule&&e.compress?"":";"),this._fileInfo,this._index)},eval:function(e){var t,n,i=!1,r=this.name,s=this.variable;"string"!=typeof r&&(r=1===r.length&&r[0]instanceof ue?r[0].value:function(e,t){var n,i="",r=t.length,s={add:function(e){i+=e}};for(n=0;n<r;n++)t[n].eval(e).genCSS(e,s);return i}(e,r),s=!1),"font"===r&&e.math===ce.ALWAYS&&(i=!0,t=e.math,e.math=ce.PARENS_DIVISION);try{if(e.importantScope.push({}),n=this.value.eval(e),!this.variable&&"DetachedRuleset"===n.type)throw{message:"Rulesets cannot be evaluated on a property.",index:this.getIndex(),filename:this.fileInfo().filename};var a=this.important,o=e.importantScope.pop();return!a&&o.important&&(a=o.important),new he(r,n,a,this.merge,this.getIndex(),this.fileInfo(),this.inline,s)}catch(e){throw"number"!=typeof e.index&&(e.index=this.getIndex(),e.filename=this.fileInfo().filename),e}finally{i&&(e.math=t)}},makeImportant:function(){return new he(this.name,this.value,"!important",this.merge,this.getIndex(),this.fileInfo(),this.inline)}});var de=function(e,t,n,i){this.value=e,this.isLineComment=t,this._index=n,this._fileInfo=i,this.allowRoot=!0};de.prototype=Object.assign(new u,{type:"Comment",genCSS:function(e,t){this.debugInfo&&t.add(ve(e,this),this.fileInfo(),this.getIndex()),t.add(this.value)},isSilent:function(e){var t=e.compress&&"!"!==this.value[2];return this.isLineComment||t}});var me={eval:function(){var e=this.value_,t=this.error_;if(t)throw t;if(!M(e))return e?ue.True:ue.False},value:function(e){this.value_=e},error:function(e){this.error_=e},reset:function(){this.value_=this.error_=null}},ge=function(e,t,n,i){this.selectors=e,this.rules=t,this._lookups={},this._variables=null,this._properties=null,this.strictImports=n,this.copyVisibilityInfo(i),this.allowRoot=!0,this.setParent(this.selectors,this),this.setParent(this.rules,this)};ge.prototype=Object.assign(new u,{type:"Ruleset",isRuleset:!0,isRulesetLike:function(){return!0},accept:function(e){this.paths?this.paths=e.visitArray(this.paths,!0):this.selectors&&(this.selectors=e.visitArray(this.selectors)),this.rules&&this.rules.length&&(this.rules=e.visitArray(this.rules))},eval:function(e){var t,n,i,r,s,a=!1;if(this.selectors&&(n=this.selectors.length)){for(t=new Array(n),me.error({type:"Syntax",message:"it is currently only allowed in parametric mixin guards,"}),r=0;r<n;r++){i=this.selectors[r].eval(e);for(var o=0;o<i.elements.length;o++)if(i.elements[o].isVariable){s=!0;break}t[r]=i,i.evaldCondition&&(a=!0)}if(s){var l=new Array(n);for(r=0;r<n;r++)i=t[r],l[r]=i.toCSS(e);var c=t[0].getIndex(),h=t[0].fileInfo();new ae(e,this.parse.importManager,h,c).parseNode(l.join(","),["selectors"],(function(e,n){n&&(t=R(n))}))}me.reset()}else a=!0;var f,p,v=this.rules?A(this.rules):null,d=new ge(t,v,this.strictImports,this.visibilityInfo());d.originalRuleset=this,d.root=this.root,d.firstRoot=this.firstRoot,d.allowImports=this.allowImports,this.debugInfo&&(d.debugInfo=this.debugInfo),a||(v.length=0),d.functionRegistry=function(e){for(var t,n=0,i=e.length;n!==i;++n)if(t=e[n].functionRegistry)return t;return ne}(e.frames).inherit();var m=e.frames;m.unshift(d);var g=e.selectors;g||(e.selectors=g=[]),g.unshift(this.selectors),(d.root||d.allowImports||!d.strictImports)&&d.evalImports(e);var y=d.rules;for(r=0;f=y[r];r++)f.evalFirst&&(y[r]=f.eval(e));var b=e.mediaBlocks&&e.mediaBlocks.length||0;for(r=0;f=y[r];r++)"MixinCall"===f.type?(v=f.eval(e).filter((function(e){return!(e instanceof he&&e.variable)||!d.variable(e.name)})),y.splice.apply(y,[r,1].concat(v)),r+=v.length-1,d.resetCache()):"VariableCall"===f.type&&(v=f.eval(e).rules.filter((function(e){return!(e instanceof he&&e.variable)})),y.splice.apply(y,[r,1].concat(v)),r+=v.length-1,d.resetCache());for(r=0;f=y[r];r++)f.evalFirst||(y[r]=f=f.eval?f.eval(e):f);for(r=0;f=y[r];r++)if(f instanceof ge&&f.selectors&&1===f.selectors.length&&f.selectors[0]&&f.selectors[0].isJustParentSelector()){y.splice(r--,1);for(o=0;p=f.rules[o];o++)p instanceof u&&(p.copyVisibilityInfo(f.visibilityInfo()),p instanceof he&&p.variable||y.splice(++r,0,p))}if(m.shift(),g.shift(),e.mediaBlocks)for(r=b;r<e.mediaBlocks.length;r++)e.mediaBlocks[r].bubbleSelectors(t);return d},evalImports:function(e){var t,n,i=this.rules;if(i)for(t=0;t<i.length;t++)"Import"===i[t].type&&((n=i[t].eval(e))&&(n.length||0===n.length)?(i.splice.apply(i,[t,1].concat(n)),t+=n.length-1):i.splice(t,1,n),this.resetCache())},makeImportant:function(){return new ge(this.selectors,this.rules.map((function(e){return e.makeImportant?e.makeImportant():e})),this.strictImports,this.visibilityInfo())},matchArgs:function(e){return!e||0===e.length},matchCondition:function(e,t){var n=this.selectors[this.selectors.length-1];return!!n.evaldCondition&&!(n.condition&&!n.condition.eval(new B.Eval(t,t.frames)))},resetCache:function(){this._rulesets=null,this._variables=null,this._properties=null,this._lookups={}},variables:function(){return this._variables||(this._variables=this.rules?this.rules.reduce((function(e,t){if(t instanceof he&&!0===t.variable&&(e[t.name]=t),"Import"===t.type&&t.root&&t.root.variables){var n=t.root.variables();for(var i in n)n.hasOwnProperty(i)&&(e[i]=t.root.variable(i))}return e}),{}):{}),this._variables},properties:function(){return this._properties||(this._properties=this.rules?this.rules.reduce((function(e,t){if(t instanceof he&&!0!==t.variable){var n=1===t.name.length&&t.name[0]instanceof ue?t.name[0].value:t.name;e["$".concat(n)]?e["$".concat(n)].push(t):e["$".concat(n)]=[t]}return e}),{}):{}),this._properties},variable:function(e){var t=this.variables()[e];if(t)return this.parseValue(t)},property:function(e){var t=this.properties()[e];if(t)return this.parseValue(t)},lastDeclaration:function(){for(var e=this.rules.length;e>0;e--){var t=this.rules[e-1];if(t instanceof he)return this.parseValue(t)}},parseValue:function(e){var t=this;function n(e){return e.value instanceof se&&!e.parsed?("string"==typeof e.value.value?new ae(this.parse.context,this.parse.importManager,e.fileInfo(),e.value.getIndex()).parseNode(e.value.value,["value","important"],(function(t,n){t&&(e.parsed=!0),n&&(e.value=n[0],e.important=n[1]||"",e.parsed=!0)})):e.parsed=!0,e):e}if(Array.isArray(e)){var i=[];return e.forEach((function(e){i.push(n.call(t,e))})),i}return n.call(t,e)},rulesets:function(){if(!this.rules)return[];var e,t,n=[],i=this.rules;for(e=0;t=i[e];e++)t.isRuleset&&n.push(t);return n},prependRule:function(e){var t=this.rules;t?t.unshift(e):this.rules=[e],this.setParent(e,this)},find:function(e,t,n){t=t||this;var i,r,s=[],a=e.toCSS();return a in this._lookups?this._lookups[a]:(this.rulesets().forEach((function(a){if(a!==t)for(var o=0;o<a.selectors.length;o++)if(i=e.match(a.selectors[o])){if(e.elements.length>i){if(!n||n(a)){r=a.find(new oe(e.elements.slice(i)),t,n);for(var l=0;l<r.length;++l)r[l].path.push(a);Array.prototype.push.apply(s,r)}}else s.push({rule:a,path:[]});break}})),this._lookups[a]=s,s)},genCSS:function(e,t){var n,i,r,s,a,o=[];e.tabLevel=e.tabLevel||0,this.root||e.tabLevel++;var l,u=e.compress?"":Array(e.tabLevel+1).join("  "),c=e.compress?"":Array(e.tabLevel).join("  "),h=0,f=0;for(n=0;s=this.rules[n];n++)s instanceof de?(f===n&&f++,o.push(s)):s.isCharset&&s.isCharset()?(o.splice(h,0,s),h++,f++):"Import"===s.type?(o.splice(f,0,s),f++):o.push(s);if(o=[].concat(o),!this.root){(r=ve(e,this,c))&&(t.add(r),t.add(c));var p=this.paths,v=p.length,d=void 0;for(l=e.compress?",":",\n".concat(c),n=0;n<v;n++)if(d=(a=p[n]).length)for(n>0&&t.add(l),e.firstSelector=!0,a[0].genCSS(e,t),e.firstSelector=!1,i=1;i<d;i++)a[i].genCSS(e,t);t.add((e.compress?"{":" {\n")+u)}for(n=0;s=o[n];n++){n+1===o.length&&(e.lastRule=!0);var m=e.lastRule;s.isRulesetLike(s)&&(e.lastRule=!1),s.genCSS?s.genCSS(e,t):s.value&&t.add(s.value.toString()),e.lastRule=m,!e.lastRule&&s.isVisible()?t.add(e.compress?"":"\n".concat(u)):e.lastRule=!1}this.root||(t.add(e.compress?"}":"\n".concat(c,"}")),e.tabLevel--),t.isEmpty()||e.compress||!this.firstRoot||t.add("\n")},joinSelectors:function(e,t,n){for(var i=0;i<n.length;i++)this.joinSelector(e,t,n[i])},joinSelector:function(e,t,n){function i(e,t){var n,i;if(0===e.length)n=new v(e[0]);else{var r=new Array(e.length);for(i=0;i<e.length;i++)r[i]=new g(null,e[i],t.isVariable,t._index,t._fileInfo);n=new v(new oe(r))}return n}function r(e,t){var n;return n=new g(null,e,t.isVariable,t._index,t._fileInfo),new oe([n])}function s(e,t,n,i){var r,s,a;if(r=[],e.length>0?(s=(r=A(e)).pop(),a=i.createDerived(A(s.elements))):a=i.createDerived([]),t.length>0){var o=n.combinator,l=t[0].elements[0];o.emptyOrWhitespace&&!l.combinator.emptyOrWhitespace&&(o=l.combinator),a.elements.push(new g(o,l.value,n.isVariable,n._index,n._fileInfo)),a.elements=a.elements.concat(t[0].elements.slice(1))}if(0!==a.elements.length&&r.push(a),t.length>1){var u=t.slice(1);u=u.map((function(e){return e.createDerived(e.elements,[])})),r=r.concat(u)}return r}function a(e,t,n,i,r){var a;for(a=0;a<e.length;a++){var o=s(e[a],t,n,i);r.push(o)}return r}function o(e,t){var n,i;if(0!==e.length)if(0!==t.length)for(n=0;i=t[n];n++)i.length>0?i[i.length-1]=i[i.length-1].createDerived(i[i.length-1].elements.concat(e)):i.push(new oe(e));else t.push([new oe(e)])}function l(e,t){var n=t.createDerived(t.elements,t.extendList,t.evaldCondition);return n.copyVisibilityInfo(e),n}var u,c;if(!function e(t,n,l){var u,c,h,f,p,d,m,y,b,w,x,S,I=!1;for(f=[],p=[[]],u=0;y=l.elements[u];u++)if("&"!==y.value){var C=(S=void 0,(x=y).value instanceof v&&(S=x.value.value)instanceof oe?S:null);if(null!==C){o(f,p);var k,A=[],_=[];for(k=e(A,n,C),I=I||k,h=0;h<A.length;h++){a(p,[r(i(A[h],y),y)],y,l,_)}p=_,f=[]}else f.push(y)}else{for(I=!0,d=[],o(f,p),c=0;c<p.length;c++)if(m=p[c],0===n.length)m.length>0&&m[0].elements.push(new g(y.combinator,"",y.isVariable,y._index,y._fileInfo)),d.push(m);else for(h=0;h<n.length;h++){var P=s(m,n[h],y,l);d.push(P)}p=d,f=[]}for(o(f,p),u=0;u<p.length;u++)(b=p[u].length)>0&&(t.push(p[u]),w=p[u][b-1],p[u][b-1]=w.createDerived(w.elements,l.extendList));return I}(c=[],t,n))if(t.length>0)for(c=[],u=0;u<t.length;u++){var h=t[u].map(l.bind(this,n.visibilityInfo()));h.push(n),c.push(h)}else c=[[n]];for(u=0;u<c.length;u++)e.push(c[u])}});var ye=function(e,t,n){this.numerator=e?A(e).sort():[],this.denominator=t?A(t).sort():[],n?this.backupUnit=n:e&&e.length&&(this.backupUnit=e[0])};ye.prototype=Object.assign(new u,{type:"Unit",clone:function(){return new ye(A(this.numerator),A(this.denominator),this.backupUnit)},genCSS:function(e,t){var n=e&&e.strictUnits;1===this.numerator.length?t.add(this.numerator[0]):!n&&this.backupUnit?t.add(this.backupUnit):!n&&this.denominator.length&&t.add(this.denominator[0])},toString:function(){var e,t=this.numerator.join("*");for(e=0;e<this.denominator.length;e++)t+="/".concat(this.denominator[e]);return t},compare:function(e){return this.is(e.toString())?0:void 0},is:function(e){return this.toString().toUpperCase()===e.toUpperCase()},isLength:function(){return RegExp("^(px|em|ex|ch|rem|in|cm|mm|pc|pt|ex|vw|vh|vmin|vmax)$","gi").test(this.toCSS())},isEmpty:function(){return 0===this.numerator.length&&0===this.denominator.length},isSingular:function(){return this.numerator.length<=1&&0===this.denominator.length},map:function(e){var t;for(t=0;t<this.numerator.length;t++)this.numerator[t]=e(this.numerator[t],!1);for(t=0;t<this.denominator.length;t++)this.denominator[t]=e(this.denominator[t],!0)},usedUnits:function(){var e,t,n,i={};for(n in t=function(t){return e.hasOwnProperty(t)&&!i[n]&&(i[n]=t),t},o)o.hasOwnProperty(n)&&(e=o[n],this.map(t));return i},cancel:function(){var e,t,n={};for(t=0;t<this.numerator.length;t++)n[e=this.numerator[t]]=(n[e]||0)+1;for(t=0;t<this.denominator.length;t++)n[e=this.denominator[t]]=(n[e]||0)-1;for(e in this.numerator=[],this.denominator=[],n)if(n.hasOwnProperty(e)){var i=n[e];if(i>0)for(t=0;t<i;t++)this.numerator.push(e);else if(i<0)for(t=0;t<-i;t++)this.denominator.push(e)}this.numerator.sort(),this.denominator.sort()}});var be=function(e,t){if(this.value=parseFloat(e),isNaN(this.value))throw new Error("Dimension is not a number.");this.unit=t&&t instanceof ye?t:new ye(t?[t]:void 0),this.setParent(this.unit,this)};be.prototype=Object.assign(new u,{type:"Dimension",accept:function(e){this.unit=e.visit(this.unit)},eval:function(e){return this},toColor:function(){return new c([this.value,this.value,this.value])},genCSS:function(e,t){if(e&&e.strictUnits&&!this.unit.isSingular())throw new Error("Multiple units in dimension. Correct the units or use the unit function. Bad unit: ".concat(this.unit.toString()));var n=this.fround(e,this.value),i=String(n);if(0!==n&&n<1e-6&&n>-1e-6&&(i=n.toFixed(20).replace(/0+$/,"")),e&&e.compress){if(0===n&&this.unit.isLength())return void t.add(i);n>0&&n<1&&(i=i.substr(1))}t.add(i),this.unit.genCSS(e,t)},operate:function(e,t,n){var i=this._operate(e,t,this.value,n.value),r=this.unit.clone();if("+"===t||"-"===t)if(0===r.numerator.length&&0===r.denominator.length)r=n.unit.clone(),this.unit.backupUnit&&(r.backupUnit=this.unit.backupUnit);else if(0===n.unit.numerator.length&&0===r.denominator.length);else{if(n=n.convertTo(this.unit.usedUnits()),e.strictUnits&&n.unit.toString()!==r.toString())throw new Error("Incompatible units. Change the units or use the unit function. "+"Bad units: '".concat(r.toString(),"' and '").concat(n.unit.toString(),"'."));i=this._operate(e,t,this.value,n.value)}else"*"===t?(r.numerator=r.numerator.concat(n.unit.numerator).sort(),r.denominator=r.denominator.concat(n.unit.denominator).sort(),r.cancel()):"/"===t&&(r.numerator=r.numerator.concat(n.unit.denominator).sort(),r.denominator=r.denominator.concat(n.unit.numerator).sort(),r.cancel());return new be(i,r)},compare:function(e){var t,n;if(e instanceof be){if(this.unit.isEmpty()||e.unit.isEmpty())t=this,n=e;else if(t=this.unify(),n=e.unify(),0!==t.unit.compare(n.unit))return;return u.numericCompare(t.value,n.value)}},unify:function(){return this.convertTo({length:"px",duration:"s",angle:"rad"})},convertTo:function(e){var t,n,i,r,s,a=this.value,l=this.unit.clone(),u={};if("string"==typeof e){for(t in o)o[t].hasOwnProperty(e)&&((u={})[t]=e);e=u}for(n in s=function(e,t){return i.hasOwnProperty(e)?(t?a/=i[e]/i[r]:a*=i[e]/i[r],r):e},e)e.hasOwnProperty(n)&&(r=e[n],i=o[n],l.map(s));return l.cancel(),new be(a,l)}});var we=function(e,t){if(this.value=e,this.noSpacing=t,!e)throw new Error("Expression requires an array parameter")};we.prototype=Object.assign(new u,{type:"Expression",accept:function(e){this.value=e.visitArray(this.value)},eval:function(e){var t,n=this.noSpacing,i=e.isMathOn(),r=this.parens,s=!1;return r&&e.inParenthesis(),this.value.length>1?t=new we(this.value.map((function(t){return t.eval?t.eval(e):t})),this.noSpacing):1===this.value.length?(!this.value[0].parens||this.value[0].parensInOp||e.inCalc||(s=!0),t=this.value[0].eval(e)):t=this,r&&e.outOfParenthesis(),!this.parens||!this.parensInOp||i||s||t instanceof be||(t=new v(t)),t.noSpacing=t.noSpacing||n,t},genCSS:function(e,t){for(var n=0;n<this.value.length;n++)this.value[n].genCSS(e,t),!this.noSpacing&&n+1<this.value.length&&(n+1<this.value.length&&!(this.value[n+1]instanceof se)||this.value[n+1]instanceof se&&","!==this.value[n+1].value)&&t.add(" ")},throwAwayComments:function(){this.value=this.value.filter((function(e){return!(e instanceof de)}))}});var xe={isRulesetLike:function(){return!0},accept:function(e){this.features&&(this.features=e.visit(this.features)),this.rules&&(this.rules=e.visitArray(this.rules))},evalTop:function(e){var t=this;if(e.mediaBlocks.length>1){var n=new oe([],null,null,this.getIndex(),this.fileInfo()).createEmptySelectors();(t=new ge(n,e.mediaBlocks)).multiMedia=!0,t.copyVisibilityInfo(this.visibilityInfo()),this.setParent(t,this)}return delete e.mediaBlocks,delete e.mediaPath,t},evalNested:function(e){var t,n,i=e.mediaPath.concat([this]);for(t=0;t<i.length;t++){if(i[t].type!==this.type)return e.mediaBlocks.splice(t,1),this;n=i[t].features instanceof le?i[t].features.value:i[t].features,i[t]=Array.isArray(n)?n:[n]}return this.features=new le(this.permute(i).map((function(e){for(e=e.map((function(e){return e.toCSS?e:new se(e)})),t=e.length-1;t>0;t--)e.splice(t,0,new se("and"));return new we(e)}))),this.setParent(this.features,this),new ge([],[])},permute:function(e){if(0===e.length)return[];if(1===e.length)return e[0];for(var t=[],n=this.permute(e.slice(1)),i=0;i<n.length;i++)for(var r=0;r<e[0].length;r++)t.push([e[0][r]].concat(n[i]));return t},bubbleSelectors:function(e){e&&(this.rules=[new ge(A(e),[this.rules[0]])],this.setParent(this.rules,this))}},Se=function(e,t,n,i,r,s,a,o){var l,c=this,h=new oe([],null,null,this._index,this._fileInfo).createEmptySelectors();if(this.name=e,this.value=t instanceof u?t:t?new se(t):t,n){if(Array.isArray(n)){var f=this.declarationsBlock(n),p=!0;n.forEach((function(e){"Ruleset"===e.type&&e.rules&&(p=p&&c.declarationsBlock(e.rules,!0))})),f&&!a?(this.simpleBlock=!0,this.declarations=n):!p||1!==n.length||a||t?this.rules=n:(this.simpleBlock=!0,this.declarations=n[0].rules?n[0].rules:n)}else{!(f=this.declarationsBlock(n.rules))||a||t?(this.rules=[n],this.rules[0].selectors=new oe([],null,null,i,r).createEmptySelectors()):(this.simpleBlock=!0,this.declarations=n.rules)}if(!this.simpleBlock)for(l=0;l<this.rules.length;l++)this.rules[l].allowImports=!0;this.setParent(h,this),this.setParent(this.rules,this)}this._index=i,this._fileInfo=r,this.debugInfo=s,this.isRooted=a||!1,this.copyVisibilityInfo(o),this.allowRoot=!0};Se.prototype=Object.assign(new u,p(p({type:"AtRule"},xe),{declarationsBlock:function(e,t){return void 0===t&&(t=!1),t?e.filter((function(e){return"Declaration"===e.type||"Comment"===e.type})).length===e.length:e.filter((function(e){return("Declaration"===e.type||"Comment"===e.type)&&!e.merge})).length===e.length},keywordList:function(e){return!!Array.isArray(e)&&e.filter((function(e){return"Keyword"===e.type||"Comment"===e.type})).length===e.length},accept:function(e){var t=this.value,n=this.rules,i=this.declarations;n?this.rules=e.visitArray(n):i&&(this.declarations=e.visitArray(i)),t&&(this.value=e.visit(t))},isRulesetLike:function(){return this.rules||!this.isCharset()},isCharset:function(){return"@charset"===this.name},genCSS:function(e,t){var n=this.value,i=this.rules||this.declarations;t.add(this.name,this.fileInfo(),this.getIndex()),n&&(t.add(" "),n.genCSS(e,t)),this.simpleBlock?this.outputRuleset(e,t,this.declarations):i?this.outputRuleset(e,t,i):t.add(";")},eval:function(e){var t,n,i=this.value,r=this.rules||this.declarations;(t=e.mediaPath,n=e.mediaBlocks,e.mediaPath=[],e.mediaBlocks=[],i&&(i=i.eval(e)).value&&this.keywordList(i.value)&&(i=new se(i.value.map((function(e){return e.value})).join(", "),this.getIndex(),this.fileInfo())),r&&(r=this.evalRoot(e,r)),Array.isArray(r)&&r[0].rules&&Array.isArray(r[0].rules)&&r[0].rules.length)&&(!this.declarationsBlock(r[0].rules,!0)||this.isRooted||i||((0,e.pluginManager.less.visitors.ToCSSVisitor.prototype._mergeRules)(r[0].rules),(r=r[0].rules).forEach((function(e){return e.merge=!1}))));return this.simpleBlock&&r&&(r[0].functionRegistry=e.frames[0].functionRegistry.inherit(),r=r.map((function(t){return t.eval(e)}))),e.mediaPath=t,e.mediaBlocks=n,new Se(this.name,i,r,this.getIndex(),this.fileInfo(),this.debugInfo,this.isRooted,this.visibilityInfo())},evalRoot:function(e,t){var n=0,i=0,r=!0,s=!1;this.simpleBlock||(t=[t[0].eval(e)]);var a=[];if(e.frames.length>0)for(var o=function(t){var o=e.frames[t];if("Ruleset"===o.type&&o.rules&&o.rules.length>0&&o&&!o.root&&o.selectors&&o.selectors.length>0&&(a=a.concat(o.selectors)),a.length>0){for(var l="",u={add:function(e){l+=e}},c=0;c<a.length;c++)a[c].genCSS(e,u);/^&+$/.test(l.replace(/\s+/g,""))?(r=!1,i++):(s=!1,n++)}},l=0;l<e.frames.length;l++)o(l);var u=n>0&&i>0&&!s&&!r;return(this.isRooted&&n>0&&0===i&&!s&&r||!u)&&(t[0].root=!0),t},variable:function(e){if(this.rules)return ge.prototype.variable.call(this.rules[0],e)},find:function(){if(this.rules)return ge.prototype.find.apply(this.rules[0],arguments)},rulesets:function(){if(this.rules)return ge.prototype.rulesets.apply(this.rules[0])},outputRuleset:function(e,t,n){var i,r=n.length;if(e.tabLevel=1+(0|e.tabLevel),e.compress){for(t.add("{"),i=0;i<r;i++)n[i].genCSS(e,t);return t.add("}"),void e.tabLevel--}var s="\n".concat(Array(e.tabLevel).join("  ")),a="".concat(s,"  ");if(r){for(t.add(" {".concat(a)),n[0].genCSS(e,t),i=1;i<r;i++)t.add(a),n[i].genCSS(e,t);t.add("".concat(s,"}"))}else t.add(" {".concat(s,"}"));e.tabLevel--}}));var Ie=function(e,t){this.ruleset=e,this.frames=t,this.setParent(this.ruleset,this)};Ie.prototype=Object.assign(new u,{type:"DetachedRuleset",evalFirst:!0,accept:function(e){this.ruleset=e.visit(this.ruleset)},eval:function(e){var t=this.frames||A(e.frames);return new Ie(this.ruleset,t)},callEval:function(e){return this.ruleset.eval(this.frames?new B.Eval(e,this.frames.concat(e.frames)):e)}});var Ce=y,ke=function(e,t,n){this.op=e.trim(),this.operands=t,this.isSpaced=n};ke.prototype=Object.assign(new u,{type:"Operation",accept:function(e){this.operands=e.visitArray(this.operands)},eval:function(e){var t,n=this.operands[0].eval(e),i=this.operands[1].eval(e);if(e.isMathOn(this.op)){if(t="./"===this.op?"/":this.op,n instanceof be&&i instanceof c&&(n=n.toColor()),i instanceof be&&n instanceof c&&(i=i.toColor()),!n.operate||!i.operate){if((n instanceof ke||i instanceof ke)&&"/"===n.op&&e.math===Ce.PARENS_DIVISION)return new ke(this.op,[n,i],this.isSpaced);throw{type:"Operation",message:"Operation on an invalid type"}}return n.operate(e,t,i)}return new ke(this.op,[n,i],this.isSpaced)},genCSS:function(e,t){this.operands[0].genCSS(e,t),this.isSpaced&&t.add(" "),t.add(this.op),this.isSpaced&&t.add(" "),this.operands[1].genCSS(e,t)}});var Ae=function(){function e(e,t,n,i){this.name=e.toLowerCase(),this.index=n,this.context=t,this.currentFileInfo=i,this.func=t.frames[0].functionRegistry.get(this.name)}return e.prototype.isValid=function(){return Boolean(this.func)},e.prototype.call=function(e){var t=this;Array.isArray(e)||(e=[e]);var n=this.func.evalArgs;!1!==n&&(e=e.map((function(e){return e.eval(t.context)})));var i=function(e){return!("Comment"===e.type)};return e=e.filter(i).map((function(e){if("Expression"===e.type){var t=e.value.filter(i);return 1===t.length?e.parens&&"/"===t[0].op?e:t[0]:new we(t)}return e})),!1===n?this.func.apply(this,function(e,t,n){if(n||2===arguments.length)for(var i,r=0,s=t.length;r<s;r++)!i&&r in t||(i||(i=Array.prototype.slice.call(t,0,r)),i[r]=t[r]);return e.concat(i||Array.prototype.slice.call(t))}([this.context],e,!1)):this.func.apply(this,e)},e}(),_e=function(e,t,n,i){this.name=e,this.args=t,this.calc="calc"===e,this._index=n,this._fileInfo=i};_e.prototype=Object.assign(new u,{type:"Call",accept:function(e){this.args&&(this.args=e.visitArray(this.args))},eval:function(e){var t=this,n=e.mathOn;e.mathOn=!this.calc,(this.calc||e.inCalc)&&e.enterCalc();var i,r=function(){(t.calc||e.inCalc)&&e.exitCalc(),e.mathOn=n},s=new Ae(this.name,e,this.getIndex(),this.fileInfo());if(s.isValid())try{i=s.call(this.args),r()}catch(e){if(e.hasOwnProperty("line")&&e.hasOwnProperty("column"))throw e;throw{type:e.type||"Runtime",message:"Error evaluating function `".concat(this.name,"`").concat(e.message?": ".concat(e.message):""),index:this.getIndex(),filename:this.fileInfo().filename,line:e.lineNumber,column:e.columnNumber}}if(null!=i)return i instanceof u||(i=new se(i&&!0!==i?i.toString():null)),i._index=this._index,i._fileInfo=this._fileInfo,i;var a=this.args.map((function(t){return t.eval(e)}));return r(),new _e(this.name,a,this.getIndex(),this.fileInfo())},genCSS:function(e,t){t.add("".concat(this.name,"("),this.fileInfo(),this.getIndex());for(var n=0;n<this.args.length;n++)this.args[n].genCSS(e,t),n+1<this.args.length&&t.add(", ");t.add(")")}});var Pe=function(e,t,n){this.name=e,this._index=t,this._fileInfo=n};Pe.prototype=Object.assign(new u,{type:"Variable",eval:function(e){var t,n=this.name;if(0===n.indexOf("@@")&&(n="@".concat(new Pe(n.slice(1),this.getIndex(),this.fileInfo()).eval(e).value)),this.evaluating)throw{type:"Name",message:"Recursive variable definition for ".concat(n),filename:this.fileInfo().filename,index:this.getIndex()};if(this.evaluating=!0,t=this.find(e.frames,(function(t){var i=t.variable(n);if(i){if(i.important)e.importantScope[e.importantScope.length-1].important=i.important;return e.inCalc?new _e("_SELF",[i.value]).eval(e):i.value.eval(e)}})))return this.evaluating=!1,t;throw{type:"Name",message:"variable ".concat(n," is undefined"),filename:this.fileInfo().filename,index:this.getIndex()}},find:function(e,t){for(var n=0,i=void 0;n<e.length;n++)if(i=t.call(e,e[n]))return i;return null}});var Ee=function(e,t,n){this.name=e,this._index=t,this._fileInfo=n};Ee.prototype=Object.assign(new u,{type:"Property",eval:function(e){var t,n=this.name,i=e.pluginManager.less.visitors.ToCSSVisitor.prototype._mergeRules;if(this.evaluating)throw{type:"Name",message:"Recursive property reference for ".concat(n),filename:this.fileInfo().filename,index:this.getIndex()};if(this.evaluating=!0,t=this.find(e.frames,(function(t){var r,s=t.property(n);if(s){for(var a=0;a<s.length;a++)r=s[a],s[a]=new he(r.name,r.value,r.important,r.merge,r.index,r.currentFileInfo,r.inline,r.variable);if(i(s),(r=s[s.length-1]).important)e.importantScope[e.importantScope.length-1].important=r.important;return r=r.value.eval(e)}})))return this.evaluating=!1,t;throw{type:"Name",message:"Property '".concat(n,"' is undefined"),filename:this.currentFileInfo.filename,index:this.index}},find:function(e,t){for(var n=0,i=void 0;n<e.length;n++)if(i=t.call(e,e[n]))return i;return null}});var Re=function(e,t,n,i){this.key=e,this.op=t,this.value=n,this.cif=i};Re.prototype=Object.assign(new u,{type:"Attribute",eval:function(e){return new Re(this.key.eval?this.key.eval(e):this.key,this.op,this.value&&this.value.eval?this.value.eval(e):this.value,this.cif)},genCSS:function(e,t){t.add(this.toCSS(e))},toCSS:function(e){var t=this.key.toCSS?this.key.toCSS(e):this.key;return this.op&&(t+=this.op,t+=this.value.toCSS?this.value.toCSS(e):this.value),this.cif&&(t=t+" "+this.cif),"[".concat(t,"]")}});var Me=function(e,t,n,i,r){this.escaped=void 0===n||n,this.value=t||"",this.quote=e.charAt(0),this._index=i,this._fileInfo=r,this.variableRegex=/@\{([\w-]+)\}/g,this.propRegex=/\$\{([\w-]+)\}/g,this.allowRoot=n};Me.prototype=Object.assign(new u,{type:"Quoted",genCSS:function(e,t){this.escaped||t.add(this.quote,this.fileInfo(),this.getIndex()),t.add(this.value),this.escaped||t.add(this.quote)},containsVariables:function(){return this.value.match(this.variableRegex)},eval:function(e){var t=this,n=this.value;function i(e,t,n){var i=e;do{e=i.toString(),i=e.replace(t,n)}while(e!==i);return i}return n=i(n,this.variableRegex,(function(n,i,r){var s=new Pe("@".concat(null!=i?i:r),t.getIndex(),t.fileInfo()).eval(e,!0);return s instanceof Me?s.value:s.toCSS()})),n=i(n,this.propRegex,(function(n,i,r){var s=new Ee("$".concat(null!=i?i:r),t.getIndex(),t.fileInfo()).eval(e,!0);return s instanceof Me?s.value:s.toCSS()})),new Me(this.quote+n+this.quote,n,this.escaped,this.getIndex(),this.fileInfo())},compare:function(e){return"Quoted"!==e.type||this.escaped||e.escaped?e.toCSS&&this.toCSS()===e.toCSS()?0:void 0:u.numericCompare(this.value,e.value)}});var Oe=function(e,t,n,i){this.value=e,this._index=t,this._fileInfo=n,this.isEvald=i};Oe.prototype=Object.assign(new u,{type:"Url",accept:function(e){this.value=e.visit(this.value)},genCSS:function(e,t){t.add("url("),this.value.genCSS(e,t),t.add(")")},eval:function(e){var t,n=this.value.eval(e);if(!this.isEvald&&("string"==typeof(t=this.fileInfo()&&this.fileInfo().rootpath)&&"string"==typeof n.value&&e.pathRequiresRewrite(n.value)?(n.quote||(t=t.replace(/[()'"\s]/g,(function(e){return"\\".concat(e)}))),n.value=e.rewritePath(n.value,t)):n.value=e.normalizePath(n.value),e.urlArgs&&!n.value.match(/^\s*data:/))){var i=(-1===n.value.indexOf("?")?"?":"&")+e.urlArgs;-1!==n.value.indexOf("#")?n.value=n.value.replace("#","".concat(i,"#")):n.value+=i}return new Oe(n,this.getIndex(),this.fileInfo(),!0)}});var $e=function(e,t,n,i,r){this._index=n,this._fileInfo=i;var s=new oe([],null,null,this._index,this._fileInfo).createEmptySelectors();this.features=new le(t),this.rules=[new ge(s,e)],this.rules[0].allowImports=!0,this.copyVisibilityInfo(r),this.allowRoot=!0,this.setParent(s,this),this.setParent(this.features,this),this.setParent(this.rules,this)};$e.prototype=Object.assign(new Se,p(p({type:"Media"},xe),{genCSS:function(e,t){t.add("@media ",this._fileInfo,this._index),this.features.genCSS(e,t),this.outputRuleset(e,t,this.rules)},eval:function(e){e.mediaBlocks||(e.mediaBlocks=[],e.mediaPath=[]);var t=new $e(null,[],this._index,this._fileInfo,this.visibilityInfo());return this.debugInfo&&(this.rules[0].debugInfo=this.debugInfo,t.debugInfo=this.debugInfo),t.features=this.features.eval(e),e.mediaPath.push(t),e.mediaBlocks.push(t),this.rules[0].functionRegistry=e.frames[0].functionRegistry.inherit(),e.frames.unshift(this.rules[0]),t.rules=[this.rules[0].eval(e)],e.frames.shift(),e.mediaPath.pop(),0===e.mediaPath.length?t.evalTop(e):t.evalNested(e)}}));var Ve=function(e,t,n,i,r,s){if(this.options=n,this._index=i,this._fileInfo=r,this.path=e,this.features=t,this.allowRoot=!0,void 0!==this.options.less||this.options.inline)this.css=!this.options.less||this.options.inline;else{var a=this.getPath();a&&/[#.&?]css([?;].*)?$/.test(a)&&(this.css=!0)}this.copyVisibilityInfo(s),this.setParent(this.features,this),this.setParent(this.path,this)};Ve.prototype=Object.assign(new u,{type:"Import",accept:function(e){this.features&&(this.features=e.visit(this.features)),this.path=e.visit(this.path),this.options.isPlugin||this.options.inline||!this.root||(this.root=e.visit(this.root))},genCSS:function(e,t){this.css&&void 0===this.path._fileInfo.reference&&(t.add("@import ",this._fileInfo,this._index),this.path.genCSS(e,t),this.features&&(t.add(" "),this.features.genCSS(e,t)),t.add(";"))},getPath:function(){return this.path instanceof Oe?this.path.value.value:this.path.value},isVariableImport:function(){var e=this.path;return e instanceof Oe&&(e=e.value),!(e instanceof Me)||e.containsVariables()},evalForImport:function(e){var t=this.path;return t instanceof Oe&&(t=t.value),new Ve(t.eval(e),this.features,this.options,this._index,this._fileInfo,this.visibilityInfo())},evalPath:function(e){var t=this.path.eval(e),n=this._fileInfo;if(!(t instanceof Oe)){var i=t.value;n&&i&&e.pathRequiresRewrite(i)?t.value=e.rewritePath(i,n.rootpath):t.value=e.normalizePath(t.value)}return t},eval:function(e){var t=this.doEval(e);return(this.options.reference||this.blocksVisibility())&&(t.length||0===t.length?t.forEach((function(e){e.addVisibilityBlock()})):t.addVisibilityBlock()),t},doEval:function(e){var t,n,i=this.features&&this.features.eval(e);if(this.options.isPlugin){if(this.root&&this.root.eval)try{this.root.eval(e)}catch(e){throw e.message="Plugin error during evaluation",new V(e,this.root.imports,this.root.filename)}return(n=e.frames[0]&&e.frames[0].functionRegistry)&&this.root&&this.root.functions&&n.addMultiple(this.root.functions),[]}if(this.skip&&("function"==typeof this.skip&&(this.skip=this.skip()),this.skip))return[];if(this.features){var r=this.features.value;if(Array.isArray(r)&&r.length>=1)if("Expression"===(o=r[0]).type&&Array.isArray(o.value)&&o.value.length>=2)"Keyword"===(r=o.value)[0].type&&"layer"===r[0].value&&"Paren"===r[1].type&&(this.css=!1)}if(this.options.inline){var s=new se(this.root,0,{filename:this.importedFilename,reference:this.path._fileInfo&&this.path._fileInfo.reference},!0,!0);return this.features?new $e([s],this.features.value):[s]}if(this.css||this.layerCss){var a=new Ve(this.evalPath(e),i,this.options,this._index);if(this.layerCss&&(a.css=this.layerCss,a.path._fileInfo=this._fileInfo),!a.css&&this.error)throw this.error;return a}if(this.root){if(this.features){var o;r=this.features.value;if(Array.isArray(r)&&1===r.length)if("Expression"===(o=r[0]).type&&Array.isArray(o.value)&&o.value.length>=2)if("Keyword"===(r=o.value)[0].type&&"layer"===r[0].value&&"Paren"===r[1].type)return this.layerCss=!0,r[0]=new we(r.slice(0,2)),r.splice(1,1),r[0].noSpacing=!0,this}return(t=new ge(null,A(this.root.rules))).evalImports(e),this.features?new $e(t.rules,this.features.value):t.rules}if(this.features){r=this.features.value;if(Array.isArray(r)&&r.length>=1)if(r=r[0].value,Array.isArray(r)&&r.length>=2)if("Keyword"===r[0].type&&"layer"===r[0].value&&"Paren"===r[1].type)return this.css=!0,r[0]=new we(r.slice(0,2)),r.splice(1,1),r[0].noSpacing=!0,this}return[]}});var Fe=function(){};Fe.prototype=Object.assign(new u,{evaluateJavaScript:function(e,t){var n,i=this,r={};if(!t.javascriptEnabled)throw{message:"Inline JavaScript is not enabled. Is it set in your options?",filename:this.fileInfo().filename,index:this.getIndex()};e=e.replace(/@\{([\w-]+)\}/g,(function(e,n){return i.jsify(new Pe("@".concat(n),i.getIndex(),i.fileInfo()).eval(t))}));try{e=new Function("return (".concat(e,")"))}catch(t){throw{message:"JavaScript evaluation error: ".concat(t.message," from `").concat(e,"`"),filename:this.fileInfo().filename,index:this.getIndex()}}var s=t.frames[0].variables();for(var a in s)s.hasOwnProperty(a)&&(r[a.slice(1)]={value:s[a].value,toJS:function(){return this.value.eval(t).toCSS()}});try{n=e.call(r)}catch(e){throw{message:"JavaScript evaluation error: '".concat(e.name,": ").concat(e.message.replace(/["]/g,"'"),"'"),filename:this.fileInfo().filename,index:this.getIndex()}}return n},jsify:function(e){return Array.isArray(e.value)&&e.value.length>1?"[".concat(e.value.map((function(e){return e.toCSS()})).join(", "),"]"):e.toCSS()}});var Le=function(e,t,n,i){this.escaped=t,this.expression=e,this._index=n,this._fileInfo=i};Le.prototype=Object.assign(new Fe,{type:"JavaScript",eval:function(e){var t=this.evaluateJavaScript(this.expression,e),n=typeof t;return"number"!==n||isNaN(t)?"string"===n?new Me('"'.concat(t,'"'),t,this.escaped,this._index):Array.isArray(t)?new se(t.join(", ")):new se(t):new be(t)}});var je=function(e,t){this.key=e,this.value=t};je.prototype=Object.assign(new u,{type:"Assignment",accept:function(e){this.value=e.visit(this.value)},eval:function(e){return this.value.eval?new je(this.key,this.value.eval(e)):this},genCSS:function(e,t){t.add("".concat(this.key,"=")),this.value.genCSS?this.value.genCSS(e,t):t.add(this.value)}});var De=function(e,t,n,i,r){this.op=e.trim(),this.lvalue=t,this.rvalue=n,this._index=i,this.negate=r};De.prototype=Object.assign(new u,{type:"Condition",accept:function(e){this.lvalue=e.visit(this.lvalue),this.rvalue=e.visit(this.rvalue)},eval:function(e){var t=function(e,t,n){switch(e){case"and":return t&&n;case"or":return t||n;default:switch(u.compare(t,n)){case-1:return"<"===e||"=<"===e||"<="===e;case 0:return"="===e||">="===e||"=<"===e||"<="===e;case 1:return">"===e||">="===e;default:return!1}}}(this.op,this.lvalue.eval(e),this.rvalue.eval(e));return this.negate?!t:t}});var Ne=function(e,t,n,i,r,s){this.op=e.trim(),this.lvalue=t,this.mvalue=n,this.op2=i?i.trim():null,this.rvalue=r,this._index=s,this.mvalues=[]};Ne.prototype=Object.assign(new u,{type:"QueryInParens",accept:function(e){this.lvalue=e.visit(this.lvalue),this.mvalue=e.visit(this.mvalue),this.rvalue&&(this.rvalue=e.visit(this.rvalue))},eval:function(e){var t,n;this.lvalue=this.lvalue.eval(e);for(var i=0;(n=e.frames[i])&&("Ruleset"!==n.type||!(t=n.rules.find((function(e){return!!(e instanceof he&&e.variable)}))));i++);return this.mvalueCopy||(this.mvalueCopy=C(this.mvalue)),t?(this.mvalue=this.mvalueCopy,this.mvalue=this.mvalue.eval(e),this.mvalues.push(this.mvalue)):this.mvalue=this.mvalue.eval(e),this.rvalue&&(this.rvalue=this.rvalue.eval(e)),this},genCSS:function(e,t){this.lvalue.genCSS(e,t),t.add(" "+this.op+" "),this.mvalues.length>0&&(this.mvalue=this.mvalues.shift()),this.mvalue.genCSS(e,t),this.rvalue&&(t.add(" "+this.op2+" "),this.rvalue.genCSS(e,t))}});var Be=function(e,t,n,i,r){this._index=n,this._fileInfo=i;var s=new oe([],null,null,this._index,this._fileInfo).createEmptySelectors();this.features=new le(t),this.rules=[new ge(s,e)],this.rules[0].allowImports=!0,this.copyVisibilityInfo(r),this.allowRoot=!0,this.setParent(s,this),this.setParent(this.features,this),this.setParent(this.rules,this)};Be.prototype=Object.assign(new Se,p(p({type:"Container"},xe),{genCSS:function(e,t){t.add("@container ",this._fileInfo,this._index),this.features.genCSS(e,t),this.outputRuleset(e,t,this.rules)},eval:function(e){e.mediaBlocks||(e.mediaBlocks=[],e.mediaPath=[]);var t=new Be(null,[],this._index,this._fileInfo,this.visibilityInfo());return this.debugInfo&&(this.rules[0].debugInfo=this.debugInfo,t.debugInfo=this.debugInfo),t.features=this.features.eval(e),e.mediaPath.push(t),e.mediaBlocks.push(t),this.rules[0].functionRegistry=e.frames[0].functionRegistry.inherit(),e.frames.unshift(this.rules[0]),t.rules=[this.rules[0].eval(e)],e.frames.shift(),e.mediaPath.pop(),0===e.mediaPath.length?t.evalTop(e):t.evalNested(e)}}));var Ue=function(e){this.value=e};Ue.prototype=Object.assign(new u,{type:"UnicodeDescriptor"});var qe=function(e){this.value=e};qe.prototype=Object.assign(new u,{type:"Negative",genCSS:function(e,t){t.add("-"),this.value.genCSS(e,t)},eval:function(e){return e.isMathOn()?new ke("*",[new be(-1),this.value]).eval(e):new qe(this.value.eval(e))}});var Te=function(e,t,n,i,r){switch(this.selector=e,this.option=t,this.object_id=Te.next_id++,this.parent_ids=[this.object_id],this._index=n,this._fileInfo=i,this.copyVisibilityInfo(r),this.allowRoot=!0,t){case"!all":case"all":this.allowBefore=!0,this.allowAfter=!0;break;default:this.allowBefore=!1,this.allowAfter=!1}this.setParent(this.selector,this)};Te.prototype=Object.assign(new u,{type:"Extend",accept:function(e){this.selector=e.visit(this.selector)},eval:function(e){return new Te(this.selector.eval(e),this.option,this.getIndex(),this.fileInfo(),this.visibilityInfo())},clone:function(e){return new Te(this.selector,this.option,this.getIndex(),this.fileInfo(),this.visibilityInfo())},findSelfSelectors:function(e){var t,n,i=[];for(t=0;t<e.length;t++)n=e[t].elements,t>0&&n.length&&""===n[0].combinator.value&&(n[0].combinator.value=" "),i=i.concat(e[t].elements);this.selfSelectors=[new oe(i)],this.selfSelectors[0].copyVisibilityInfo(this.visibilityInfo())}}),Te.next_id=0;var ze=function(e,t,n){this.variable=e,this._index=t,this._fileInfo=n,this.allowRoot=!0};ze.prototype=Object.assign(new u,{type:"VariableCall",eval:function(e){var t,n=new Pe(this.variable,this.getIndex(),this.fileInfo()).eval(e),i=new V({message:"Could not evaluate variable call ".concat(this.variable)});if(!n.ruleset){if(n.rules)t=n;else if(Array.isArray(n))t=new ge("",n);else{if(!Array.isArray(n.value))throw i;t=new ge("",n.value)}n=new Ie(t)}if(n.ruleset)return n.callEval(e);throw i}});var Ge=function(e,t,n,i){this.value=e,this.lookups=t,this._index=n,this._fileInfo=i};Ge.prototype=Object.assign(new u,{type:"NamespaceValue",eval:function(e){var t,n,i=this.value.eval(e);for(t=0;t<this.lookups.length;t++){if(n=this.lookups[t],Array.isArray(i)&&(i=new ge([new oe],i)),""===n)i=i.lastDeclaration();else if("@"===n.charAt(0)){if("@"===n.charAt(1)&&(n="@".concat(new Pe(n.substr(1)).eval(e).value)),i.variables&&(i=i.variable(n)),!i)throw{type:"Name",message:"variable ".concat(n," not found"),filename:this.fileInfo().filename,index:this.getIndex()}}else{if(n="$@"===n.substring(0,2)?"$".concat(new Pe(n.substr(1)).eval(e).value):"$"===n.charAt(0)?n:"$".concat(n),i.properties&&(i=i.property(n)),!i)throw{type:"Name",message:'property "'.concat(n.substr(1),'" not found'),filename:this.fileInfo().filename,index:this.getIndex()};i=i[i.length-1]}i.value&&(i=i.eval(e).value),i.ruleset&&(i=i.ruleset.eval(e))}return i}});var We=function(e,t,n,i,r,s,a){this.name=e||"anonymous mixin",this.selectors=[new oe([new g(null,e,!1,this._index,this._fileInfo)])],this.params=t,this.condition=i,this.variadic=r,this.arity=t.length,this.rules=n,this._lookups={};var o=[];this.required=t.reduce((function(e,t){return!t.name||t.name&&!t.value?e+1:(o.push(t.name),e)}),0),this.optionalParameters=o,this.frames=s,this.copyVisibilityInfo(a),this.allowRoot=!0};We.prototype=Object.assign(new ge,{type:"MixinDefinition",evalFirst:!0,accept:function(e){this.params&&this.params.length&&(this.params=e.visitArray(this.params)),this.rules=e.visitArray(this.rules),this.condition&&(this.condition=e.visit(this.condition))},evalParams:function(e,t,n,i){var r,s,a,o,l,u,c,h,f=new ge(null,null),p=A(this.params),v=0;if(t.frames&&t.frames[0]&&t.frames[0].functionRegistry&&(f.functionRegistry=t.frames[0].functionRegistry.inherit()),t=new B.Eval(t,[f].concat(t.frames)),n)for(v=(n=A(n)).length,a=0;a<v;a++)if(u=(s=n[a])&&s.name){for(c=!1,o=0;o<p.length;o++)if(!i[o]&&u===p[o].name){i[o]=s.value.eval(e),f.prependRule(new he(u,s.value.eval(e))),c=!0;break}if(c){n.splice(a,1),a--;continue}throw{type:"Runtime",message:"Named argument for ".concat(this.name," ").concat(n[a].name," not found")}}for(h=0,a=0;a<p.length;a++)if(!i[a]){if(s=n&&n[h],u=p[a].name)if(p[a].variadic){for(r=[],o=h;o<v;o++)r.push(n[o].value.eval(e));f.prependRule(new he(u,new we(r).eval(e)))}else{if(l=s&&s.value)l=Array.isArray(l)?new Ie(new ge("",l)):l.eval(e);else{if(!p[a].value)throw{type:"Runtime",message:"wrong number of arguments for ".concat(this.name," (").concat(v," for ").concat(this.arity,")")};l=p[a].value.eval(t),f.resetCache()}f.prependRule(new he(u,l)),i[a]=l}if(p[a].variadic&&n)for(o=h;o<v;o++)i[o]=n[o].value.eval(e);h++}return f},makeImportant:function(){var e=this.rules?this.rules.map((function(e){return e.makeImportant?e.makeImportant(!0):e})):this.rules;return new We(this.name,this.params,e,this.condition,this.variadic,this.frames)},eval:function(e){return new We(this.name,this.params,this.rules,this.condition,this.variadic,this.frames||A(e.frames))},evalCall:function(e,t,n){var i,r,s=[],a=this.frames?this.frames.concat(e.frames):e.frames,o=this.evalParams(e,new B.Eval(e,a),t,s);return o.prependRule(new he("@arguments",new we(s).eval(e))),i=A(this.rules),(r=new ge(null,i)).originalRuleset=this,r=r.eval(new B.Eval(e,[this,o].concat(a))),n&&(r=r.makeImportant()),r},matchCondition:function(e,t){return!(this.condition&&!this.condition.eval(new B.Eval(t,[this.evalParams(t,new B.Eval(t,this.frames?this.frames.concat(t.frames):t.frames),e,[])].concat(this.frames||[]).concat(t.frames))))},matchArgs:function(e,t){var n,i=e&&e.length||0,r=this.optionalParameters,s=e?e.reduce((function(e,t){return r.indexOf(t.name)<0?e+1:e}),0):0;if(this.variadic){if(s<this.required-1)return!1}else{if(s<this.required)return!1;if(i>this.params.length)return!1}n=Math.min(s,this.arity);for(var a=0;a<n;a++)if(!this.params[a].name&&!this.params[a].variadic&&e[a].value.eval(t).toCSS()!=this.params[a].value.eval(t).toCSS())return!1;return!0}});var Je=function(e,t,n,i,r){this.selector=new oe(e),this.arguments=t||[],this._index=n,this._fileInfo=i,this.important=r,this.allowRoot=!0,this.setParent(this.selector,this)};Je.prototype=Object.assign(new u,{type:"MixinCall",accept:function(e){this.selector&&(this.selector=e.visit(this.selector)),this.arguments.length&&(this.arguments=e.visitArray(this.arguments))},eval:function(e){var t,n,i,r,s,a,o,l,u,c,h,f,p,v,d,m=[],g=[],y=!1,b=[],w=[];function x(t,n){var i,r,s;for(i=0;i<2;i++){for(w[i]=!0,me.value(i),r=0;r<n.length&&w[i];r++)(s=n[r]).matchCondition&&(w[i]=w[i]&&s.matchCondition(null,e));t.matchCondition&&(w[i]=w[i]&&t.matchCondition(m,e))}return w[0]||w[1]?w[0]!=w[1]?w[1]?1:2:0:-1}for(this.selector=this.selector.eval(e),a=0;a<this.arguments.length;a++)if(s=(r=this.arguments[a]).value.eval(e),r.expand&&Array.isArray(s.value))for(s=s.value,o=0;o<s.length;o++)m.push({value:s[o]});else m.push({name:r.name,value:s});for(d=function(t){return t.matchArgs(null,e)},a=0;a<e.frames.length;a++)if((t=e.frames[a].find(this.selector,null,d)).length>0){for(c=!0,o=0;o<t.length;o++){for(n=t[o].rule,i=t[o].path,u=!1,l=0;l<e.frames.length;l++)if(!(n instanceof We)&&n===(e.frames[l].originalRuleset||e.frames[l])){u=!0;break}u||n.matchArgs(m,e)&&(-1!==(h={mixin:n,group:x(n,i)}).group&&b.push(h),y=!0)}for(me.reset(),p=[0,0,0],o=0;o<b.length;o++)p[b[o].group]++;if(p[0]>0)f=2;else if(f=1,p[1]+p[2]>1)throw{type:"Runtime",message:"Ambiguous use of `default()` found when matching for `".concat(this.format(m),"`"),index:this.getIndex(),filename:this.fileInfo().filename};for(o=0;o<b.length;o++)if(0===(h=b[o].group)||h===f)try{(n=b[o].mixin)instanceof We||(v=n.originalRuleset||n,(n=new We("",[],n.rules,null,!1,null,v.visibilityInfo())).originalRuleset=v);var S=n.evalCall(e,m,this.important).rules;this._setVisibilityToReplacement(S),Array.prototype.push.apply(g,S)}catch(e){throw{message:e.message,index:this.getIndex(),filename:this.fileInfo().filename,stack:e.stack}}if(y)return g}throw c?{type:"Runtime",message:"No matching definition was found for `".concat(this.format(m),"`"),index:this.getIndex(),filename:this.fileInfo().filename}:{type:"Name",message:"".concat(this.selector.toCSS().trim()," is undefined"),index:this.getIndex(),filename:this.fileInfo().filename}},_setVisibilityToReplacement:function(e){var t;if(this.blocksVisibility())for(t=0;t<e.length;t++)e[t].addVisibilityBlock()},format:function(e){return"".concat(this.selector.toCSS().trim(),"(").concat(e?e.map((function(e){var t="";return e.name&&(t+="".concat(e.name,":")),e.value.toCSS?t+=e.value.toCSS():t+="???",t})).join(", "):"",")")}});var He={Node:u,Color:c,AtRule:Se,DetachedRuleset:Ie,Operation:ke,Dimension:be,Unit:ye,Keyword:ue,Variable:Pe,Property:Ee,Ruleset:ge,Element:g,Attribute:Re,Combinator:m,Selector:oe,Quoted:Me,Expression:we,Declaration:he,Call:_e,URL:Oe,Import:Ve,Comment:de,Anonymous:se,Value:le,JavaScript:Le,Assignment:je,Condition:De,Paren:v,Media:$e,Container:Be,QueryInParens:Ne,UnicodeDescriptor:Ue,Negative:qe,Extend:Te,VariableCall:ze,NamespaceValue:Ge,mixin:{Call:Je,Definition:We}},Ke=function(){function e(){}return e.prototype.getPath=function(e){var t=e.lastIndexOf("?");return t>0&&(e=e.slice(0,t)),(t=e.lastIndexOf("/"))<0&&(t=e.lastIndexOf("\\")),t<0?"":e.slice(0,t+1)},e.prototype.tryAppendExtension=function(e,t){return/(\.[a-z]*$)|([?;].*)$/.test(e)?e:e+t},e.prototype.tryAppendLessExtension=function(e){return this.tryAppendExtension(e,".less")},e.prototype.supportsSync=function(){return!1},e.prototype.alwaysMakePathsAbsolute=function(){return!1},e.prototype.isPathAbsolute=function(e){return/^(?:[a-z-]+:|\/|\\|#)/i.test(e)},e.prototype.join=function(e,t){return e?e+t:t},e.prototype.pathDiff=function(e,t){var n,i,r,s,a=this.extractUrlParts(e),o=this.extractUrlParts(t),l="";if(a.hostPart!==o.hostPart)return"";for(i=Math.max(o.directories.length,a.directories.length),n=0;n<i&&o.directories[n]===a.directories[n];n++);for(s=o.directories.slice(n),r=a.directories.slice(n),n=0;n<s.length-1;n++)l+="../";for(n=0;n<r.length-1;n++)l+="".concat(r[n],"/");return l},e.prototype.extractUrlParts=function(e,t){var n,i,r=/^((?:[a-z-]+:)?\/{2}(?:[^/?#]*\/)|([/\\]))?((?:[^/\\?#]*[/\\])*)([^/\\?#]*)([#?].*)?$/i,s=e.match(r),a={},o=[],l=[];if(!s)throw new Error("Could not parse sheet href - '".concat(e,"'"));if(t&&(!s[1]||s[2])){if(!(i=t.match(r)))throw new Error("Could not parse page url - '".concat(t,"'"));s[1]=s[1]||i[1]||"",s[2]||(s[3]=i[3]+s[3])}if(s[3])for(o=s[3].replace(/\\/g,"/").split("/"),n=0;n<o.length;n++)".."===o[n]?l.pop():"."!==o[n]&&l.push(o[n]);return a.hostPart=s[1],a.directories=l,a.rawPath=(s[1]||"")+o.join("/"),a.path=(s[1]||"")+l.join("/"),a.filename=s[4],a.fileUrl=a.path+(s[4]||""),a.url=a.fileUrl+(s[5]||""),a},e}(),Qe=function(){function e(){this.require=function(){return null}}return e.prototype.evalPlugin=function(e,t,n,i,r){var s,a,o,l,u,c;l=t.pluginManager,r&&(u="string"==typeof r?r:r.filename);var h=(new this.less.FileManager).extractUrlParts(u).filename;if(u&&(a=l.get(u))){if(c=this.trySetOptions(a,u,h,i))return c;try{a.use&&a.use.call(this.context,a)}catch(e){return e.message=e.message||"Error during @plugin call",new V(e,n,u)}return a}o={exports:{},pluginManager:l,fileInfo:r},s=ne.create();try{new Function("module","require","registerPlugin","functions","tree","less","fileInfo",e)(o,this.require(u),(function(e){a=e}),s,this.less.tree,this.less,r)}catch(e){return new V(e,n,u)}if(a||(a=o.exports),(a=this.validatePlugin(a,u,h))instanceof V)return a;if(!a)return new V({message:"Not a valid plugin"},n,u);if(a.imports=n,a.filename=u,(!a.minVersion||this.compareVersion("3.0.0",a.minVersion)<0)&&(c=this.trySetOptions(a,u,h,i)))return c;if(l.addPlugin(a,r.filename,s),a.functions=s.getLocalFunctions(),c=this.trySetOptions(a,u,h,i))return c;try{a.use&&a.use.call(this.context,a)}catch(e){return e.message=e.message||"Error during @plugin call",new V(e,n,u)}return a},e.prototype.trySetOptions=function(e,t,n,i){if(i&&!e.setOptions)return new V({message:"Options have been provided but the plugin ".concat(n," does not support any options.")});try{e.setOptions&&e.setOptions(i)}catch(e){return new V(e)}},e.prototype.validatePlugin=function(e,t,n){return e?("function"==typeof e&&(e=new e),e.minVersion&&this.compareVersion(e.minVersion,this.less.version)<0?new V({message:"Plugin ".concat(n," requires version ").concat(this.versionToString(e.minVersion))}):e):null},e.prototype.compareVersion=function(e,t){"string"==typeof e&&(e=e.match(/^(\d+)\.?(\d+)?\.?(\d+)?/)).shift();for(var n=0;n<e.length;n++)if(e[n]!==t[n])return parseInt(e[n])>parseInt(t[n])?-1:1;return 0},e.prototype.versionToString=function(e){for(var t="",n=0;n<e.length;n++)t+=(t?".":"")+e[n];return t},e.prototype.printUsage=function(e){for(var t=0;t<e.length;t++){var n=e[t];n.printUsage&&n.printUsage()}},e}();function Ze(e,t,n,i){return t.eval(e)?n.eval(e):i?i.eval(e):new se}function Xe(e,t){try{return t.eval(e),ue.True}catch(e){return ue.False}}Ze.evalArgs=!1,Xe.evalArgs=!1;var Ye,et={isdefined:Xe,boolean:function(e){return e?ue.True:ue.False},if:Ze};function tt(e){return Math.min(1,Math.max(0,e))}function nt(e,t){var n=Ye.hsla(t.h,t.s,t.l,t.a);if(n)return e.value&&/^(rgb|hsl)/.test(e.value)?n.value=e.value:n.value="rgb",n}function it(e){if(e.toHSL)return e.toHSL();throw new Error("Argument cannot be evaluated to a color")}function rt(e){if(e.toHSV)return e.toHSV();throw new Error("Argument cannot be evaluated to a color")}function st(e){if(e instanceof be)return parseFloat(e.unit.is("%")?e.value/100:e.value);if("number"==typeof e)return e;throw{type:"Argument",message:"color functions take numbers as parameters"}}var at=Ye={rgb:function(e,t,n){var i=1;if(e instanceof we){var r=e.value;if(e=r[0],t=r[1],(n=r[2])instanceof ke){var s=n;n=s.operands[0],i=s.operands[1]}}var a=Ye.rgba(e,t,n,i);if(a)return a.value="rgb",a},rgba:function(e,t,n,i){try{if(e instanceof c)return i=t?st(t):e.alpha,new c(e.rgb,i,"rgba");var r=[e,t,n].map((function(e){return n=255,(t=e)instanceof be&&t.unit.is("%")?parseFloat(t.value*n/100):st(t);var t,n}));return i=st(i),new c(r,i,"rgba")}catch(e){}},hsl:function(e,t,n){var i=1;if(e instanceof we){var r=e.value;if(e=r[0],t=r[1],(n=r[2])instanceof ke){var s=n;n=s.operands[0],i=s.operands[1]}}var a=Ye.hsla(e,t,n,i);if(a)return a.value="hsl",a},hsla:function(e,t,n,i){var r,s;function a(e){return 6*(e=e<0?e+1:e>1?e-1:e)<1?r+(s-r)*e*6:2*e<1?s:3*e<2?r+(s-r)*(2/3-e)*6:r}try{if(e instanceof c)return i=t?st(t):e.alpha,new c(e.rgb,i,"hsla");e=st(e)%360/360,t=tt(st(t)),n=tt(st(n)),i=tt(st(i)),r=2*n-(s=n<=.5?n*(t+1):n+t-n*t);var o=[255*a(e+1/3),255*a(e),255*a(e-1/3)];return i=st(i),new c(o,i,"hsla")}catch(e){}},hsv:function(e,t,n){return Ye.hsva(e,t,n,1)},hsva:function(e,t,n,i){var r,s;e=st(e)%360/360*360,t=st(t),n=st(n),i=st(i);var a=[n,n*(1-t),n*(1-(s=e/60-(r=Math.floor(e/60%6)))*t),n*(1-(1-s)*t)],o=[[0,3,1],[2,0,1],[1,0,3],[1,2,0],[3,1,0],[0,1,2]];return Ye.rgba(255*a[o[r][0]],255*a[o[r][1]],255*a[o[r][2]],i)},hue:function(e){return new be(it(e).h)},saturation:function(e){return new be(100*it(e).s,"%")},lightness:function(e){return new be(100*it(e).l,"%")},hsvhue:function(e){return new be(rt(e).h)},hsvsaturation:function(e){return new be(100*rt(e).s,"%")},hsvvalue:function(e){return new be(100*rt(e).v,"%")},red:function(e){return new be(e.rgb[0])},green:function(e){return new be(e.rgb[1])},blue:function(e){return new be(e.rgb[2])},alpha:function(e){return new be(it(e).a)},luma:function(e){return new be(e.luma()*e.alpha*100,"%")},luminance:function(e){var t=.2126*e.rgb[0]/255+.7152*e.rgb[1]/255+.0722*e.rgb[2]/255;return new be(t*e.alpha*100,"%")},saturate:function(e,t,n){if(!e.rgb)return null;var i=it(e);return void 0!==n&&"relative"===n.value?i.s+=i.s*t.value/100:i.s+=t.value/100,i.s=tt(i.s),nt(e,i)},desaturate:function(e,t,n){var i=it(e);return void 0!==n&&"relative"===n.value?i.s-=i.s*t.value/100:i.s-=t.value/100,i.s=tt(i.s),nt(e,i)},lighten:function(e,t,n){var i=it(e);return void 0!==n&&"relative"===n.value?i.l+=i.l*t.value/100:i.l+=t.value/100,i.l=tt(i.l),nt(e,i)},darken:function(e,t,n){var i=it(e);return void 0!==n&&"relative"===n.value?i.l-=i.l*t.value/100:i.l-=t.value/100,i.l=tt(i.l),nt(e,i)},fadein:function(e,t,n){var i=it(e);return void 0!==n&&"relative"===n.value?i.a+=i.a*t.value/100:i.a+=t.value/100,i.a=tt(i.a),nt(e,i)},fadeout:function(e,t,n){var i=it(e);return void 0!==n&&"relative"===n.value?i.a-=i.a*t.value/100:i.a-=t.value/100,i.a=tt(i.a),nt(e,i)},fade:function(e,t){var n=it(e);return n.a=t.value/100,n.a=tt(n.a),nt(e,n)},spin:function(e,t){var n=it(e),i=(n.h+t.value)%360;return n.h=i<0?360+i:i,nt(e,n)},mix:function(e,t,n){n||(n=new be(50));var i=n.value/100,r=2*i-1,s=it(e).a-it(t).a,a=((r*s==-1?r:(r+s)/(1+r*s))+1)/2,o=1-a,l=[e.rgb[0]*a+t.rgb[0]*o,e.rgb[1]*a+t.rgb[1]*o,e.rgb[2]*a+t.rgb[2]*o],u=e.alpha*i+t.alpha*(1-i);return new c(l,u)},greyscale:function(e){return Ye.desaturate(e,new be(100))},contrast:function(e,t,n,i){if(!e.rgb)return null;if(void 0===n&&(n=Ye.rgba(255,255,255,1)),void 0===t&&(t=Ye.rgba(0,0,0,1)),t.luma()>n.luma()){var r=n;n=t,t=r}return i=void 0===i?.43:st(i),e.luma()<i?n:t},argb:function(e){return new se(e.toARGB())},color:function(e){if(e instanceof Me&&/^#([A-Fa-f0-9]{8}|[A-Fa-f0-9]{6}|[A-Fa-f0-9]{3,4})$/i.test(e.value)){var t=e.value.slice(1);return new c(t,void 0,"#".concat(t))}if(e instanceof c||(e=c.fromKeyword(e.value)))return e.value=void 0,e;throw{type:"Argument",message:"argument must be a color keyword or 3|4|6|8 digit hex e.g. #FFF"}},tint:function(e,t){return Ye.mix(Ye.rgb(255,255,255),e,t)},shade:function(e,t){return Ye.mix(Ye.rgb(0,0,0),e,t)}};function ot(e,t,n){var i,r,s,a,o=t.alpha,l=n.alpha,u=[];s=l+o*(1-l);for(var h=0;h<3;h++)a=e(i=t.rgb[h]/255,r=n.rgb[h]/255),s&&(a=(l*r+o*(i-l*(i+r-a)))/s),u[h]=255*a;return new c(u,s)}var lt={multiply:function(e,t){return e*t},screen:function(e,t){return e+t-e*t},overlay:function(e,t){return(e*=2)<=1?lt.multiply(e,t):lt.screen(e-1,t)},softlight:function(e,t){var n=1,i=e;return t>.5&&(i=1,n=e>.25?Math.sqrt(e):((16*e-12)*e+4)*e),e-(1-2*t)*i*(n-e)},hardlight:function(e,t){return lt.overlay(t,e)},difference:function(e,t){return Math.abs(e-t)},exclusion:function(e,t){return e+t-2*e*t},average:function(e,t){return(e+t)/2},negation:function(e,t){return 1-Math.abs(e+t-1)}};for(var ut in lt)lt.hasOwnProperty(ut)&&(ot[ut]=ot.bind(null,lt[ut]));var ct=function(e){return Array.isArray(e.value)?e.value:Array(e)},ht={_SELF:function(e){return e},"~":function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return 1===e.length?e[0]:new le(e)},extract:function(e,t){return t=t.value-1,ct(e)[t]},length:function(e){return new be(ct(e).length)},range:function(e,t,n){var i,r,s=1,a=[];t?(r=t,i=e.value,n&&(s=n.value)):(i=1,r=e);for(var o=i;o<=r.value;o+=s)a.push(new be(o,r.unit));return new we(a)},each:function(e,t){var n,i,r=this,s=[],a=function(e){return e instanceof u?e.eval(r.context):e};i=!e.value||e instanceof Me?e.ruleset?a(e.ruleset).rules:e.rules?e.rules.map(a):Array.isArray(e)?e.map(a):[a(e)]:Array.isArray(e.value)?e.value.map(a):[a(e.value)];var o="@value",l="@key",c="@index";t.params?(o=t.params[0]&&t.params[0].name,l=t.params[1]&&t.params[1].name,c=t.params[2]&&t.params[2].name,t=t.rules):t=t.ruleset;for(var h=0;h<i.length;h++){var f=void 0,p=void 0,v=i[h];v instanceof he?(f="string"==typeof v.name?v.name:v.name[0].value,p=v.value):(f=new be(h+1),p=v),v instanceof de||(n=t.rules.slice(0),o&&n.push(new he(o,p,!1,!1,this.index,this.currentFileInfo)),c&&n.push(new he(c,new be(h+1),!1,!1,this.index,this.currentFileInfo)),l&&n.push(new he(l,f,!1,!1,this.index,this.currentFileInfo)),s.push(new ge([new oe([new g("","&")])],n,t.strictImports,t.visibilityInfo())))}return new ge([new oe([new g("","&")])],s,t.strictImports,t.visibilityInfo()).eval(this.context)}},ft=function(e,t,n){if(!(n instanceof be))throw{type:"Argument",message:"argument must be a number"};return null===t?t=n.unit:n=n.unify(),new be(e(parseFloat(n.value)),t)},pt={ceil:null,floor:null,sqrt:null,abs:null,tan:"",sin:"",cos:"",atan:"rad",asin:"rad",acos:"rad"};for(var vt in pt)pt.hasOwnProperty(vt)&&(pt[vt]=ft.bind(null,Math[vt],pt[vt]));pt.round=function(e,t){var n=void 0===t?0:t.value;return ft((function(e){return e.toFixed(n)}),null,e)};var dt=function(e,t){var n,i,r,s,a,o,l,u,c=this;switch((t=Array.prototype.slice.call(t)).length){case 0:throw{type:"Argument",message:"one or more arguments required"}}var h=[],f={};for(n=0;n<t.length;n++){if(!((r=t[n])instanceof be)){if(Array.isArray(t[n].value)){Array.prototype.push.apply(t,Array.prototype.slice.call(t[n].value));continue}throw{type:"Argument",message:"incompatible types"}}if(l=""!==(o=""===(s=""===r.unit.toString()&&void 0!==u?new be(r.value,u).unify():r.unify()).unit.toString()&&void 0!==l?l:s.unit.toString())&&void 0===l||""!==o&&""===h[0].unify().unit.toString()?o:l,u=""!==o&&void 0===u?r.unit.toString():u,void 0!==(i=void 0!==f[""]&&""!==o&&o===l?f[""]:f[o]))a=""===h[i].unit.toString()&&void 0!==u?new be(h[i].value,u).unify():h[i].unify(),(e&&s.value<a.value||!e&&s.value>a.value)&&(h[i]=r);else{if(void 0!==l&&o!==l)throw{type:"Argument",message:"incompatible types"};f[o]=h.length,h.push(r)}}return 1==h.length?h[0]:(t=h.map((function(e){return e.toCSS(c.context)})).join(this.context.compress?",":", "),new se("".concat(e?"min":"max","(").concat(t,")")))},mt={min:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];try{return dt.call(this,!0,e)}catch(e){}},max:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];try{return dt.call(this,!1,e)}catch(e){}},convert:function(e,t){return e.convertTo(t.value)},pi:function(){return new be(Math.PI)},mod:function(e,t){return new be(e.value%t.value,e.unit)},pow:function(e,t){if("number"==typeof e&&"number"==typeof t)e=new be(e),t=new be(t);else if(!(e instanceof be&&t instanceof be))throw{type:"Argument",message:"arguments must be numbers"};return new be(Math.pow(e.value,t.value),e.unit)},percentage:function(e){return ft((function(e){return 100*e}),"%",e)}},gt={e:function(e){return new Me('"',e instanceof Le?e.evaluated:e.value,!0)},escape:function(e){return new se(encodeURI(e.value).replace(/=/g,"%3D").replace(/:/g,"%3A").replace(/#/g,"%23").replace(/;/g,"%3B").replace(/\(/g,"%28").replace(/\)/g,"%29"))},replace:function(e,t,n,i){var r=e.value;return n="Quoted"===n.type?n.value:n.toCSS(),r=r.replace(new RegExp(t.value,i?i.value:""),n),new Me(e.quote||"",r,e.escaped)},"%":function(e){for(var t=Array.prototype.slice.call(arguments,1),n=e.value,i=function(e){n=n.replace(/%[sda]/i,(function(n){var i="Quoted"===t[e].type&&n.match(/s/i)?t[e].value:t[e].toCSS();return n.match(/[A-Z]$/)?encodeURIComponent(i):i}))},r=0;r<t.length;r++)i(r);return n=n.replace(/%%/g,"%"),new Me(e.quote||"",n,e.escaped)}},yt=function(e,t){return e instanceof t?ue.True:ue.False},bt=function(e,t){if(void 0===t)throw{type:"Argument",message:"missing the required second argument to isunit."};if("string"!=typeof(t="string"==typeof t.value?t.value:t))throw{type:"Argument",message:"Second argument to isunit should be a unit or a string."};return e instanceof be&&e.unit.is(t)?ue.True:ue.False},wt={isruleset:function(e){return yt(e,Ie)},iscolor:function(e){return yt(e,c)},isnumber:function(e){return yt(e,be)},isstring:function(e){return yt(e,Me)},iskeyword:function(e){return yt(e,ue)},isurl:function(e){return yt(e,Oe)},ispixel:function(e){return bt(e,"px")},ispercentage:function(e){return bt(e,"%")},isem:function(e){return bt(e,"em")},isunit:bt,unit:function(e,t){if(!(e instanceof be))throw{type:"Argument",message:"the first argument to unit must be a number".concat(e instanceof ke?". Have you forgotten parenthesis?":"")};return t=t?t instanceof ue?t.value:t.toCSS():"",new be(e.value,t)},"get-unit":function(e){return new se(e.unit)}},xt=function(e){var t=this;switch((e=Array.prototype.slice.call(e)).length){case 0:throw{type:"Argument",message:"one or more arguments required"}}return e=[new Pe(e[0].value,this.index,this.currentFileInfo).eval(this.context)].map((function(e){return e.toCSS(t.context)})).join(this.context.compress?",":", "),new Pe("style(".concat(e,")"))},St={style:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];try{return xt.call(this,e)}catch(e){}}},It=function(e){var t={functionRegistry:ne,functionCaller:Ae};return ne.addMultiple(et),ne.add("default",me.eval.bind(me)),ne.addMultiple(at),ne.addMultiple(ot),ne.addMultiple(function(e){var t=function(e,t){return new Oe(t,e.index,e.currentFileInfo).eval(e.context)};return{"data-uri":function(n,i){i||(i=n,n=null);var s=n&&n.value,a=i.value,o=this.currentFileInfo,l=o.rewriteUrls?o.currentDirectory:o.entryPath,u=a.indexOf("#"),c="";-1!==u&&(c=a.slice(u),a=a.slice(0,u));var h=_(this.context);h.rawBuffer=!0;var f=e.getFileManager(a,l,h,e,!0);if(!f)return t(this,i);var p=!1;if(n)p=/;base64$/.test(s);else{if("image/svg+xml"===(s=e.mimeLookup(a)))p=!1;else{var v=e.charsetLookup(s);p=["US-ASCII","UTF-8"].indexOf(v)<0}p&&(s+=";base64")}var d=f.loadFileSync(a,l,h,e);if(!d.contents)return r.warn("Skipped data-uri embedding of ".concat(a," because file not found")),t(this,i||n);var m=d.contents;if(p&&!e.encodeBase64)return t(this,i);m=p?e.encodeBase64(m):encodeURIComponent(m);var g="data:".concat(s,",").concat(m).concat(c);return new Oe(new Me('"'.concat(g,'"'),g,!1,this.index,this.currentFileInfo),this.index,this.currentFileInfo)}}}(e)),ne.addMultiple(ht),ne.addMultiple(pt),ne.addMultiple(mt),ne.addMultiple(gt),ne.addMultiple({"svg-gradient":function(e){var t,n,i,r,s,a,o,l,u="linear",h='x="0" y="0" width="1" height="1"',f={compress:!1},p=e.toCSS(f);function v(){throw{type:"Argument",message:"svg-gradient expects direction, start_color [start_position], [color position,]..., end_color [end_position] or direction, color list"}}switch(2==arguments.length?(arguments[1].value.length<2&&v(),t=arguments[1].value):arguments.length<3?v():t=Array.prototype.slice.call(arguments,1),p){case"to bottom":n='x1="0%" y1="0%" x2="0%" y2="100%"';break;case"to right":n='x1="0%" y1="0%" x2="100%" y2="0%"';break;case"to bottom right":n='x1="0%" y1="0%" x2="100%" y2="100%"';break;case"to top right":n='x1="0%" y1="100%" x2="100%" y2="0%"';break;case"ellipse":case"ellipse at center":u="radial",n='cx="50%" cy="50%" r="75%"',h='x="-50" y="-50" width="101" height="101"';break;default:throw{type:"Argument",message:"svg-gradient direction must be 'to bottom', 'to right', 'to bottom right', 'to top right' or 'ellipse at center'"}}for(i='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1 1"><'.concat(u,'Gradient id="g" ').concat(n,">"),r=0;r<t.length;r+=1)t[r]instanceof we?(s=t[r].value[0],a=t[r].value[1]):(s=t[r],a=void 0),s instanceof c&&((0===r||r+1===t.length)&&void 0===a||a instanceof be)||v(),o=a?a.toCSS(f):0===r?"0%":"100%",l=s.alpha,i+='<stop offset="'.concat(o,'" stop-color="').concat(s.toRGB(),'"').concat(l<1?' stop-opacity="'.concat(l,'"'):"","/>");return i+="</".concat(u,"Gradient><rect ").concat(h,' fill="url(#g)" /></svg>'),i=encodeURIComponent(i),i="data:image/svg+xml,".concat(i),new Oe(new Me("'".concat(i,"'"),i,!1,this.index,this.currentFileInfo),this.index,this.currentFileInfo)}}),ne.addMultiple(wt),ne.addMultiple(St),t};function Ct(e,t){var n,i=(t=t||{}).variables,r=new B.Eval(t);"object"!=typeof i||Array.isArray(i)||(i=Object.keys(i).map((function(e){var t=i[e];return t instanceof He.Value||(t instanceof He.Expression||(t=new He.Expression([t])),t=new He.Value([t])),new He.Declaration("@".concat(e),t,!1,null,0)})),r.frames=[new He.Ruleset(null,i)]);var s,a,o=[new ee.JoinSelectorVisitor,new ee.MarkVisibleSelectorsVisitor(!0),new ee.ExtendVisitor,new ee.ToCSSVisitor({compress:Boolean(t.compress)})],l=[];if(t.pluginManager){a=t.pluginManager.visitor();for(var u=0;u<2;u++)for(a.first();s=a.get();)s.isPreEvalVisitor?0!==u&&-1!==l.indexOf(s)||(l.push(s),s.run(e)):0!==u&&-1!==o.indexOf(s)||(s.isPreVisitor?o.unshift(s):o.push(s))}n=e.eval(r);for(var c=0;c<o.length;c++)o[c].run(n);if(t.pluginManager)for(a.first();s=a.get();)-1===o.indexOf(s)&&-1===l.indexOf(s)&&s.run(n);return n}var kt,At=function(){function e(e){this.less=e,this.visitors=[],this.preProcessors=[],this.postProcessors=[],this.installedPlugins=[],this.fileManagers=[],this.iterator=-1,this.pluginCache={},this.Loader=new e.PluginLoader(e)}return e.prototype.addPlugins=function(e){if(e)for(var t=0;t<e.length;t++)this.addPlugin(e[t])},e.prototype.addPlugin=function(e,t,n){this.installedPlugins.push(e),t&&(this.pluginCache[t]=e),e.install&&e.install(this.less,this,n||this.less.functions.functionRegistry)},e.prototype.get=function(e){return this.pluginCache[e]},e.prototype.addVisitor=function(e){this.visitors.push(e)},e.prototype.addPreProcessor=function(e,t){var n;for(n=0;n<this.preProcessors.length&&!(this.preProcessors[n].priority>=t);n++);this.preProcessors.splice(n,0,{preProcessor:e,priority:t})},e.prototype.addPostProcessor=function(e,t){var n;for(n=0;n<this.postProcessors.length&&!(this.postProcessors[n].priority>=t);n++);this.postProcessors.splice(n,0,{postProcessor:e,priority:t})},e.prototype.addFileManager=function(e){this.fileManagers.push(e)},e.prototype.getPreProcessors=function(){for(var e=[],t=0;t<this.preProcessors.length;t++)e.push(this.preProcessors[t].preProcessor);return e},e.prototype.getPostProcessors=function(){for(var e=[],t=0;t<this.postProcessors.length;t++)e.push(this.postProcessors[t].postProcessor);return e},e.prototype.getVisitors=function(){return this.visitors},e.prototype.visitor=function(){var e=this;return{first:function(){return e.iterator=-1,e.visitors[e.iterator]},get:function(){return e.iterator+=1,e.visitors[e.iterator]}}},e.prototype.getFileManagers=function(){return this.fileManagers},e}(),_t=function(e,t){return!t&&kt||(kt=new At(e)),kt};var Pt,Et,Rt=function(e){var t=e.match(/^v(\d{1,2})\.(\d{1,2})\.(\d{1,2})(?:-([0-9A-Za-z-.]+))?(?:\+([0-9A-Za-z-.]+))?$/);if(!t)throw new Error("Unable to parse: "+e);return{major:parseInt(t[1],10),minor:parseInt(t[2],10),patch:parseInt(t[3],10),pre:t[4]||"",build:t[5]||""}};function Mt(e,t){var n,i,a,o;a=function(e){return function(){function t(e,t){this.root=e,this.imports=t}return t.prototype.toCSS=function(t){var n,i,s={};try{n=Ct(this.root,t)}catch(e){throw new V(e,this.imports)}try{var a=Boolean(t.compress);a&&r.warn("The compress option has been deprecated. We recommend you use a dedicated css minifier, for instance see less-plugin-clean-css.");var o={compress:a,dumpLineNumbers:t.dumpLineNumbers,strictUnits:Boolean(t.strictUnits),numPrecision:8};t.sourceMap?(i=new e(t.sourceMap),s.css=i.toCSS(n,o,this.imports)):s.css=n.toCSS(o)}catch(e){throw new V(e,this.imports)}if(t.pluginManager)for(var l=t.pluginManager.getPostProcessors(),u=0;u<l.length;u++)s.css=l[u].process(s.css,{sourceMap:i,options:t,imports:this.imports});for(var c in t.sourceMap&&(s.map=i.getExternalSourceMap()),s.imports=[],this.imports.files)Object.prototype.hasOwnProperty.call(this.imports.files,c)&&c!==this.imports.rootFilename&&s.imports.push(c);return s},t}()}(i=function(e,t){return function(){function n(e){this.options=e}return n.prototype.toCSS=function(t,n,i){var r=new e({contentsIgnoredCharsMap:i.contentsIgnoredChars,rootNode:t,contentsMap:i.contents,sourceMapFilename:this.options.sourceMapFilename,sourceMapURL:this.options.sourceMapURL,outputFilename:this.options.sourceMapOutputFilename,sourceMapBasepath:this.options.sourceMapBasepath,sourceMapRootpath:this.options.sourceMapRootpath,outputSourceFiles:this.options.outputSourceFiles,sourceMapGenerator:this.options.sourceMapGenerator,sourceMapFileInline:this.options.sourceMapFileInline,disableSourcemapAnnotation:this.options.disableSourcemapAnnotation}),s=r.toCSS(n);return this.sourceMap=r.sourceMap,this.sourceMapURL=r.sourceMapURL,this.options.sourceMapInputFilename&&(this.sourceMapInputFilename=r.normalizeFilename(this.options.sourceMapInputFilename)),void 0!==this.options.sourceMapBasepath&&void 0!==this.sourceMapURL&&(this.sourceMapURL=r.removeBasepath(this.sourceMapURL)),s+this.getCSSAppendage()},n.prototype.getCSSAppendage=function(){var e=this.sourceMapURL;if(this.options.sourceMapFileInline){if(void 0===this.sourceMap)return"";e="data:application/json;base64,".concat(t.encodeBase64(this.sourceMap))}return this.options.disableSourcemapAnnotation?"":e?"/*# sourceMappingURL=".concat(e," */"):""},n.prototype.getExternalSourceMap=function(){return this.sourceMap},n.prototype.setExternalSourceMap=function(e){this.sourceMap=e},n.prototype.isInline=function(){return this.options.sourceMapFileInline},n.prototype.getSourceMapURL=function(){return this.sourceMapURL},n.prototype.getOutputFilename=function(){return this.options.sourceMapOutputFilename},n.prototype.getInputFilename=function(){return this.sourceMapInputFilename},n}()}(n=function(e){return function(){function t(t){this._css=[],this._rootNode=t.rootNode,this._contentsMap=t.contentsMap,this._contentsIgnoredCharsMap=t.contentsIgnoredCharsMap,t.sourceMapFilename&&(this._sourceMapFilename=t.sourceMapFilename.replace(/\\/g,"/")),this._outputFilename=t.outputFilename,this.sourceMapURL=t.sourceMapURL,t.sourceMapBasepath&&(this._sourceMapBasepath=t.sourceMapBasepath.replace(/\\/g,"/")),t.sourceMapRootpath?(this._sourceMapRootpath=t.sourceMapRootpath.replace(/\\/g,"/"),"/"!==this._sourceMapRootpath.charAt(this._sourceMapRootpath.length-1)&&(this._sourceMapRootpath+="/")):this._sourceMapRootpath="",this._outputSourceFiles=t.outputSourceFiles,this._sourceMapGeneratorConstructor=e.getSourceMapGenerator(),this._lineNumber=0,this._column=0}return t.prototype.removeBasepath=function(e){return this._sourceMapBasepath&&0===e.indexOf(this._sourceMapBasepath)&&("\\"!==(e=e.substring(this._sourceMapBasepath.length)).charAt(0)&&"/"!==e.charAt(0)||(e=e.substring(1))),e},t.prototype.normalizeFilename=function(e){return e=e.replace(/\\/g,"/"),e=this.removeBasepath(e),(this._sourceMapRootpath||"")+e},t.prototype.add=function(e,t,n,i){if(e){var r,s,a,o,l;if(t&&t.filename){var u=this._contentsMap[t.filename];if(this._contentsIgnoredCharsMap[t.filename]&&((n-=this._contentsIgnoredCharsMap[t.filename])<0&&(n=0),u=u.slice(this._contentsIgnoredCharsMap[t.filename])),void 0===u)return void this._css.push(e);o=(s=(u=u.substring(0,n)).split("\n"))[s.length-1]}if(a=(r=e.split("\n"))[r.length-1],t&&t.filename)if(i)for(l=0;l<r.length;l++)this._sourceMapGenerator.addMapping({generated:{line:this._lineNumber+l+1,column:0===l?this._column:0},original:{line:s.length+l,column:0===l?o.length:0},source:this.normalizeFilename(t.filename)});else this._sourceMapGenerator.addMapping({generated:{line:this._lineNumber+1,column:this._column},original:{line:s.length,column:o.length},source:this.normalizeFilename(t.filename)});1===r.length?this._column+=a.length:(this._lineNumber+=r.length-1,this._column=a.length),this._css.push(e)}},t.prototype.isEmpty=function(){return 0===this._css.length},t.prototype.toCSS=function(e){if(this._sourceMapGenerator=new this._sourceMapGeneratorConstructor({file:this._outputFilename,sourceRoot:null}),this._outputSourceFiles)for(var t in this._contentsMap)if(this._contentsMap.hasOwnProperty(t)){var n=this._contentsMap[t];this._contentsIgnoredCharsMap[t]&&(n=n.slice(this._contentsIgnoredCharsMap[t])),this._sourceMapGenerator.setSourceContent(this.normalizeFilename(t),n)}if(this._rootNode.genCSS(e,this),this._css.length>0){var i=void 0,r=JSON.stringify(this._sourceMapGenerator.toJSON());this.sourceMapURL?i=this.sourceMapURL:this._sourceMapFilename&&(i=this._sourceMapFilename),this.sourceMapURL=i,this.sourceMap=r}return this._css.join("")},t}()}(e=new s(e,t)),e)),o=function(e){return function(){function t(e,t,n){this.less=e,this.rootFilename=n.filename,this.paths=t.paths||[],this.contents={},this.contentsIgnoredChars={},this.mime=t.mime,this.error=null,this.context=t,this.queue=[],this.files={}}return t.prototype.push=function(t,n,i,s,a){var o=this,l=this.context.pluginManager.Loader;this.queue.push(t);var u=function(e,n,i){o.queue.splice(o.queue.indexOf(t),1);var l=i===o.rootFilename;s.optional&&e?(a(null,{rules:[]},!1,null),r.info("The file ".concat(i," was skipped because it was not found and the import was marked optional."))):(o.files[i]||s.inline||(o.files[i]={root:n,options:s}),e&&!o.error&&(o.error=e),a(e,n,l,i))},c={rewriteUrls:this.context.rewriteUrls,entryPath:i.entryPath,rootpath:i.rootpath,rootFilename:i.rootFilename},h=e.getFileManager(t,i.currentDirectory,this.context,e);if(h){var f,p,v=function(e){var t,n=e.filename,r=e.contents.replace(/^\uFEFF/,"");c.currentDirectory=h.getPath(n),c.rewriteUrls&&(c.rootpath=h.join(o.context.rootpath||"",h.pathDiff(c.currentDirectory,c.entryPath)),!h.isPathAbsolute(c.rootpath)&&h.alwaysMakePathsAbsolute()&&(c.rootpath=h.join(c.entryPath,c.rootpath))),c.filename=n;var a=new B.Parse(o.context);a.processImports=!1,o.contents[n]=r,(i.reference||s.reference)&&(c.reference=!0),s.isPlugin?(t=l.evalPlugin(r,a,o,s.pluginArgs,c))instanceof V?u(t,null,n):u(null,t,n):s.inline?u(null,r,n):!o.files[n]||o.files[n].options.multiple||s.multiple?new ae(a,o,c).parse(r,(function(e,t){u(e,t,n)})):u(null,o.files[n].root,n)},d=_(this.context);n&&(d.ext=s.isPlugin?".js":".less"),s.isPlugin?(d.mime="application/javascript",d.syncImport?f=l.loadPluginSync(t,i.currentDirectory,d,e,h):p=l.loadPlugin(t,i.currentDirectory,d,e,h)):d.syncImport?f=h.loadFileSync(t,i.currentDirectory,d,e):p=h.loadFile(t,i.currentDirectory,d,e,(function(e,t){e?u(e):v(t)})),f?f.filename?v(f):u(f):p&&p.then(v,u)}else u({message:"Could not find a file-manager for ".concat(t)})},t}()}(e);var u,c=function(e,t){var n=function(e,i,r){if("function"==typeof i?(r=i,i=E(this.options,{})):i=E(this.options,i||{}),!r){var s=this;return new Promise((function(t,r){n.call(s,e,i,(function(e,n){e?r(e):t(n)}))}))}this.parse(e,i,(function(e,n,i,s){if(e)return r(e);var a;try{a=new t(n,i).toCSS(s)}catch(e){return r(e)}r(null,a)}))};return n}(0,a),h=function(e,t,n){var i=function(e,t,r){if("function"==typeof t?(r=t,t=E(this.options,{})):t=E(this.options,t||{}),!r){var s=this;return new Promise((function(n,r){i.call(s,e,t,(function(e,t){e?r(e):n(t)}))}))}var a,o=void 0,l=new _t(this,!t.reUsePluginManager);if(t.pluginManager=l,a=new B.Parse(t),t.rootFileInfo)o=t.rootFileInfo;else{var u=t.filename||"input",c=u.replace(/[^/\\]*$/,"");(o={filename:u,rewriteUrls:a.rewriteUrls,rootpath:a.rootpath||"",currentDirectory:c,entryPath:c,rootFilename:u}).rootpath&&"/"!==o.rootpath.slice(-1)&&(o.rootpath+="/")}var h=new n(this,a,o);this.importManager=h,t.plugins&&t.plugins.forEach((function(e){var t,n;if(e.fileContent){if(n=e.fileContent.replace(/^\uFEFF/,""),(t=l.Loader.evalPlugin(n,a,h,e.options,e.filename))instanceof V)return r(t)}else l.addPlugin(e)})),new ae(a,h,o).parse(e,(function(e,n){if(e)return r(e);r(null,n,h,t)}),t)};return i}(0,0,o),f=Rt("v".concat("4.4.0")),p={version:[f.major,f.minor,f.patch],data:l,tree:He,Environment:s,AbstractFileManager:Ke,AbstractPluginLoader:Qe,environment:e,visitors:ee,Parser:ae,functions:It(e),contexts:B,SourceMapOutput:n,SourceMapBuilder:i,ParseTree:a,ImportManager:o,render:c,parse:h,LessError:V,transformTree:Ct,utils:O,PluginManager:_t,logger:r},v=function(e){return function(){var t=Object.create(e.prototype);return e.apply(t,Array.prototype.slice.call(arguments,0)),t}},d=Object.create(p);for(var m in p.tree)if("function"==typeof(u=p.tree[m]))d[m.toLowerCase()]=v(u);else for(var g in d[m]=Object.create(null),u)d[m][g.toLowerCase()]=v(u[g]);return p.parse=p.parse.bind(d),p.render=p.render.bind(d),d}var Ot={},$t=function(){};$t.prototype=Object.assign(new Ke,{alwaysMakePathsAbsolute:function(){return!0},join:function(e,t){return e?this.extractUrlParts(t,e).path:t},doXHR:function(e,t,n,i){var r=new XMLHttpRequest,s=!Pt.isFileProtocol||Pt.fileAsync;function a(t,n,i){t.status>=200&&t.status<300?n(t.responseText,t.getResponseHeader("Last-Modified")):"function"==typeof i&&i(t.status,e)}"function"==typeof r.overrideMimeType&&r.overrideMimeType("text/css"),Et.debug("XHR: Getting '".concat(e,"'")),r.open("GET",e,s),r.setRequestHeader("Accept",t||"text/x-less, text/css; q=0.9, */*; q=0.5"),r.send(null),Pt.isFileProtocol&&!Pt.fileAsync?0===r.status||r.status>=200&&r.status<300?n(r.responseText):i(r.status,e):s?r.onreadystatechange=function(){4==r.readyState&&a(r,n,i)}:a(r,n,i)},supports:function(){return!0},clearFileCache:function(){Ot={}},loadFile:function(e,t,n){t&&!this.isPathAbsolute(e)&&(e=t+e),e=n.ext?this.tryAppendExtension(e,n.ext):e,n=n||{};var i=this.extractUrlParts(e,window.location.href).url,r=this;return new Promise((function(e,t){if(n.useFileCache&&Ot[i])try{var s=Ot[i];return e({contents:s,filename:i,webInfo:{lastModified:new Date}})}catch(e){return t({filename:i,message:"Error loading file ".concat(i," error was ").concat(e.message)})}r.doXHR(i,n.mime,(function(t,n){Ot[i]=t,e({contents:t,filename:i,webInfo:{lastModified:n}})}),(function(e,n){t({type:"File",message:"'".concat(n,"' wasn't found (").concat(e,")"),href:i})}))}))}});var Vt=function(e,t){return Pt=e,Et=t,$t},Ft=function(e){this.less=e};Ft.prototype=Object.assign(new Qe,{loadPlugin:function(e,t,n,i,r){return new Promise((function(s,a){r.loadFile(e,t,n,i).then(s).catch(a)}))}});var Lt=function(t,i,r){return{add:function(s,a){r.errorReporting&&"html"!==r.errorReporting?"console"===r.errorReporting?function(e,t){var n=e.filename||t,s=[],a="".concat(e.type||"Syntax","Error: ").concat(e.message||"There is an error in your .less file"," in ").concat(n),o=function(e,t,n){void 0!==e.extract[t]&&s.push("{line} {content}".replace(/\{line\}/,(parseInt(e.line,10)||0)+(t-1)).replace(/\{class\}/,n).replace(/\{content\}/,e.extract[t]))};e.line&&(o(e,0,""),o(e,1,"line"),o(e,2,""),a+=" on line ".concat(e.line,", column ").concat(e.column+1,":\n").concat(s.join("\n"))),e.stack&&(e.extract||r.logLevel>=4)&&(a+="\nStack Trace\n".concat(e.stack)),i.logger.error(a)}(s,a):"function"==typeof r.errorReporting&&r.errorReporting("add",s,a):function(i,s){var a,o,l="less-error-message:".concat(e(s||"")),u=t.document.createElement("div"),c=[],h=i.filename||s,f=h.match(/([^/]+(\?.*)?)$/)[1];u.id=l,u.className="less-error-message",o="<h3>".concat(i.type||"Syntax","Error: ").concat(i.message||"There is an error in your .less file")+'</h3><p>in <a href="'.concat(h,'">').concat(f,"</a> ");var p=function(e,t,n){void 0!==e.extract[t]&&c.push('<li><label>{line}</label><pre class="{class}">{content}</pre></li>'.replace(/\{line\}/,(parseInt(e.line,10)||0)+(t-1)).replace(/\{class\}/,n).replace(/\{content\}/,e.extract[t]))};i.line&&(p(i,0,""),p(i,1,"line"),p(i,2,""),o+="on line ".concat(i.line,", column ").concat(i.column+1,":</p><ul>").concat(c.join(""),"</ul>")),i.stack&&(i.extract||r.logLevel>=4)&&(o+="<br/>Stack Trace</br />".concat(i.stack.split("\n").slice(1).join("<br/>"))),u.innerHTML=o,n(t.document,[".less-error-message ul, .less-error-message li {","list-style-type: none;","margin-right: 15px;","padding: 4px 0;","margin: 0;","}",".less-error-message label {","font-size: 12px;","margin-right: 15px;","padding: 4px 0;","color: #cc7777;","}",".less-error-message pre {","color: #dd6666;","padding: 4px 0;","margin: 0;","display: inline-block;","}",".less-error-message pre.line {","color: #ff0000;","}",".less-error-message h3 {","font-size: 20px;","font-weight: bold;","padding: 15px 0 5px 0;","margin: 0;","}",".less-error-message a {","color: #10a","}",".less-error-message .error {","color: red;","font-weight: bold;","padding-bottom: 2px;","border-bottom: 1px dashed red;","}"].join("\n"),{title:"error-message"}),u.style.cssText=["font-family: Arial, sans-serif","border: 1px solid #e00","background-color: #eee","border-radius: 5px","-webkit-border-radius: 5px","-moz-border-radius: 5px","color: #e00","padding: 15px","margin-bottom: 15px"].join(";"),"development"===r.env&&(a=setInterval((function(){var e=t.document,n=e.body;n&&(e.getElementById(l)?n.replaceChild(u,e.getElementById(l)):n.insertBefore(u,n.firstChild),clearInterval(a))}),10))}(s,a)},remove:function(n){r.errorReporting&&"html"!==r.errorReporting?"console"===r.errorReporting||"function"==typeof r.errorReporting&&r.errorReporting("remove",n):function(n){var i=t.document.getElementById("less-error-message:".concat(e(n)));i&&i.parentNode.removeChild(i)}(n)}}},jt={javascriptEnabled:!1,depends:!1,compress:!1,lint:!1,paths:[],color:!0,strictImports:!1,insecure:!1,rootpath:"",rewriteUrls:!1,math:1,strictUnits:!1,globalVars:null,modifyVars:null,urlArgs:""};if(window.less)for(var Dt in window.less)Object.prototype.hasOwnProperty.call(window.less,Dt)&&(jt[Dt]=window.less[Dt]);!function(e,n){t(n,i(e)),void 0===n.isFileProtocol&&(n.isFileProtocol=/^(file|(chrome|safari)(-extension)?|resource|qrc|app):/.test(e.location.protocol)),n.async=n.async||!1,n.fileAsync=n.fileAsync||!1,n.poll=n.poll||(n.isFileProtocol?1e3:1500),n.env=n.env||("127.0.0.1"==e.location.hostname||"0.0.0.0"==e.location.hostname||"localhost"==e.location.hostname||e.location.port&&e.location.port.length>0||n.isFileProtocol?"development":"production");var r=/!dumpLineNumbers:(comments|mediaquery|all)/.exec(e.location.hash);r&&(n.dumpLineNumbers=r[1]),void 0===n.useFileCache&&(n.useFileCache=!0),void 0===n.onReady&&(n.onReady=!0),n.relativeUrls&&(n.rewriteUrls="all")}(window,jt),jt.plugins=jt.plugins||[],window.LESS_PLUGINS&&(jt.plugins=jt.plugins.concat(window.LESS_PLUGINS));var Nt,Bt,Ut,qt=function(e,i){var r=e.document,s=Mt();s.options=i;var a=s.environment,o=Vt(i,s.logger),l=new o;a.addFileManager(l),s.FileManager=o,s.PluginLoader=Ft,function(e,t){t.logLevel=void 0!==t.logLevel?t.logLevel:"development"===t.env?3:1,t.loggers||(t.loggers=[{debug:function(e){t.logLevel>=4&&console.log(e)},info:function(e){t.logLevel>=3&&console.log(e)},warn:function(e){t.logLevel>=2&&console.warn(e)},error:function(e){t.logLevel>=1&&console.error(e)}}]);for(var n=0;n<t.loggers.length;n++)e.logger.addListener(t.loggers[n])}(s,i);var u=Lt(e,s,i),c=s.cache=i.cache||function(e,t,n){var i=null;if("development"!==t.env)try{i=void 0===e.localStorage?null:e.localStorage}catch(e){}return{setCSS:function(e,t,r,s){if(i){n.info("saving ".concat(e," to cache."));try{i.setItem(e,s),i.setItem("".concat(e,":timestamp"),t),r&&i.setItem("".concat(e,":vars"),JSON.stringify(r))}catch(t){n.error('failed to save "'.concat(e,'" to local storage for caching.'))}}},getCSS:function(e,t,n){var r=i&&i.getItem(e),s=i&&i.getItem("".concat(e,":timestamp")),a=i&&i.getItem("".concat(e,":vars"));if(n=n||{},a=a||"{}",s&&t.lastModified&&new Date(t.lastModified).valueOf()===new Date(s).valueOf()&&JSON.stringify(n)===a)return r}}}(e,i,s.logger);!function(){function e(){throw{type:"Runtime",message:"Image size functions are not supported in browser version of less"}}var t={"image-size":function(t){return e(),-1},"image-width":function(t){return e(),-1},"image-height":function(t){return e(),-1}};ne.addMultiple(t)}(s.environment),i.functions&&s.functions.functionRegistry.addMultiple(i.functions);var h=/^text\/(x-)?less$/;function f(e){var t={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t}function p(e,t){var n=Array.prototype.slice.call(arguments,2);return function(){var i=n.concat(Array.prototype.slice.call(arguments,0));return e.apply(t,i)}}function v(e){for(var t,n=r.getElementsByTagName("style"),a=0;a<n.length;a++)if((t=n[a]).type.match(h)){var o=f(i);o.modifyVars=e;var l=t.innerHTML||"";o.filename=r.location.href.replace(/#.*$/,""),s.render(l,o,p((function(e,t,n){t?u.add(t,"inline"):(e.type="text/css",e.styleSheet?e.styleSheet.cssText=n.css:e.innerHTML=n.css)}),null,t))}}function d(e,n,r,o,h){var p=f(i);t(p,e),p.mime=e.type,h&&(p.modifyVars=h),l.loadFile(e.href,null,p,a).then((function(t){!function(t){var i=t.contents,a=t.filename,h=t.webInfo,f={currentDirectory:l.getPath(a),filename:a,rootFilename:a,rewriteUrls:p.rewriteUrls};if(f.entryPath=f.currentDirectory,f.rootpath=p.rootpath||f.currentDirectory,h){h.remaining=o;var v=c.getCSS(a,h,p.modifyVars);if(!r&&v)return h.local=!0,void n(null,v,i,e,h,a)}u.remove(a),p.rootFileInfo=f,s.render(i,p,(function(t,r){t?(t.href=a,n(t)):(c.setCSS(e.href,h.lastModified,p.modifyVars,r.css),n(null,r.css,i,e,h,a))}))}(t)})).catch((function(e){console.log(e),n(e)}))}function m(e,t,n){for(var i=0;i<s.sheets.length;i++)d(s.sheets[i],e,t,s.sheets.length-(i+1),n)}return s.watch=function(){return s.watchMode||(s.env="development","development"===s.env&&(s.watchTimer=setInterval((function(){s.watchMode&&(l.clearFileCache(),m((function(t,i,r,s,a){t?u.add(t,t.href||s.href):i&&n(e.document,i,s)})))}),i.poll))),this.watchMode=!0,!0},s.unwatch=function(){return clearInterval(s.watchTimer),this.watchMode=!1,!1},s.registerStylesheetsImmediately=function(){var e=r.getElementsByTagName("link");s.sheets=[];for(var t=0;t<e.length;t++)("stylesheet/less"===e[t].rel||e[t].rel.match(/stylesheet/)&&e[t].type.match(h))&&s.sheets.push(e[t])},s.registerStylesheets=function(){return new Promise((function(e){s.registerStylesheetsImmediately(),e()}))},s.modifyVars=function(e){return s.refresh(!0,e,!1)},s.refresh=function(t,i,r){return(t||r)&&!1!==r&&l.clearFileCache(),new Promise((function(r,a){var o,l,c,h;o=l=new Date,0===(h=s.sheets.length)?(l=new Date,c=l-o,s.logger.info("Less has finished and no sheets were loaded."),r({startTime:o,endTime:l,totalMilliseconds:c,sheets:s.sheets.length})):m((function(t,i,f,p,v){if(t)return u.add(t,t.href||p.href),void a(t);v.local?s.logger.info("Loading ".concat(p.href," from cache.")):s.logger.info("Rendered ".concat(p.href," successfully.")),n(e.document,i,p),s.logger.info("CSS for ".concat(p.href," generated in ").concat(new Date-l,"ms")),0===--h&&(c=new Date-o,s.logger.info("Less has finished. CSS generated in ".concat(c,"ms")),r({startTime:o,endTime:l,totalMilliseconds:c,sheets:s.sheets.length})),l=new Date}),t,i),v(i)}))},s.refreshStyles=v,s}(window,jt);function Tt(e){e.filename&&console.warn(e),jt.async||Bt.removeChild(Ut)}return window.less=qt,jt.onReady&&(/!watch/.test(window.location.hash)&&qt.watch(),jt.async||(Nt="body { display: none !important }",Bt=document.head||document.getElementsByTagName("head")[0],(Ut=document.createElement("style")).type="text/css",Ut.styleSheet?Ut.styleSheet.cssText=Nt:Ut.appendChild(document.createTextNode(Nt)),Bt.appendChild(Ut)),qt.registerStylesheetsImmediately(),qt.pageLoadFinished=qt.refresh("development"===qt.env).then(Tt,Tt)),qt}));
//# sourceMappingURL=less.min.js.map
