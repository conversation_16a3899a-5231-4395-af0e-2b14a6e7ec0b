{"version": 3, "file": "image-size.js", "sourceRoot": "", "sources": ["../../src/less-browser/image-size.js"], "names": [], "mappings": ";;;AACA,oGAAqE;AAErE,mBAAe;IACX,SAAS,SAAS;QACd,MAAM;YACF,IAAI,EAAE,SAAS;YACf,OAAO,EAAE,mEAAmE;SAC/E,CAAC;IACN,CAAC;IAED,IAAM,cAAc,GAAG;QACnB,YAAY,EAAE,UAAS,YAAY;YAC/B,SAAS,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;YAC9B,OAAO,CAAC,CAAC,CAAC;QACd,CAAC;QACD,aAAa,EAAE,UAAS,YAAY;YAChC,SAAS,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;YAC9B,OAAO,CAAC,CAAC,CAAC;QACd,CAAC;QACD,cAAc,EAAE,UAAS,YAAY;YACjC,SAAS,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;YAC9B,OAAO,CAAC,CAAC,CAAC;QACd,CAAC;KACJ,CAAC;IAEF,2BAAgB,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;AACjD,CAAC,EAAC", "sourcesContent": ["\nimport functionRegistry from './../less/functions/function-registry';\n\nexport default () => {\n    function imageSize() {\n        throw {\n            type: 'Runtime',\n            message: 'Image size functions are not supported in browser version of less'\n        };\n    }\n\n    const imageFunctions = {\n        'image-size': function(filePathNode) {\n            imageSize(this, filePathNode);\n            return -1;\n        },\n        'image-width': function(filePathNode) {\n            imageSize(this, filePathNode);\n            return -1;\n        },\n        'image-height': function(filePathNode) {\n            imageSize(this, filePathNode);\n            return -1;\n        }\n    };\n\n    functionRegistry.addMultiple(imageFunctions);\n};\n"]}