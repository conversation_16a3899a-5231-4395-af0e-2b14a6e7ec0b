{"version": 3, "file": "bootstrap.js", "sourceRoot": "", "sources": ["../../src/less-browser/bootstrap.js"], "names": [], "mappings": ";;;AAAA;;;;GAIG;AACH,oFAAqD;AACrD,sFAAsD;AACtD,0DAA2B;AAE3B,IAAM,OAAO,GAAG,IAAA,yBAAc,GAAE,CAAC;AAEjC,IAAI,MAAM,CAAC,IAAI,EAAE;IACb,KAAK,IAAM,GAAG,IAAI,MAAM,CAAC,IAAI,EAAE;QAC3B,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE;YACxD,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SACnC;KACJ;CACJ;AACD,IAAA,6BAAiB,EAAC,MAAM,EAAE,OAAO,CAAC,CAAC;AAEnC,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,EAAE,CAAC;AAExC,IAAI,MAAM,CAAC,YAAY,EAAE;IACrB,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;CACjE;AAED,IAAM,IAAI,GAAG,IAAA,eAAI,EAAC,MAAM,EAAE,OAAO,CAAC,CAAC;AACnC,kBAAe,IAAI,CAAC;AAEpB,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;AAEnB,IAAI,GAAG,CAAC;AACR,IAAI,IAAI,CAAC;AACT,IAAI,KAAK,CAAC;AAEV,iCAAiC;AACjC,SAAS,eAAe,CAAC,IAAI;IACzB,IAAI,IAAI,CAAC,QAAQ,EAAE;QACf,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KACtB;IACD,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;QAChB,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;KAC3B;AACL,CAAC;AAED,IAAI,OAAO,CAAC,OAAO,EAAE;IACjB,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;QACrC,IAAI,CAAC,KAAK,EAAE,CAAC;KAChB;IACD,mEAAmE;IACnE,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;QAChB,GAAG,GAAG,mCAAmC,CAAC;QAC1C,IAAI,GAAG,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACjE,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAExC,KAAK,CAAC,IAAI,GAAG,UAAU,CAAC;QACxB,IAAI,KAAK,CAAC,UAAU,EAAE;YAClB,KAAK,CAAC,UAAU,CAAC,OAAO,GAAG,GAAG,CAAC;SAClC;aAAM;YACH,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC;SACnD;QAED,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;KAC3B;IACD,IAAI,CAAC,8BAA8B,EAAE,CAAC;IACtC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK,aAAa,CAAC,CAAC,IAAI,CAAC,eAAe,EAAE,eAAe,CAAC,CAAC;CAC3G", "sourcesContent": ["/**\n * Kicks off less and compiles any stylesheets\n * used in the browser distributed version of less\n * to kick-start less using the browser api\n */\nimport defaultOptions from '../less/default-options';\nimport addDefaultOptions from './add-default-options';\nimport root from './index';\n\nconst options = defaultOptions();\n\nif (window.less) {\n    for (const key in window.less) {\n        if (Object.prototype.hasOwnProperty.call(window.less, key)) {\n            options[key] = window.less[key];\n        }\n    }\n}\naddDefaultOptions(window, options);\n\noptions.plugins = options.plugins || [];\n\nif (window.LESS_PLUGINS) {\n    options.plugins = options.plugins.concat(window.LESS_PLUGINS);\n}\n\nconst less = root(window, options);\nexport default less;\n\nwindow.less = less;\n\nlet css;\nlet head;\nlet style;\n\n// Always restore page visibility\nfunction resolveOrReject(data) {\n    if (data.filename) {\n        console.warn(data);\n    }\n    if (!options.async) {\n        head.removeChild(style);\n    }\n}\n\nif (options.onReady) {\n    if (/!watch/.test(window.location.hash)) {\n        less.watch();\n    }\n    // Simulate synchronous stylesheet loading by hiding page rendering\n    if (!options.async) {\n        css = 'body { display: none !important }';\n        head = document.head || document.getElementsByTagName('head')[0];\n        style = document.createElement('style');\n\n        style.type = 'text/css';\n        if (style.styleSheet) {\n            style.styleSheet.cssText = css;\n        } else {\n            style.appendChild(document.createTextNode(css));\n        }\n\n        head.appendChild(style);\n    }\n    less.registerStylesheetsImmediately();\n    less.pageLoadFinished = less.refresh(less.env === 'development').then(resolveOrReject, resolveOrReject);\n}\n"]}