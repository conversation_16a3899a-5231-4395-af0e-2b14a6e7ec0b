{"version": 3, "file": "import-manager.js", "sourceRoot": "", "sources": ["../../src/less/import-manager.js"], "names": [], "mappings": ";;;AAAA,gEAAkC;AAClC,mEAAqC;AACrC,oEAAqC;AACrC,qDAAiC;AACjC,4DAA8B;AAE9B,mBAAwB,WAAW;IAC/B,eAAe;IACf,mEAAmE;IACnE,uDAAuD;IACvD,4DAA4D;IAC5D,2DAA2D;IAC3D,8CAA8C;IAC9C,iDAAiD;IACjD,iGAAiG;IAEjG;QACI,uBAAY,IAAI,EAAE,OAAO,EAAE,YAAY;YACnC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;YACjB,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC,QAAQ,CAAC;YAC1C,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,EAAE,CAAC,CAAE,+BAA+B;YAClE,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC,CAAa,8CAA8C;YAC9E,IAAI,CAAC,oBAAoB,GAAG,EAAE,CAAC,CAAC,kEAAkE;YAClG,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;YACzB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;YAClB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;YACvB,uDAAuD;YACvD,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,CAAQ,wCAAwC;YAChE,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,CAAQ,kCAAkC;QAC9D,CAAC;QAED;;;;;;;WAOG;QACH,4BAAI,GAAJ,UAAK,IAAI,EAAE,kBAAkB,EAAE,eAAe,EAAE,aAAa,EAAE,QAAQ;YACnE,IAAM,aAAa,GAAG,IAAI,EAAE,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC;YAE7E,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEtB,IAAM,cAAc,GAAG,UAAU,CAAC,EAAE,IAAI,EAAE,QAAQ;gBAC9C,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,iCAAiC;gBAEnG,IAAM,kBAAkB,GAAG,QAAQ,KAAK,aAAa,CAAC,YAAY,CAAC;gBACnE,IAAI,aAAa,CAAC,QAAQ,IAAI,CAAC,EAAE;oBAC7B,QAAQ,CAAC,IAAI,EAAE,EAAC,KAAK,EAAC,EAAE,EAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;oBACxC,gBAAM,CAAC,IAAI,CAAC,mBAAY,QAAQ,8EAA2E,CAAC,CAAC;iBAChH;qBACI;oBACD,qCAAqC;oBACrC,iGAAiG;oBACjG,4FAA4F;oBAC5F,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE;wBACzD,aAAa,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,MAAA,EAAE,OAAO,EAAE,aAAa,EAAE,CAAC;qBACpE;oBACD,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE;wBAAE,aAAa,CAAC,KAAK,GAAG,CAAC,CAAC;qBAAE;oBAC3D,QAAQ,CAAC,CAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,QAAQ,CAAC,CAAC;iBACnD;YACL,CAAC,CAAC;YAEF,IAAM,WAAW,GAAG;gBAChB,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW;gBACrC,SAAS,EAAE,eAAe,CAAC,SAAS;gBACpC,QAAQ,EAAE,eAAe,CAAC,QAAQ;gBAClC,YAAY,EAAE,eAAe,CAAC,YAAY;aAC7C,CAAC;YAEF,IAAM,WAAW,GAAG,WAAW,CAAC,cAAc,CAAC,IAAI,EAAE,eAAe,CAAC,gBAAgB,EAAE,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;YAElH,IAAI,CAAC,WAAW,EAAE;gBACd,cAAc,CAAC,EAAE,OAAO,EAAE,4CAAqC,IAAI,CAAE,EAAE,CAAC,CAAC;gBACzE,OAAO;aACV;YAED,IAAM,gBAAgB,GAAG,UAAS,UAAU;gBACxC,IAAI,MAAM,CAAC;gBACX,IAAM,gBAAgB,GAAG,UAAU,CAAC,QAAQ,CAAC;gBAC7C,IAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;gBAE5D,4EAA4E;gBAC5E,8BAA8B;gBAC9B,EAAE;gBACF,YAAY;gBACZ,+EAA+E;gBAC/E,mDAAmD;gBACnD,0EAA0E;gBAC1E,2CAA2C;gBAC3C,WAAW,CAAC,gBAAgB,GAAG,WAAW,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;gBACrE,IAAI,WAAW,CAAC,WAAW,EAAE;oBACzB,WAAW,CAAC,QAAQ,GAAG,WAAW,CAAC,IAAI,CACnC,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,IAAI,EAAE,CAAC,EACtC,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,gBAAgB,EAAE,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC;oBAE/E,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,WAAW,CAAC,uBAAuB,EAAE,EAAE;wBAC5F,WAAW,CAAC,QAAQ,GAAG,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC;qBACxF;iBACJ;gBACD,WAAW,CAAC,QAAQ,GAAG,gBAAgB,CAAC;gBAExC,IAAM,MAAM,GAAG,IAAI,kBAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;gBAEzD,MAAM,CAAC,cAAc,GAAG,KAAK,CAAC;gBAC9B,aAAa,CAAC,QAAQ,CAAC,gBAAgB,CAAC,GAAG,QAAQ,CAAC;gBAEpD,IAAI,eAAe,CAAC,SAAS,IAAI,aAAa,CAAC,SAAS,EAAE;oBACtD,WAAW,CAAC,SAAS,GAAG,IAAI,CAAC;iBAChC;gBAED,IAAI,aAAa,CAAC,QAAQ,EAAE;oBACxB,MAAM,GAAG,YAAY,CAAC,UAAU,CAAC,QAAQ,EAAE,MAAM,EAAE,aAAa,EAAE,aAAa,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;oBACzG,IAAI,MAAM,YAAY,oBAAS,EAAE;wBAC7B,cAAc,CAAC,MAAM,EAAE,IAAI,EAAE,gBAAgB,CAAC,CAAC;qBAClD;yBACI;wBACD,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE,gBAAgB,CAAC,CAAC;qBAClD;iBACJ;qBAAM,IAAI,aAAa,CAAC,MAAM,EAAE;oBAC7B,cAAc,CAAC,IAAI,EAAE,QAAQ,EAAE,gBAAgB,CAAC,CAAC;iBACpD;qBAAM;oBACH,4EAA4E;oBAC5E,gCAAgC;oBAChC,IAAI,aAAa,CAAC,KAAK,CAAC,gBAAgB,CAAC;2BAClC,CAAC,aAAa,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,QAAQ;2BACvD,CAAC,aAAa,CAAC,QAAQ,EAAE;wBAE5B,cAAc,CAAC,IAAI,EAAE,aAAa,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;qBACtF;yBACI;wBACD,IAAI,gBAAM,CAAC,MAAM,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC,KAAK,CAAC,QAAQ,EAAE,UAAU,CAAC,EAAE,IAAI;4BAC5E,cAAc,CAAC,CAAC,EAAE,IAAI,EAAE,gBAAgB,CAAC,CAAC;wBAC9C,CAAC,CAAC,CAAC;qBACN;iBACJ;YACL,CAAC,CAAC;YACF,IAAI,UAAU,CAAC;YACf,IAAI,OAAO,CAAC;YACZ,IAAM,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAE1C,IAAI,kBAAkB,EAAE;gBACpB,OAAO,CAAC,GAAG,GAAG,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC;aAC1D;YAED,IAAI,aAAa,CAAC,QAAQ,EAAE;gBACxB,OAAO,CAAC,IAAI,GAAG,wBAAwB,CAAC;gBAExC,IAAI,OAAO,CAAC,UAAU,EAAE;oBACpB,UAAU,GAAG,YAAY,CAAC,cAAc,CAAC,IAAI,EAAE,eAAe,CAAC,gBAAgB,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;iBACvH;qBAAM;oBACH,OAAO,GAAG,YAAY,CAAC,UAAU,CAAC,IAAI,EAAE,eAAe,CAAC,gBAAgB,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;iBAChH;aACJ;iBACI;gBACD,IAAI,OAAO,CAAC,UAAU,EAAE;oBACpB,UAAU,GAAG,WAAW,CAAC,YAAY,CAAC,IAAI,EAAE,eAAe,CAAC,gBAAgB,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;iBACvG;qBAAM;oBACH,OAAO,GAAG,WAAW,CAAC,QAAQ,CAAC,IAAI,EAAE,eAAe,CAAC,gBAAgB,EAAE,OAAO,EAAE,WAAW,EACvF,UAAC,GAAG,EAAE,UAAU;wBACZ,IAAI,GAAG,EAAE;4BACL,cAAc,CAAC,GAAG,CAAC,CAAC;yBACvB;6BAAM;4BACH,gBAAgB,CAAC,UAAU,CAAC,CAAC;yBAChC;oBACL,CAAC,CAAC,CAAC;iBACV;aACJ;YACD,IAAI,UAAU,EAAE;gBACZ,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;oBACtB,cAAc,CAAC,UAAU,CAAC,CAAC;iBAC9B;qBAAM;oBACH,gBAAgB,CAAC,UAAU,CAAC,CAAC;iBAChC;aACJ;iBAAM,IAAI,OAAO,EAAE;gBAChB,OAAO,CAAC,IAAI,CAAC,gBAAgB,EAAE,cAAc,CAAC,CAAC;aAClD;QACL,CAAC;QACL,oBAAC;IAAD,CAAC,AAnKD,IAmKC;IAED,OAAO,aAAa,CAAC;AACzB,CAAC;AAhLD,4BAgLC", "sourcesContent": ["import contexts from './contexts';\nimport Parser from './parser/parser';\nimport LessError from './less-error';\nimport * as utils from './utils';\nimport logger from './logger';\n\nexport default function(environment) {\n    // FileInfo = {\n    //  'rewriteUrls' - option - whether to adjust URL's to be relative\n    //  'filename' - full resolved filename of current file\n    //  'rootpath' - path to append to normal URLs for this node\n    //  'currentDirectory' - path to the current file, absolute\n    //  'rootFilename' - filename of the base file\n    //  'entryPath' - absolute path to the entry file\n    //  'reference' - whether the file should not be output and only output parts that are referenced\n\n    class ImportManager {\n        constructor(less, context, rootFileInfo) {\n            this.less = less;\n            this.rootFilename = rootFileInfo.filename;\n            this.paths = context.paths || [];  // Search paths, when importing\n            this.contents = {};             // map - filename to contents of all the files\n            this.contentsIgnoredChars = {}; // map - filename to lines at the beginning of each file to ignore\n            this.mime = context.mime;\n            this.error = null;\n            this.context = context;\n            // Deprecated? Unused outside of here, could be useful.\n            this.queue = [];        // Files which haven't been imported yet\n            this.files = {};        // Holds the imported parse trees.\n        }\n\n        /**\n         * Add an import to be imported\n         * @param path - the raw path\n         * @param tryAppendExtension - whether to try appending a file extension (.less or .js if the path has no extension)\n         * @param currentFileInfo - the current file info (used for instance to work out relative paths)\n         * @param importOptions - import options\n         * @param callback - callback for when it is imported\n         */\n        push(path, tryAppendExtension, currentFileInfo, importOptions, callback) {\n            const importManager = this, pluginLoader = this.context.pluginManager.Loader;\n\n            this.queue.push(path);\n\n            const fileParsedFunc = function (e, root, fullPath) {\n                importManager.queue.splice(importManager.queue.indexOf(path), 1); // Remove the path from the queue\n\n                const importedEqualsRoot = fullPath === importManager.rootFilename;\n                if (importOptions.optional && e) {\n                    callback(null, {rules:[]}, false, null);\n                    logger.info(`The file ${fullPath} was skipped because it was not found and the import was marked optional.`);\n                }\n                else {\n                    // Inline imports aren't cached here.\n                    // If we start to cache them, please make sure they won't conflict with non-inline imports of the\n                    // same name as they used to do before this comment and the condition below have been added.\n                    if (!importManager.files[fullPath] && !importOptions.inline) {\n                        importManager.files[fullPath] = { root, options: importOptions };\n                    }\n                    if (e && !importManager.error) { importManager.error = e; }\n                    callback(e, root, importedEqualsRoot, fullPath);\n                }\n            };\n\n            const newFileInfo = {\n                rewriteUrls: this.context.rewriteUrls,\n                entryPath: currentFileInfo.entryPath,\n                rootpath: currentFileInfo.rootpath,\n                rootFilename: currentFileInfo.rootFilename\n            };\n\n            const fileManager = environment.getFileManager(path, currentFileInfo.currentDirectory, this.context, environment);\n\n            if (!fileManager) {\n                fileParsedFunc({ message: `Could not find a file-manager for ${path}` });\n                return;\n            }\n\n            const loadFileCallback = function(loadedFile) {\n                let plugin;\n                const resolvedFilename = loadedFile.filename;\n                const contents = loadedFile.contents.replace(/^\\uFEFF/, '');\n\n                // Pass on an updated rootpath if path of imported file is relative and file\n                // is in a (sub|sup) directory\n                //\n                // Examples:\n                // - If path of imported file is 'module/nav/nav.less' and rootpath is 'less/',\n                //   then rootpath should become 'less/module/nav/'\n                // - If path of imported file is '../mixins.less' and rootpath is 'less/',\n                //   then rootpath should become 'less/../'\n                newFileInfo.currentDirectory = fileManager.getPath(resolvedFilename);\n                if (newFileInfo.rewriteUrls) {\n                    newFileInfo.rootpath = fileManager.join(\n                        (importManager.context.rootpath || ''),\n                        fileManager.pathDiff(newFileInfo.currentDirectory, newFileInfo.entryPath));\n\n                    if (!fileManager.isPathAbsolute(newFileInfo.rootpath) && fileManager.alwaysMakePathsAbsolute()) {\n                        newFileInfo.rootpath = fileManager.join(newFileInfo.entryPath, newFileInfo.rootpath);\n                    }\n                }\n                newFileInfo.filename = resolvedFilename;\n\n                const newEnv = new contexts.Parse(importManager.context);\n\n                newEnv.processImports = false;\n                importManager.contents[resolvedFilename] = contents;\n\n                if (currentFileInfo.reference || importOptions.reference) {\n                    newFileInfo.reference = true;\n                }\n\n                if (importOptions.isPlugin) {\n                    plugin = pluginLoader.evalPlugin(contents, newEnv, importManager, importOptions.pluginArgs, newFileInfo);\n                    if (plugin instanceof LessError) {\n                        fileParsedFunc(plugin, null, resolvedFilename);\n                    }\n                    else {\n                        fileParsedFunc(null, plugin, resolvedFilename);\n                    }\n                } else if (importOptions.inline) {\n                    fileParsedFunc(null, contents, resolvedFilename);\n                } else {\n                    // import (multiple) parse trees apparently get altered and can't be cached.\n                    // TODO: investigate why this is\n                    if (importManager.files[resolvedFilename]\n                        && !importManager.files[resolvedFilename].options.multiple\n                        && !importOptions.multiple) {\n\n                        fileParsedFunc(null, importManager.files[resolvedFilename].root, resolvedFilename);\n                    }\n                    else {\n                        new Parser(newEnv, importManager, newFileInfo).parse(contents, function (e, root) {\n                            fileParsedFunc(e, root, resolvedFilename);\n                        });\n                    }\n                }\n            };\n            let loadedFile;\n            let promise;\n            const context = utils.clone(this.context);\n\n            if (tryAppendExtension) {\n                context.ext = importOptions.isPlugin ? '.js' : '.less';\n            }\n\n            if (importOptions.isPlugin) {\n                context.mime = 'application/javascript';\n\n                if (context.syncImport) {\n                    loadedFile = pluginLoader.loadPluginSync(path, currentFileInfo.currentDirectory, context, environment, fileManager);\n                } else {\n                    promise = pluginLoader.loadPlugin(path, currentFileInfo.currentDirectory, context, environment, fileManager);\n                }\n            }\n            else {\n                if (context.syncImport) {\n                    loadedFile = fileManager.loadFileSync(path, currentFileInfo.currentDirectory, context, environment);\n                } else {\n                    promise = fileManager.loadFile(path, currentFileInfo.currentDirectory, context, environment,\n                        (err, loadedFile) => {\n                            if (err) {\n                                fileParsedFunc(err);\n                            } else {\n                                loadFileCallback(loadedFile);\n                            }\n                        });\n                }\n            }\n            if (loadedFile) {\n                if (!loadedFile.filename) {\n                    fileParsedFunc(loadedFile);\n                } else {\n                    loadFileCallback(loadedFile);\n                }\n            } else if (promise) {\n                promise.then(loadFileCallback, fileParsedFunc);\n            }\n        }\n    }\n\n    return ImportManager;\n}\n"]}