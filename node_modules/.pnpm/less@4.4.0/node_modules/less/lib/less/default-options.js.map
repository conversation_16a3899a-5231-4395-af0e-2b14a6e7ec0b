{"version": 3, "file": "default-options.js", "sourceRoot": "", "sources": ["../../src/less/default-options.js"], "names": [], "mappings": ";;AAAA,iCAAiC;AACjC;IACI,OAAO;QACH,+CAA+C;QAC/C,iBAAiB,EAAE,KAAK;QAExB,0DAA0D;QAC1D,OAAO,EAAE,KAAK;QAEd;;wCAEgC;QAChC,QAAQ,EAAE,KAAK;QAEf,sEAAsE;QACtE,IAAI,EAAE,KAAK;QAEX;;;;gFAIwE;QACxE,KAAK,EAAE,EAAE;QAET,kCAAkC;QAClC,KAAK,EAAE,IAAI;QAEX;;6DAEqD;QACrD,aAAa,EAAE,KAAK;QAEpB,6CAA6C;QAC7C,QAAQ,EAAE,KAAK;QAEf;;8CAEsC;QACtC,QAAQ,EAAE,EAAE;QAEZ;;;8DAGsD;QACtD,WAAW,EAAE,KAAK;QAElB;;;;;WAKG;QACH,IAAI,EAAE,CAAC;QAEP,wFAAwF;QACxF,WAAW,EAAE,KAAK;QAElB;;qCAE6B;QAC7B,UAAU,EAAE,IAAI;QAEhB;iGACyF;QACzF,UAAU,EAAE,IAAI;QAEhB,0EAA0E;QAC1E,OAAO,EAAE,EAAE;KACd,CAAA;AACL,CAAC;AApED,4BAoEC", "sourcesContent": ["// Export a new default each time\nexport default function() {\n    return {\n        /* Inline Javascript - @plugin still allowed */\n        javascriptEnabled: false,\n\n        /* Outputs a makefile import dependency list to stdout. */\n        depends: false,\n\n        /* (DEPRECATED) Compress using less built-in compression. \n         * This does an okay job but does not utilise all the tricks of \n         * dedicated css compression. */\n        compress: false,\n\n        /* Runs the less parser and just reports errors without any output. */\n        lint: false,\n\n        /* Sets available include paths.\n         * If the file in an @import rule does not exist at that exact location, \n         * less will look for it at the location(s) passed to this option. \n         * You might use this for instance to specify a path to a library which \n         * you want to be referenced simply and relatively in the less files. */\n        paths: [],\n\n        /* color output in the terminal */\n        color: true,\n\n        /* The strictImports controls whether the compiler will allow an @import inside of either \n         * @media blocks or (a later addition) other selector blocks.\n         * See: https://github.com/less/less.js/issues/656 */\n        strictImports: false,\n\n        /* Allow Imports from Insecure HTTPS Hosts */\n        insecure: false,\n\n        /* Allows you to add a path to every generated import and url in your css. \n         * This does not affect less import statements that are processed, just ones \n         * that are left in the output css. */\n        rootpath: '',\n\n        /* By default URLs are kept as-is, so if you import a file in a sub-directory \n         * that references an image, exactly the same URL will be output in the css. \n         * This option allows you to re-write URL's in imported files so that the \n         * URL is always relative to the base imported file */\n        rewriteUrls: false,\n\n        /* How to process math \n         *   0 always           - eagerly try to solve all operations\n         *   1 parens-division  - require parens for division \"/\"\n         *   2 parens | strict  - require parens for all operations\n         *   3 strict-legacy    - legacy strict behavior (super-strict)\n         */\n        math: 1,\n\n        /* Without this option, less attempts to guess at the output unit when it does maths. */\n        strictUnits: false,\n\n        /* Effectively the declaration is put at the top of your base Less file, \n         * meaning it can be used but it also can be overridden if this variable \n         * is defined in the file. */\n        globalVars: null,\n\n        /* As opposed to the global variable option, this puts the declaration at the\n         * end of your base file, meaning it will override anything defined in your Less file. */\n        modifyVars: null,\n\n        /* This option allows you to specify a argument to go on to every URL.  */\n        urlArgs: ''\n    }\n}"]}