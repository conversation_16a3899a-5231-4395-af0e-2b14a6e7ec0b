{"version": 3, "file": "unit-conversions.js", "sourceRoot": "", "sources": ["../../../src/less/data/unit-conversions.js"], "names": [], "mappings": ";;AAAA,kBAAe;IACX,MAAM,EAAE;QACJ,GAAG,EAAE,CAAC;QACN,IAAI,EAAE,IAAI;QACV,IAAI,EAAE,KAAK;QACX,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,MAAM,GAAG,EAAE;QACjB,IAAI,EAAE,MAAM,GAAG,EAAE;QACjB,IAAI,EAAE,MAAM,GAAG,EAAE,GAAG,EAAE;KACzB;IACD,QAAQ,EAAE;QACN,GAAG,EAAE,CAAC;QACN,IAAI,EAAE,KAAK;KACd;IACD,KAAK,EAAE;QACH,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;QACxB,KAAK,EAAE,CAAC,GAAG,GAAG;QACd,MAAM,EAAE,CAAC,GAAG,GAAG;QACf,MAAM,EAAE,CAAC;KACZ;CACJ,CAAC", "sourcesContent": ["export default {\n    length: {\n        'm': 1,\n        'cm': 0.01,\n        'mm': 0.001,\n        'in': 0.0254,\n        'px': 0.0254 / 96,\n        'pt': 0.0254 / 72,\n        'pc': 0.0254 / 72 * 12\n    },\n    duration: {\n        's': 1,\n        'ms': 0.001\n    },\n    angle: {\n        'rad': 1 / (2 * Math.PI),\n        'deg': 1 / 360,\n        'grad': 1 / 400,\n        'turn': 1\n    }\n};"]}