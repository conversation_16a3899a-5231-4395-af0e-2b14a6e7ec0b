{"version": 3, "file": "string.js", "sourceRoot": "", "sources": ["../../../src/less/functions/string.js"], "names": [], "mappings": ";;;AAAA,kEAAoC;AACpC,wEAA0C;AAC1C,0EAA4C;AAE5C,kBAAe;IACX,CAAC,EAAE,UAAU,GAAG;QACZ,OAAO,IAAI,gBAAM,CAAC,GAAG,EAAE,GAAG,YAAY,oBAAU,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IACxF,CAAC;IACD,MAAM,EAAE,UAAU,GAAG;QACjB,OAAO,IAAI,mBAAS,CAChB,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC;aACnG,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;IAC1D,CAAC;IACD,OAAO,EAAE,UAAU,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK;QAClD,IAAI,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC;QAC1B,WAAW,GAAG,CAAC,WAAW,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC;YAC3C,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;QAC5C,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC;QAC1F,OAAO,IAAI,gBAAM,CAAC,MAAM,CAAC,KAAK,IAAI,EAAE,EAAE,MAAM,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;IAClE,CAAC;IACD,GAAG,EAAE,UAAU,MAAM,CAAC,mBAAmB;QACrC,IAAM,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;QACtD,IAAI,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC;gCAEjB,CAAC;YACN,0BAA0B;YAC1B,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,UAAA,KAAK;gBACpC,IAAM,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC;oBACtC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;gBACzD,OAAO,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YACrE,CAAC,CAAC,CAAC;;QANP,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE;oBAA3B,CAAC;SAOT;QACD,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QACpC,OAAO,IAAI,gBAAM,CAAC,MAAM,CAAC,KAAK,IAAI,EAAE,EAAE,MAAM,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;IAClE,CAAC;CACJ,CAAC", "sourcesContent": ["import Quoted from '../tree/quoted';\nimport Anonymous from '../tree/anonymous';\nimport JavaScript from '../tree/javascript';\n\nexport default {\n    e: function (str) {\n        return new Quoted('\"', str instanceof JavaScript ? str.evaluated : str.value, true);\n    },\n    escape: function (str) {\n        return new Anonymous(\n            encodeURI(str.value).replace(/=/g, '%3D').replace(/:/g, '%3A').replace(/#/g, '%23').replace(/;/g, '%3B')\n                .replace(/\\(/g, '%28').replace(/\\)/g, '%29'));\n    },\n    replace: function (string, pattern, replacement, flags) {\n        let result = string.value;\n        replacement = (replacement.type === 'Quoted') ?\n            replacement.value : replacement.toCSS();\n        result = result.replace(new RegExp(pattern.value, flags ? flags.value : ''), replacement);\n        return new Quoted(string.quote || '', result, string.escaped);\n    },\n    '%': function (string /* arg, arg, ... */) {\n        const args = Array.prototype.slice.call(arguments, 1);\n        let result = string.value;\n\n        for (let i = 0; i < args.length; i++) {\n            /* jshint loopfunc:true */\n            result = result.replace(/%[sda]/i, token => {\n                const value = ((args[i].type === 'Quoted') &&\n                    token.match(/s/i)) ? args[i].value : args[i].toCSS();\n                return token.match(/[A-Z]$/) ? encodeURIComponent(value) : value;\n            });\n        }\n        result = result.replace(/%%/g, '%');\n        return new Quoted(string.quote || '', result, string.escaped);\n    }\n};\n"]}