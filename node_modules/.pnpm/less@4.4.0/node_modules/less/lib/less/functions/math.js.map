{"version": 3, "file": "math.js", "sourceRoot": "", "sources": ["../../../src/less/functions/math.js"], "names": [], "mappings": ";;;AAAA,4EAA0C;AAE1C,IAAM,aAAa,GAAG;IAClB,cAAc;IACd,IAAI,EAAG,IAAI;IACX,KAAK,EAAE,IAAI;IACX,IAAI,EAAG,IAAI;IACX,GAAG,EAAI,IAAI;IACX,GAAG,EAAI,EAAE;IACT,GAAG,EAAI,EAAE;IACT,GAAG,EAAI,EAAE;IACT,IAAI,EAAG,KAAK;IACZ,IAAI,EAAG,KAAK;IACZ,IAAI,EAAG,KAAK;CACf,CAAC;AAEF,KAAK,IAAM,CAAC,IAAI,aAAa,EAAE;IAC3B,iDAAiD;IACjD,IAAI,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE;QACjC,aAAa,CAAC,CAAC,CAAC,GAAG,wBAAU,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;KACvE;CACJ;AAED,aAAa,CAAC,KAAK,GAAG,UAAC,CAAC,EAAE,CAAC;IACvB,IAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IACxD,OAAO,IAAA,wBAAU,EAAC,UAAA,GAAG,IAAI,OAAA,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,EAArB,CAAqB,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AAC7D,CAAC,CAAC;AAEF,kBAAe,aAAa,CAAC", "sourcesContent": ["import mathHelper from './math-helper.js';\n\nconst mathFunctions = {\n    // name,  unit\n    ceil:  null,\n    floor: null,\n    sqrt:  null,\n    abs:   null,\n    tan:   '',\n    sin:   '',\n    cos:   '',\n    atan:  'rad',\n    asin:  'rad',\n    acos:  'rad'\n};\n\nfor (const f in mathFunctions) {\n    // eslint-disable-next-line no-prototype-builtins\n    if (mathFunctions.hasOwnProperty(f)) {\n        mathFunctions[f] = mathHelper.bind(null, Math[f], mathFunctions[f]);\n    }\n}\n\nmathFunctions.round = (n, f) => {\n    const fraction = typeof f === 'undefined' ? 0 : f.value;\n    return mathHelper(num => num.toFixed(fraction), null, n);\n};\n\nexport default mathFunctions;\n"]}