{"version": 3, "file": "style.js", "sourceRoot": "", "sources": ["../../../src/less/functions/style.js"], "names": [], "mappings": ";;;AAAA,sEAAwC;AACxC,sEAAyC;AAEzC,IAAM,eAAe,GAAG,UAAU,IAAI;IAAd,iBAWvB;IAVG,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACxC,QAAQ,IAAI,CAAC,MAAM,EAAE;QACjB,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,OAAO,EAAE,gCAAgC,EAAE,CAAC;KACjF;IAED,IAAM,UAAU,GAAG,CAAC,IAAI,kBAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IAEtG,IAAI,GAAG,UAAU,CAAC,GAAG,CAAC,UAAA,CAAC,IAAM,OAAO,CAAC,CAAC,KAAK,CAAC,KAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAEvG,OAAO,IAAI,kBAAS,CAAC,gBAAS,IAAI,MAAG,CAAC,CAAC;AAC3C,CAAC,CAAC;AAEF,kBAAe;IACX,KAAK,EAAE;QAAS,cAAO;aAAP,UAAO,EAAP,qBAAO,EAAP,IAAO;YAAP,yBAAO;;QACnB,IAAI;YACA,OAAO,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SAC3C;QAAC,OAAO,CAAC,EAAE,GAAE;IAClB,CAAC;CACJ,CAAC", "sourcesContent": ["import Variable from '../tree/variable';\nimport Anonymous from '../tree/variable';\n\nconst styleExpression = function (args) {\n    args = Array.prototype.slice.call(args);\n    switch (args.length) {\n        case 0: throw { type: 'Argument', message: 'one or more arguments required' };\n    }\n    \n    const entityList = [new Variable(args[0].value, this.index, this.currentFileInfo).eval(this.context)];\n       \n    args = entityList.map(a => { return a.toCSS(this.context); }).join(this.context.compress ? ',' : ', ');\n    \n    return new Anonymous(`style(${args})`);\n};\n\nexport default {\n    style: function(...args) {\n        try {\n            return styleExpression.call(this, args);\n        } catch (e) {}\n    },\n};\n"]}