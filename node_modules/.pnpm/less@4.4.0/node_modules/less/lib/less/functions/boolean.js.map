{"version": 3, "file": "boolean.js", "sourceRoot": "", "sources": ["../../../src/less/functions/boolean.js"], "names": [], "mappings": ";;;AAAA,wEAA0C;AAC1C,oEAAsC;AAEtC,SAAS,OAAO,CAAC,SAAS;IACtB,OAAO,SAAS,CAAC,CAAC,CAAC,iBAAO,CAAC,IAAI,CAAC,CAAC,CAAC,iBAAO,CAAC,KAAK,CAAC;AACpD,CAAC;AAED;;;GAGG;AACH,SAAS,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU;IACjD,OAAO,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC;QACpD,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,mBAAS,CAAC,CAAC;AAClE,CAAC;AACD,EAAE,CAAC,QAAQ,GAAG,KAAK,CAAC;AAEpB,SAAS,SAAS,CAAC,OAAO,EAAE,QAAQ;IAChC,IAAI;QACA,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACvB,OAAO,iBAAO,CAAC,IAAI,CAAC;KACvB;IAAC,OAAO,CAAC,EAAE;QACR,OAAO,iBAAO,CAAC,KAAK,CAAC;KACxB;AACL,CAAC;AAED,SAAS,CAAC,QAAQ,GAAG,KAAK,CAAC;AAE3B,kBAAe,EAAE,SAAS,WAAA,EAAE,OAAO,SAAA,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC", "sourcesContent": ["import Anonymous from '../tree/anonymous';\nimport Keyword from '../tree/keyword';\n\nfunction boolean(condition) {\n    return condition ? Keyword.True : Keyword.False;\n}\n\n/**\n * Functions with evalArgs set to false are sent context\n * as the first argument.\n */\nfunction If(context, condition, trueValue, falseValue) {\n    return condition.eval(context) ? trueValue.eval(context)\n        : (falseValue ? falseValue.eval(context) : new Anonymous);\n}\nIf.evalArgs = false;\n\nfunction isdefined(context, variable) {\n    try {\n        variable.eval(context);\n        return Keyword.True;\n    } catch (e) {\n        return Keyword.False;\n    }\n}\n\nisdefined.evalArgs = false;\n\nexport default { isdefined, boolean, 'if': If };\n"]}