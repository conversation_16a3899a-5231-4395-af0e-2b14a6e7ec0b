{"version": 3, "file": "math-helper.js", "sourceRoot": "", "sources": ["../../../src/less/functions/math-helper.js"], "names": [], "mappings": ";;;AAAA,wEAA0C;AAE1C,IAAM,UAAU,GAAG,UAAC,EAAE,EAAE,IAAI,EAAE,CAAC;IAC3B,IAAI,CAAC,CAAC,CAAC,YAAY,mBAAS,CAAC,EAAE;QAC3B,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,OAAO,EAAE,2BAA2B,EAAE,CAAC;KACpE;IACD,IAAI,IAAI,KAAK,IAAI,EAAE;QACf,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;KACjB;SAAM;QACH,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC;KACjB;IACD,OAAO,IAAI,mBAAS,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AACxD,CAAC,CAAC;AAEF,kBAAe,UAAU,CAAC", "sourcesContent": ["import Dimension from '../tree/dimension';\n\nconst MathHelper = (fn, unit, n) => {\n    if (!(n instanceof Dimension)) {\n        throw { type: 'Argument', message: 'argument must be a number' };\n    }\n    if (unit === null) {\n        unit = n.unit;\n    } else {\n        n = n.unify();\n    }\n    return new Dimension(fn(parseFloat(n.value)), unit);\n};\n\nexport default MathHelper;"]}