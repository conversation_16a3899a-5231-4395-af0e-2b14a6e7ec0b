{"version": 3, "file": "color.js", "sourceRoot": "", "sources": ["../../../src/less/functions/color.js"], "names": [], "mappings": ";;;AAAA,wEAA0C;AAC1C,gEAAkC;AAClC,kEAAoC;AACpC,wEAA0C;AAC1C,0EAA4C;AAC5C,wEAA0C;AAC1C,IAAI,cAAc,CAAC;AAEnB,SAAS,KAAK,CAAC,GAAG;IACd,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AACzC,CAAC;AACD,SAAS,IAAI,CAAC,SAAS,EAAE,GAAG;IACxB,IAAM,KAAK,GAAG,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;IAC9D,IAAI,KAAK,EAAE;QACP,IAAI,SAAS,CAAC,KAAK;YACf,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;YACpC,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC;SACjC;aAAM;YACH,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;SACvB;QACD,OAAO,KAAK,CAAC;KAChB;AACL,CAAC;AACD,SAAS,KAAK,CAAC,KAAK;IAChB,IAAI,KAAK,CAAC,KAAK,EAAE;QACb,OAAO,KAAK,CAAC,KAAK,EAAE,CAAC;KACxB;SAAM;QACH,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;KAC9D;AACL,CAAC;AAED,SAAS,KAAK,CAAC,KAAK;IAChB,IAAI,KAAK,CAAC,KAAK,EAAE;QACb,OAAO,KAAK,CAAC,KAAK,EAAE,CAAC;KACxB;SAAM;QACH,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAC;KAC9D;AACL,CAAC;AAED,SAAS,MAAM,CAAC,CAAC;IACb,IAAI,CAAC,YAAY,mBAAS,EAAE;QACxB,OAAO,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;KAC/D;SAAM,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;QAC9B,OAAO,CAAC,CAAC;KACZ;SAAM;QACH,MAAM;YACF,IAAI,EAAE,UAAU;YAChB,OAAO,EAAE,4CAA4C;SACxD,CAAC;KACL;AACL,CAAC;AACD,SAAS,MAAM,CAAC,CAAC,EAAE,IAAI;IACnB,IAAI,CAAC,YAAY,mBAAS,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE;QAC1C,OAAO,UAAU,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC;KAC3C;SAAM;QACH,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;KACpB;AACL,CAAC;AACD,cAAc,GAAG;IACb,GAAG,EAAE,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC;QAClB,IAAI,CAAC,GAAG,CAAC,CAAA;QACT;;;WAGG;QACH,IAAI,CAAC,YAAY,oBAAU,EAAE;YACzB,IAAM,GAAG,GAAG,CAAC,CAAC,KAAK,CAAA;YACnB,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAA;YACV,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAA;YACV,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAA;YACV;;;eAGG;YACH,IAAI,CAAC,YAAY,mBAAS,EAAE;gBACxB,IAAM,EAAE,GAAG,CAAC,CAAA;gBACZ,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAA;gBAClB,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAA;aACrB;SACJ;QACD,IAAM,KAAK,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9C,IAAI,KAAK,EAAE;YACP,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;YACpB,OAAO,KAAK,CAAC;SAChB;IACL,CAAC;IACD,IAAI,EAAE,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QACtB,IAAI;YACA,IAAI,CAAC,YAAY,eAAK,EAAE;gBACpB,IAAI,CAAC,EAAE;oBACH,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;iBACjB;qBAAM;oBACH,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC;iBACf;gBACD,OAAO,IAAI,eAAK,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;aACtC;YACD,IAAM,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC,EAAd,CAAc,CAAC,CAAC;YAC/C,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACd,OAAO,IAAI,eAAK,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;SACpC;QACD,OAAO,CAAC,EAAE,GAAE;IAChB,CAAC;IACD,GAAG,EAAE,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC;QAClB,IAAI,CAAC,GAAG,CAAC,CAAA;QACT,IAAI,CAAC,YAAY,oBAAU,EAAE;YACzB,IAAM,GAAG,GAAG,CAAC,CAAC,KAAK,CAAA;YACnB,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAA;YACV,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAA;YACV,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAA;YAEV,IAAI,CAAC,YAAY,mBAAS,EAAE;gBACxB,IAAM,EAAE,GAAG,CAAC,CAAA;gBACZ,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAA;gBAClB,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAA;aACrB;SACJ;QACD,IAAM,KAAK,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAC9C,IAAI,KAAK,EAAE;YACP,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;YACpB,OAAO,KAAK,CAAC;SAChB;IACL,CAAC;IACD,IAAI,EAAE,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QACtB,IAAI,EAAE,CAAC;QACP,IAAI,EAAE,CAAC;QAEP,SAAS,GAAG,CAAC,CAAC;YACV,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACxC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;gBACX,OAAO,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;aACjC;iBACI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;gBAChB,OAAO,EAAE,CAAC;aACb;iBACI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;gBAChB,OAAO,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;aAC3C;iBACI;gBACD,OAAO,EAAE,CAAC;aACb;QACL,CAAC;QAED,IAAI;YACA,IAAI,CAAC,YAAY,eAAK,EAAE;gBACpB,IAAI,CAAC,EAAE;oBACH,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;iBACjB;qBAAM;oBACH,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC;iBACf;gBACD,OAAO,IAAI,eAAK,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;aACtC;YAED,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;YAC5B,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAAA,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAAA,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YAE/D,EAAE,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAC5C,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;YAEhB,IAAM,GAAG,GAAG;gBACR,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG;gBACpB,GAAG,CAAC,CAAC,CAAC,GAAS,GAAG;gBAClB,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG;aACvB,CAAC;YACF,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACd,OAAO,IAAI,eAAK,CAAC,GAAG,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;SACpC;QACD,OAAO,CAAC,EAAE,GAAE;IAChB,CAAC;IAED,GAAG,EAAE,UAAS,CAAC,EAAE,CAAC,EAAE,CAAC;QACjB,OAAO,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;IAC7C,CAAC;IAED,IAAI,EAAE,UAAS,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QACrB,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QACpC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QAAA,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QAAA,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QAE1C,IAAI,CAAC,CAAC;QACN,IAAI,CAAC,CAAC;QACN,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;QAC7B,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;QAEjB,IAAM,EAAE,GAAG,CAAC,CAAC;YACT,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;YACX,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACf,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC3B,IAAM,IAAI,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACnB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACT,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAEf,OAAO,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,EAC3C,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,EACpB,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,EACpB,CAAC,CAAC,CAAC;IACX,CAAC;IAED,GAAG,EAAE,UAAU,KAAK;QAChB,OAAO,IAAI,mBAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACzC,CAAC;IACD,UAAU,EAAE,UAAU,KAAK;QACvB,OAAO,IAAI,mBAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;IACpD,CAAC;IACD,SAAS,EAAE,UAAU,KAAK;QACtB,OAAO,IAAI,mBAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;IACpD,CAAC;IACD,MAAM,EAAE,UAAS,KAAK;QAClB,OAAO,IAAI,mBAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACzC,CAAC;IACD,aAAa,EAAE,UAAU,KAAK;QAC1B,OAAO,IAAI,mBAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;IACpD,CAAC;IACD,QAAQ,EAAE,UAAU,KAAK;QACrB,OAAO,IAAI,mBAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;IACpD,CAAC;IACD,GAAG,EAAE,UAAU,KAAK;QAChB,OAAO,IAAI,mBAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC,CAAC;IACD,KAAK,EAAE,UAAU,KAAK;QAClB,OAAO,IAAI,mBAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC,CAAC;IACD,IAAI,EAAE,UAAU,KAAK;QACjB,OAAO,IAAI,mBAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC,CAAC;IACD,KAAK,EAAE,UAAU,KAAK;QAClB,OAAO,IAAI,mBAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACzC,CAAC;IACD,IAAI,EAAE,UAAU,KAAK;QACjB,OAAO,IAAI,mBAAS,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,KAAK,CAAC,KAAK,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;IAChE,CAAC;IACD,SAAS,EAAE,UAAU,KAAK;QACtB,IAAM,SAAS,GACX,CAAC,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;YACzB,CAAC,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;YAC7B,CAAC,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;QAEtC,OAAO,IAAI,mBAAS,CAAC,SAAS,GAAG,KAAK,CAAC,KAAK,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;IAC7D,CAAC;IACD,QAAQ,EAAE,UAAU,KAAK,EAAE,MAAM,EAAE,MAAM;QACrC,yBAAyB;QACzB,2CAA2C;QAC3C,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE;YACZ,OAAO,IAAI,CAAC;SACf;QACD,IAAM,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;QAEzB,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,KAAK,KAAK,UAAU,EAAE;YAC9D,GAAG,CAAC,CAAC,IAAK,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,GAAG,GAAG,CAAC;SACxC;aACI;YACD,GAAG,CAAC,CAAC,IAAI,MAAM,CAAC,KAAK,GAAG,GAAG,CAAC;SAC/B;QACD,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACrB,OAAO,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAC5B,CAAC;IACD,UAAU,EAAE,UAAU,KAAK,EAAE,MAAM,EAAE,MAAM;QACvC,IAAM,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;QAEzB,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,KAAK,KAAK,UAAU,EAAE;YAC9D,GAAG,CAAC,CAAC,IAAK,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,GAAG,GAAG,CAAC;SACxC;aACI;YACD,GAAG,CAAC,CAAC,IAAI,MAAM,CAAC,KAAK,GAAG,GAAG,CAAC;SAC/B;QACD,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACrB,OAAO,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAC5B,CAAC;IACD,OAAO,EAAE,UAAU,KAAK,EAAE,MAAM,EAAE,MAAM;QACpC,IAAM,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;QAEzB,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,KAAK,KAAK,UAAU,EAAE;YAC9D,GAAG,CAAC,CAAC,IAAK,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,GAAG,GAAG,CAAC;SACxC;aACI;YACD,GAAG,CAAC,CAAC,IAAI,MAAM,CAAC,KAAK,GAAG,GAAG,CAAC;SAC/B;QACD,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACrB,OAAO,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAC5B,CAAC;IACD,MAAM,EAAE,UAAU,KAAK,EAAE,MAAM,EAAE,MAAM;QACnC,IAAM,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;QAEzB,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,KAAK,KAAK,UAAU,EAAE;YAC9D,GAAG,CAAC,CAAC,IAAK,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,GAAG,GAAG,CAAC;SACxC;aACI;YACD,GAAG,CAAC,CAAC,IAAI,MAAM,CAAC,KAAK,GAAG,GAAG,CAAC;SAC/B;QACD,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACrB,OAAO,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAC5B,CAAC;IACD,MAAM,EAAE,UAAU,KAAK,EAAE,MAAM,EAAE,MAAM;QACnC,IAAM,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;QAEzB,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,KAAK,KAAK,UAAU,EAAE;YAC9D,GAAG,CAAC,CAAC,IAAK,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,GAAG,GAAG,CAAC;SACxC;aACI;YACD,GAAG,CAAC,CAAC,IAAI,MAAM,CAAC,KAAK,GAAG,GAAG,CAAC;SAC/B;QACD,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACrB,OAAO,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAC5B,CAAC;IACD,OAAO,EAAE,UAAU,KAAK,EAAE,MAAM,EAAE,MAAM;QACpC,IAAM,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;QAEzB,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,KAAK,KAAK,UAAU,EAAE;YAC9D,GAAG,CAAC,CAAC,IAAK,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,GAAG,GAAG,CAAC;SACxC;aACI;YACD,GAAG,CAAC,CAAC,IAAI,MAAM,CAAC,KAAK,GAAG,GAAG,CAAC;SAC/B;QACD,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACrB,OAAO,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAC5B,CAAC;IACD,IAAI,EAAE,UAAU,KAAK,EAAE,MAAM;QACzB,IAAM,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;QAEzB,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,GAAG,GAAG,CAAC;QAC3B,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACrB,OAAO,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAC5B,CAAC;IACD,IAAI,EAAE,UAAU,KAAK,EAAE,MAAM;QACzB,IAAM,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;QACzB,IAAM,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC;QAEzC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QAElC,OAAO,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAC5B,CAAC;IACD,EAAE;IACF,iFAAiF;IACjF,uBAAuB;IACvB,EAAE;IACF,GAAG,EAAE,UAAU,MAAM,EAAE,MAAM,EAAE,MAAM;QACjC,IAAI,CAAC,MAAM,EAAE;YACT,MAAM,GAAG,IAAI,mBAAS,CAAC,EAAE,CAAC,CAAC;SAC9B;QACD,IAAM,CAAC,GAAG,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;QAC/B,IAAM,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACpB,IAAM,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAE5C,IAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;QACnE,IAAM,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;QAElB,IAAM,GAAG,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE;YAChD,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE;YACvC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;QAE7C,IAAM,KAAK,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAExD,OAAO,IAAI,eAAK,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IACjC,CAAC;IACD,SAAS,EAAE,UAAU,KAAK;QACtB,OAAO,cAAc,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,mBAAS,CAAC,GAAG,CAAC,CAAC,CAAC;IAChE,CAAC;IACD,QAAQ,EAAE,UAAU,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS;QAC7C,yBAAyB;QACzB,2CAA2C;QAC3C,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE;YACZ,OAAO,IAAI,CAAC;SACf;QACD,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;YAC9B,KAAK,GAAG,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;SACnD;QACD,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;YAC7B,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;SAC5C;QACD,+CAA+C;QAC/C,IAAI,IAAI,CAAC,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,EAAE;YAC5B,IAAM,CAAC,GAAG,KAAK,CAAC;YAChB,KAAK,GAAG,IAAI,CAAC;YACb,IAAI,GAAG,CAAC,CAAC;SACZ;QACD,IAAI,OAAO,SAAS,KAAK,WAAW,EAAE;YAClC,SAAS,GAAG,IAAI,CAAC;SACpB;aAAM;YACH,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;SACjC;QACD,IAAI,KAAK,CAAC,IAAI,EAAE,GAAG,SAAS,EAAE;YAC1B,OAAO,KAAK,CAAC;SAChB;aAAM;YACH,OAAO,IAAI,CAAC;SACf;IACL,CAAC;IACD,4CAA4C;IAC5C,0DAA0D;IAC1D,sFAAsF;IACtF,oEAAoE;IACpE,wDAAwD;IACxD,mEAAmE;IACnE,gCAAgC;IAChC,kDAAkD;IAClD,wBAAwB;IACxB,uBAAuB;IACvB,QAAQ;IACR,2CAA2C;IAC3C,sDAAsD;IACtD,QAAQ;IACR,2CAA2C;IAC3C,4DAA4D;IAC5D,QAAQ;IACR,gCAAgC;IAChC,+BAA+B;IAC/B,iCAAiC;IACjC,iCAAiC;IACjC,kDAAkD;IAClD,0BAA0B;IAC1B,sDAAsD;IACtD,eAAe;IACf,sDAAsD;IACtD,QAAQ;IACR,0BAA0B;IAC1B,sDAAsD;IACtD,eAAe;IACf,sDAAsD;IACtD,QAAQ;IACR,mCAAmC;IACnC,yBAAyB;IACzB,eAAe;IACf,yBAAyB;IACzB,QAAQ;IACR,KAAK;IACL,IAAI,EAAE,UAAU,KAAK;QACjB,OAAO,IAAI,mBAAS,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;IACzC,CAAC;IACD,KAAK,EAAE,UAAS,CAAC;QACb,IAAI,CAAC,CAAC,YAAY,gBAAM,CAAC;YACrB,CAAC,sDAAsD,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;YACxE,IAAM,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC7B,OAAO,IAAI,eAAK,CAAC,GAAG,EAAE,SAAS,EAAE,WAAI,GAAG,CAAE,CAAC,CAAC;SAC/C;QACD,IAAI,CAAC,CAAC,YAAY,eAAK,CAAC,IAAI,CAAC,CAAC,GAAG,eAAK,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;YAC1D,CAAC,CAAC,KAAK,GAAG,SAAS,CAAC;YACpB,OAAO,CAAC,CAAC;SACZ;QACD,MAAM;YACF,IAAI,EAAK,UAAU;YACnB,OAAO,EAAE,iEAAiE;SAC7E,CAAC;IACN,CAAC;IACD,IAAI,EAAE,UAAS,KAAK,EAAE,MAAM;QACxB,OAAO,cAAc,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;IAChF,CAAC;IACD,KAAK,EAAE,UAAS,KAAK,EAAE,MAAM;QACzB,OAAO,cAAc,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;IAC1E,CAAC;CACJ,CAAC;AAEF,kBAAe,cAAc,CAAC", "sourcesContent": ["import Dimension from '../tree/dimension';\nimport Color from '../tree/color';\nimport Quoted from '../tree/quoted';\nimport Anonymous from '../tree/anonymous';\nimport Expression from '../tree/expression';\nimport Operation from '../tree/operation';\nlet colorFunctions;\n\nfunction clamp(val) {\n    return Math.min(1, Math.max(0, val));\n}\nfunction hsla(origColor, hsl) {\n    const color = colorFunctions.hsla(hsl.h, hsl.s, hsl.l, hsl.a);\n    if (color) {\n        if (origColor.value && \n            /^(rgb|hsl)/.test(origColor.value)) {\n            color.value = origColor.value;\n        } else {\n            color.value = 'rgb';\n        }\n        return color;\n    }\n}\nfunction toHSL(color) {\n    if (color.toHSL) {\n        return color.toHSL();\n    } else {\n        throw new Error('Argument cannot be evaluated to a color');\n    }\n}\n\nfunction toHSV(color) {\n    if (color.toHSV) {\n        return color.toHSV();\n    } else {\n        throw new Error('Argument cannot be evaluated to a color');\n    }\n}\n\nfunction number(n) {\n    if (n instanceof Dimension) {\n        return parseFloat(n.unit.is('%') ? n.value / 100 : n.value);\n    } else if (typeof n === 'number') {\n        return n;\n    } else {\n        throw {\n            type: 'Argument',\n            message: 'color functions take numbers as parameters'\n        };\n    }\n}\nfunction scaled(n, size) {\n    if (n instanceof Dimension && n.unit.is('%')) {\n        return parseFloat(n.value * size / 100);\n    } else {\n        return number(n);\n    }\n}\ncolorFunctions = {\n    rgb: function (r, g, b) {\n        let a = 1\n        /**\n         * Comma-less syntax\n         *   e.g. rgb(0 128 255 / 50%)\n         */\n        if (r instanceof Expression) {\n            const val = r.value\n            r = val[0]\n            g = val[1]\n            b = val[2]\n            /** \n             * @todo - should this be normalized in\n             *   function caller? Or parsed differently?\n             */\n            if (b instanceof Operation) {\n                const op = b\n                b = op.operands[0]\n                a = op.operands[1]\n            }\n        }\n        const color = colorFunctions.rgba(r, g, b, a);\n        if (color) {\n            color.value = 'rgb';\n            return color;\n        }\n    },\n    rgba: function (r, g, b, a) {\n        try {\n            if (r instanceof Color) {\n                if (g) {\n                    a = number(g);\n                } else {\n                    a = r.alpha;\n                }\n                return new Color(r.rgb, a, 'rgba');\n            }\n            const rgb = [r, g, b].map(c => scaled(c, 255));\n            a = number(a);\n            return new Color(rgb, a, 'rgba');\n        }\n        catch (e) {}\n    },\n    hsl: function (h, s, l) {\n        let a = 1\n        if (h instanceof Expression) {\n            const val = h.value\n            h = val[0]\n            s = val[1]\n            l = val[2]\n\n            if (l instanceof Operation) {\n                const op = l\n                l = op.operands[0]\n                a = op.operands[1]\n            }\n        }\n        const color = colorFunctions.hsla(h, s, l, a);\n        if (color) {\n            color.value = 'hsl';\n            return color;\n        }\n    },\n    hsla: function (h, s, l, a) {\n        let m1;\n        let m2;\n\n        function hue(h) {\n            h = h < 0 ? h + 1 : (h > 1 ? h - 1 : h);\n            if (h * 6 < 1) {\n                return m1 + (m2 - m1) * h * 6;\n            }\n            else if (h * 2 < 1) {\n                return m2;\n            }\n            else if (h * 3 < 2) {\n                return m1 + (m2 - m1) * (2 / 3 - h) * 6;\n            }\n            else {\n                return m1;\n            }\n        }\n\n        try {\n            if (h instanceof Color) {\n                if (s) {\n                    a = number(s);\n                } else {\n                    a = h.alpha;\n                }\n                return new Color(h.rgb, a, 'hsla');\n            }\n\n            h = (number(h) % 360) / 360;\n            s = clamp(number(s));l = clamp(number(l));a = clamp(number(a));\n\n            m2 = l <= 0.5 ? l * (s + 1) : l + s - l * s;\n            m1 = l * 2 - m2;\n\n            const rgb = [\n                hue(h + 1 / 3) * 255,\n                hue(h)       * 255,\n                hue(h - 1 / 3) * 255\n            ];\n            a = number(a);\n            return new Color(rgb, a, 'hsla');\n        }\n        catch (e) {}\n    },\n\n    hsv: function(h, s, v) {\n        return colorFunctions.hsva(h, s, v, 1.0);\n    },\n\n    hsva: function(h, s, v, a) {\n        h = ((number(h) % 360) / 360) * 360;\n        s = number(s);v = number(v);a = number(a);\n\n        let i;\n        let f;\n        i = Math.floor((h / 60) % 6);\n        f = (h / 60) - i;\n\n        const vs = [v,\n            v * (1 - s),\n            v * (1 - f * s),\n            v * (1 - (1 - f) * s)];\n        const perm = [[0, 3, 1],\n            [2, 0, 1],\n            [1, 0, 3],\n            [1, 2, 0],\n            [3, 1, 0],\n            [0, 1, 2]];\n\n        return colorFunctions.rgba(vs[perm[i][0]] * 255,\n            vs[perm[i][1]] * 255,\n            vs[perm[i][2]] * 255,\n            a);\n    },\n\n    hue: function (color) {\n        return new Dimension(toHSL(color).h);\n    },\n    saturation: function (color) {\n        return new Dimension(toHSL(color).s * 100, '%');\n    },\n    lightness: function (color) {\n        return new Dimension(toHSL(color).l * 100, '%');\n    },\n    hsvhue: function(color) {\n        return new Dimension(toHSV(color).h);\n    },\n    hsvsaturation: function (color) {\n        return new Dimension(toHSV(color).s * 100, '%');\n    },\n    hsvvalue: function (color) {\n        return new Dimension(toHSV(color).v * 100, '%');\n    },\n    red: function (color) {\n        return new Dimension(color.rgb[0]);\n    },\n    green: function (color) {\n        return new Dimension(color.rgb[1]);\n    },\n    blue: function (color) {\n        return new Dimension(color.rgb[2]);\n    },\n    alpha: function (color) {\n        return new Dimension(toHSL(color).a);\n    },\n    luma: function (color) {\n        return new Dimension(color.luma() * color.alpha * 100, '%');\n    },\n    luminance: function (color) {\n        const luminance =\n            (0.2126 * color.rgb[0] / 255) +\n                (0.7152 * color.rgb[1] / 255) +\n                (0.0722 * color.rgb[2] / 255);\n\n        return new Dimension(luminance * color.alpha * 100, '%');\n    },\n    saturate: function (color, amount, method) {\n        // filter: saturate(3.2);\n        // should be kept as is, so check for color\n        if (!color.rgb) {\n            return null;\n        }\n        const hsl = toHSL(color);\n\n        if (typeof method !== 'undefined' && method.value === 'relative') {\n            hsl.s +=  hsl.s * amount.value / 100;\n        }\n        else {\n            hsl.s += amount.value / 100;\n        }\n        hsl.s = clamp(hsl.s);\n        return hsla(color, hsl);\n    },\n    desaturate: function (color, amount, method) {\n        const hsl = toHSL(color);\n\n        if (typeof method !== 'undefined' && method.value === 'relative') {\n            hsl.s -=  hsl.s * amount.value / 100;\n        }\n        else {\n            hsl.s -= amount.value / 100;\n        }\n        hsl.s = clamp(hsl.s);\n        return hsla(color, hsl);\n    },\n    lighten: function (color, amount, method) {\n        const hsl = toHSL(color);\n\n        if (typeof method !== 'undefined' && method.value === 'relative') {\n            hsl.l +=  hsl.l * amount.value / 100;\n        }\n        else {\n            hsl.l += amount.value / 100;\n        }\n        hsl.l = clamp(hsl.l);\n        return hsla(color, hsl);\n    },\n    darken: function (color, amount, method) {\n        const hsl = toHSL(color);\n\n        if (typeof method !== 'undefined' && method.value === 'relative') {\n            hsl.l -=  hsl.l * amount.value / 100;\n        }\n        else {\n            hsl.l -= amount.value / 100;\n        }\n        hsl.l = clamp(hsl.l);\n        return hsla(color, hsl);\n    },\n    fadein: function (color, amount, method) {\n        const hsl = toHSL(color);\n\n        if (typeof method !== 'undefined' && method.value === 'relative') {\n            hsl.a +=  hsl.a * amount.value / 100;\n        }\n        else {\n            hsl.a += amount.value / 100;\n        }\n        hsl.a = clamp(hsl.a);\n        return hsla(color, hsl);\n    },\n    fadeout: function (color, amount, method) {\n        const hsl = toHSL(color);\n\n        if (typeof method !== 'undefined' && method.value === 'relative') {\n            hsl.a -=  hsl.a * amount.value / 100;\n        }\n        else {\n            hsl.a -= amount.value / 100;\n        }\n        hsl.a = clamp(hsl.a);\n        return hsla(color, hsl);\n    },\n    fade: function (color, amount) {\n        const hsl = toHSL(color);\n\n        hsl.a = amount.value / 100;\n        hsl.a = clamp(hsl.a);\n        return hsla(color, hsl);\n    },\n    spin: function (color, amount) {\n        const hsl = toHSL(color);\n        const hue = (hsl.h + amount.value) % 360;\n\n        hsl.h = hue < 0 ? 360 + hue : hue;\n\n        return hsla(color, hsl);\n    },\n    //\n    // Copyright (c) 2006-2009 Hampton Catlin, Natalie Weizenbaum, and Chris Eppstein\n    // http://sass-lang.com\n    //\n    mix: function (color1, color2, weight) {\n        if (!weight) {\n            weight = new Dimension(50);\n        }\n        const p = weight.value / 100.0;\n        const w = p * 2 - 1;\n        const a = toHSL(color1).a - toHSL(color2).a;\n\n        const w1 = (((w * a == -1) ? w : (w + a) / (1 + w * a)) + 1) / 2.0;\n        const w2 = 1 - w1;\n\n        const rgb = [color1.rgb[0] * w1 + color2.rgb[0] * w2,\n            color1.rgb[1] * w1 + color2.rgb[1] * w2,\n            color1.rgb[2] * w1 + color2.rgb[2] * w2];\n\n        const alpha = color1.alpha * p + color2.alpha * (1 - p);\n\n        return new Color(rgb, alpha);\n    },\n    greyscale: function (color) {\n        return colorFunctions.desaturate(color, new Dimension(100));\n    },\n    contrast: function (color, dark, light, threshold) {\n        // filter: contrast(3.2);\n        // should be kept as is, so check for color\n        if (!color.rgb) {\n            return null;\n        }\n        if (typeof light === 'undefined') {\n            light = colorFunctions.rgba(255, 255, 255, 1.0);\n        }\n        if (typeof dark === 'undefined') {\n            dark = colorFunctions.rgba(0, 0, 0, 1.0);\n        }\n        // Figure out which is actually light and dark:\n        if (dark.luma() > light.luma()) {\n            const t = light;\n            light = dark;\n            dark = t;\n        }\n        if (typeof threshold === 'undefined') {\n            threshold = 0.43;\n        } else {\n            threshold = number(threshold);\n        }\n        if (color.luma() < threshold) {\n            return light;\n        } else {\n            return dark;\n        }\n    },\n    // Changes made in 2.7.0 - Reverted in 3.0.0\n    // contrast: function (color, color1, color2, threshold) {\n    //     // Return which of `color1` and `color2` has the greatest contrast with `color`\n    //     // according to the standard WCAG contrast ratio calculation.\n    //     // http://www.w3.org/TR/WCAG20/#contrast-ratiodef\n    //     // The threshold param is no longer used, in line with SASS.\n    //     // filter: contrast(3.2);\n    //     // should be kept as is, so check for color\n    //     if (!color.rgb) {\n    //         return null;\n    //     }\n    //     if (typeof color1 === 'undefined') {\n    //         color1 = colorFunctions.rgba(0, 0, 0, 1.0);\n    //     }\n    //     if (typeof color2 === 'undefined') {\n    //         color2 = colorFunctions.rgba(255, 255, 255, 1.0);\n    //     }\n    //     var contrast1, contrast2;\n    //     var luma = color.luma();\n    //     var luma1 = color1.luma();\n    //     var luma2 = color2.luma();\n    //     // Calculate contrast ratios for each color\n    //     if (luma > luma1) {\n    //         contrast1 = (luma + 0.05) / (luma1 + 0.05);\n    //     } else {\n    //         contrast1 = (luma1 + 0.05) / (luma + 0.05);\n    //     }\n    //     if (luma > luma2) {\n    //         contrast2 = (luma + 0.05) / (luma2 + 0.05);\n    //     } else {\n    //         contrast2 = (luma2 + 0.05) / (luma + 0.05);\n    //     }\n    //     if (contrast1 > contrast2) {\n    //         return color1;\n    //     } else {\n    //         return color2;\n    //     }\n    // },\n    argb: function (color) {\n        return new Anonymous(color.toARGB());\n    },\n    color: function(c) {\n        if ((c instanceof Quoted) &&\n            (/^#([A-Fa-f0-9]{8}|[A-Fa-f0-9]{6}|[A-Fa-f0-9]{3,4})$/i.test(c.value))) {\n            const val = c.value.slice(1);\n            return new Color(val, undefined, `#${val}`);\n        }\n        if ((c instanceof Color) || (c = Color.fromKeyword(c.value))) {\n            c.value = undefined;\n            return c;\n        }\n        throw {\n            type:    'Argument',\n            message: 'argument must be a color keyword or 3|4|6|8 digit hex e.g. #FFF'\n        };\n    },\n    tint: function(color, amount) {\n        return colorFunctions.mix(colorFunctions.rgb(255, 255, 255), color, amount);\n    },\n    shade: function(color, amount) {\n        return colorFunctions.mix(colorFunctions.rgb(0, 0, 0), color, amount);\n    }\n};\n\nexport default colorFunctions;\n"]}