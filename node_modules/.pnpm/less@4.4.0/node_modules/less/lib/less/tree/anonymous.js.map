{"version": 3, "file": "anonymous.js", "sourceRoot": "", "sources": ["../../../src/less/tree/anonymous.js"], "names": [], "mappings": ";;;AAAA,wDAA0B;AAE1B,IAAM,SAAS,GAAG,UAAS,KAAK,EAAE,KAAK,EAAE,eAAe,EAAE,QAAQ,EAAE,WAAW,EAAE,cAAc;IAC3F,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACnB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACpB,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC;IACjC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACzB,IAAI,CAAC,WAAW,GAAG,CAAC,OAAO,WAAW,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,WAAW,CAAC;IAC9E,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IACtB,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;AAC5C,CAAC,CAAA;AAED,SAAS,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,cAAI,EAAE,EAAE;IAC5C,IAAI,EAAE,WAAW;IACjB,IAAI;QACA,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;IAC1H,CAAC;IACD,OAAO,YAAC,KAAK;QACT,OAAO,KAAK,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IACzE,CAAC;IACD,aAAa;QACT,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IACD,MAAM,YAAC,OAAO,EAAE,MAAM;QAClB,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACvC,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;SACtE;IACL,CAAC;CACJ,CAAC,CAAA;AAEF,kBAAe,SAAS,CAAC", "sourcesContent": ["import Node from './node';\n\nconst Anonymous = function(value, index, currentFileInfo, mapLines, rulesetLike, visibilityInfo) {\n    this.value = value;\n    this._index = index;\n    this._fileInfo = currentFileInfo;\n    this.mapLines = mapLines;\n    this.rulesetLike = (typeof rulesetLike === 'undefined') ? false : rulesetLike;\n    this.allowRoot = true;\n    this.copyVisibilityInfo(visibilityInfo);\n}\n\nAnonymous.prototype = Object.assign(new Node(), {\n    type: 'Anonymous',\n    eval() {\n        return new Anonymous(this.value, this._index, this._fileInfo, this.mapLines, this.rulesetLike, this.visibilityInfo());\n    },\n    compare(other) {\n        return other.toCSS && this.toCSS() === other.toCSS() ? 0 : undefined;\n    },\n    isRulesetLike() {\n        return this.rulesetLike;\n    },\n    genCSS(context, output) {\n        this.nodeVisible = Boolean(this.value);\n        if (this.nodeVisible) {\n            output.add(this.value, this._fileInfo, this._index, this.mapLines);\n        }\n    }\n})\n\nexport default Anonymous;\n"]}