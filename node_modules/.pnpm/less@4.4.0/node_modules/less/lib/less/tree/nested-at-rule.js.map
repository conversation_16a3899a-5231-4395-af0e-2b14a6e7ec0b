{"version": 3, "file": "nested-at-rule.js", "sourceRoot": "", "sources": ["../../../src/less/tree/nested-at-rule.js"], "names": [], "mappings": ";;;AAAA,8DAAgC;AAChC,0DAA4B;AAC5B,gEAAkC;AAClC,kEAAoC;AACpC,oEAAsC;AACtC,sDAAkC;AAElC,IAAM,uBAAuB,GAAG;IAE5B,aAAa;QACT,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,MAAM,YAAC,OAAO;QACV,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SAChD;QACD,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAC/C;IACL,CAAC;IAED,OAAO,YAAC,OAAO;QACX,IAAI,MAAM,GAAG,IAAI,CAAC;QAElB,qCAAqC;QACrC,IAAI,OAAO,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;YAChC,IAAM,SAAS,GAAG,CAAC,IAAI,kBAAQ,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,oBAAoB,EAAE,CAAC;YAC1G,MAAM,GAAG,IAAI,iBAAO,CAAC,SAAS,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC;YACrD,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC;YACzB,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;YACjD,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;SAChC;QAED,OAAO,OAAO,CAAC,WAAW,CAAC;QAC3B,OAAO,OAAO,CAAC,SAAS,CAAC;QAEzB,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,UAAU,YAAC,OAAO;QACd,IAAI,CAAC,CAAC;QACN,IAAI,KAAK,CAAC;QACV,IAAM,IAAI,GAAG,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QAE9C,8DAA8D;QAC9D,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC9B,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE;gBAC5B,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBAEjC,OAAO,IAAI,CAAC;aACf;YAED,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,YAAY,eAAK,CAAC,CAAC;gBACvC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;YAC9C,IAAI,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;SACpD;QAED,gEAAgE;QAChE,EAAE;QACF,qCAAqC;QACrC,aAAa;QACb,aAAa;QACb,mBAAmB;QACnB,mBAAmB;QACnB,IAAI,CAAC,QAAQ,GAAG,IAAI,eAAK,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,UAAA,IAAI;YACjD,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,UAAA,QAAQ,IAAI,OAAA,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,mBAAS,CAAC,QAAQ,CAAC,EAAnD,CAAmD,CAAC,CAAC;YAEjF,KAAK,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;gBAClC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,mBAAS,CAAC,KAAK,CAAC,CAAC,CAAC;aAC3C;YAED,OAAO,IAAI,oBAAU,CAAC,IAAI,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC,CAAC;QACJ,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAEpC,iDAAiD;QACjD,OAAO,IAAI,iBAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAC/B,CAAC;IAED,OAAO,YAAC,GAAG;QACP,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;YAClB,OAAO,EAAE,CAAC;SACb;aAAM,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;YACzB,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;SACjB;aAAM;YACH,IAAM,MAAM,GAAG,EAAE,CAAC;YAClB,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YACxC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACpC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;iBAC5C;aACJ;YACD,OAAO,MAAM,CAAC;SACjB;IACL,CAAC;IAED,eAAe,YAAC,SAAS;QACrB,IAAI,CAAC,SAAS,EAAE;YACZ,OAAO;SACV;QACD,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,iBAAO,CAAC,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IACrC,CAAC;CACJ,CAAC;AAEF,kBAAe,uBAAuB,CAAC", "sourcesContent": ["import Ruleset from './ruleset';\nimport Value from './value';\nimport Selector from './selector';\nimport Anonymous from './anonymous';\nimport Expression from './expression';\nimport * as utils from '../utils';\n\nconst NestableAtRulePrototype = {\n\n    isRulesetLike() {\n        return true;\n    },\n\n    accept(visitor) {\n        if (this.features) {\n            this.features = visitor.visit(this.features);\n        }\n        if (this.rules) {\n            this.rules = visitor.visitArray(this.rules);\n        }\n    },\n\n    evalTop(context) {\n        let result = this;\n\n        // Render all dependent Media blocks.\n        if (context.mediaBlocks.length > 1) {\n            const selectors = (new Selector([], null, null, this.getIndex(), this.fileInfo())).createEmptySelectors();\n            result = new Ruleset(selectors, context.mediaBlocks);\n            result.multiMedia = true;\n            result.copyVisibilityInfo(this.visibilityInfo());\n            this.setParent(result, this);\n        }\n\n        delete context.mediaBlocks;\n        delete context.mediaPath;\n\n        return result;\n    },\n\n    evalNested(context) {\n        let i;\n        let value;\n        const path = context.mediaPath.concat([this]);\n\n        // Extract the media-query conditions separated with `,` (OR).\n        for (i = 0; i < path.length; i++) {\n            if (path[i].type !== this.type) { \n                context.mediaBlocks.splice(i, 1); \n                \n                return this; \n            }\n            \n            value = path[i].features instanceof Value ?\n                path[i].features.value : path[i].features;\n            path[i] = Array.isArray(value) ? value : [value];\n        }\n\n        // Trace all permutations to generate the resulting media-query.\n        //\n        // (a, b and c) with nested (d, e) ->\n        //    a and d\n        //    a and e\n        //    b and c and d\n        //    b and c and e\n        this.features = new Value(this.permute(path).map(path => {\n            path = path.map(fragment => fragment.toCSS ? fragment : new Anonymous(fragment));\n\n            for (i = path.length - 1; i > 0; i--) {\n                path.splice(i, 0, new Anonymous('and'));\n            }\n\n            return new Expression(path);\n        }));\n        this.setParent(this.features, this);\n\n        // Fake a tree-node that doesn't output anything.\n        return new Ruleset([], []);\n    },\n\n    permute(arr) {\n        if (arr.length === 0) {\n            return [];\n        } else if (arr.length === 1) {\n            return arr[0];\n        } else {\n            const result = [];\n            const rest = this.permute(arr.slice(1));\n            for (let i = 0; i < rest.length; i++) {\n                for (let j = 0; j < arr[0].length; j++) {\n                    result.push([arr[0][j]].concat(rest[i]));\n                }\n            }\n            return result;\n        }\n    },\n\n    bubbleSelectors(selectors) {\n        if (!selectors) {\n            return;\n        }\n        this.rules = [new Ruleset(utils.copyArray(selectors), [this.rules[0]])];\n        this.setParent(this.rules, this);\n    }\n};\n\nexport default NestableAtRulePrototype;\n"]}