{"version": 3, "file": "keyword.js", "sourceRoot": "", "sources": ["../../../src/less/tree/keyword.js"], "names": [], "mappings": ";;;AAAA,wDAA0B;AAE1B,IAAM,OAAO,GAAG,UAAS,KAAK;IAC1B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACvB,CAAC,CAAC;AAEF,OAAO,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,cAAI,EAAE,EAAE;IAC1C,IAAI,EAAE,SAAS;IAEf,MAAM,YAAC,OAAO,EAAE,MAAM;QAClB,IAAI,IAAI,CAAC,KAAK,KAAK,GAAG,EAAE;YAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,0BAA0B,EAAE,CAAC;SAAE;QAC1F,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC3B,CAAC;CACJ,CAAC,CAAC;AAEH,OAAO,CAAC,IAAI,GAAG,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC;AACnC,OAAO,CAAC,KAAK,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,CAAC;AAErC,kBAAe,OAAO,CAAC", "sourcesContent": ["import Node from './node';\n\nconst Keyword = function(value) {\n    this.value = value;\n};\n\nKeyword.prototype = Object.assign(new Node(), {\n    type: 'Keyword',\n\n    genCSS(context, output) {\n        if (this.value === '%') { throw { type: 'Syntax', message: 'Invalid % without number' }; }\n        output.add(this.value);\n    }\n});\n\nKeyword.True = new Keyword('true');\nKeyword.False = new Keyword('false');\n\nexport default Keyword;\n"]}