{"version": 3, "file": "comment.js", "sourceRoot": "", "sources": ["../../../src/less/tree/comment.js"], "names": [], "mappings": ";;;AAAA,wDAA0B;AAC1B,oEAAwC;AAExC,IAAM,OAAO,GAAG,UAAS,KAAK,EAAE,aAAa,EAAE,KAAK,EAAE,eAAe;IACjE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACnB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;IACnC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACpB,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC;IACjC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,CAAC,CAAA;AAED,OAAO,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,cAAI,EAAE,EAAE;IAC1C,IAAI,EAAE,SAAS;IAEf,MAAM,YAAC,OAAO,EAAE,MAAM;QAClB,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,MAAM,CAAC,GAAG,CAAC,IAAA,oBAAY,EAAC,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;SAC7E;QACD,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC3B,CAAC;IAED,QAAQ,YAAC,OAAO;QACZ,IAAM,YAAY,GAAG,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC;QAC/D,OAAO,IAAI,CAAC,aAAa,IAAI,YAAY,CAAC;IAC9C,CAAC;CACJ,CAAC,CAAC;AAEH,kBAAe,OAAO,CAAC", "sourcesContent": ["import Node from './node';\nimport getDebugInfo from './debug-info';\n\nconst Comment = function(value, isLineComment, index, currentFileInfo) {\n    this.value = value;\n    this.isLineComment = isLineComment;\n    this._index = index;\n    this._fileInfo = currentFileInfo;\n    this.allowRoot = true;\n}\n\nComment.prototype = Object.assign(new Node(), {\n    type: 'Comment',\n\n    genCSS(context, output) {\n        if (this.debugInfo) {\n            output.add(getDebugInfo(context, this), this.fileInfo(), this.getIndex());\n        }\n        output.add(this.value);\n    },\n\n    isSilent(context) {\n        const isCompressed = context.compress && this.value[2] !== '!';\n        return this.isLineComment || isCompressed;\n    }\n});\n\nexport default Comment;\n"]}