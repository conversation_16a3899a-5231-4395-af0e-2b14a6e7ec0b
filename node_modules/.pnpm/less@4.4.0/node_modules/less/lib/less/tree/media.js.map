{"version": 3, "file": "media.js", "sourceRoot": "", "sources": ["../../../src/less/tree/media.js"], "names": [], "mappings": ";;;AAAA,8DAAgC;AAChC,0DAA4B;AAC5B,gEAAkC;AAClC,4DAA8B;AAC9B,4EAAuD;AAEvD,IAAM,KAAK,GAAG,UAAS,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,eAAe,EAAE,cAAc;IAC1E,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACpB,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC;IAEjC,IAAM,SAAS,GAAG,CAAC,IAAI,kBAAQ,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,oBAAoB,EAAE,CAAC;IAErG,IAAI,CAAC,QAAQ,GAAG,IAAI,eAAK,CAAC,QAAQ,CAAC,CAAC;IACpC,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,iBAAO,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC;IAC7C,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,IAAI,CAAC;IAClC,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;IACxC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IACtB,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;IAChC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;IACpC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AACrC,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,gBAAM,EAAE,sCACxC,IAAI,EAAE,OAAO,IAEV,wBAAuB,KAE1B,MAAM,YAAC,OAAO,EAAE,MAAM;QAClB,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QACnD,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QACtC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;IACpD,CAAC,EAED,IAAI,YAAC,OAAO;QACR,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;YACtB,OAAO,CAAC,WAAW,GAAG,EAAE,CAAC;YACzB,OAAO,CAAC,SAAS,GAAG,EAAE,CAAC;SAC1B;QAED,IAAM,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,EAAE,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;QACtF,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;YACzC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;SACpC;QAED,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAE7C,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9B,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEhC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,gBAAgB,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;QAC9E,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,KAAK,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;QAC5C,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;QAEvB,OAAO,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;QAExB,OAAO,OAAO,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;YAC5D,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;IAClC,CAAC,IACH,CAAC;AAEH,kBAAe,KAAK,CAAC", "sourcesContent": ["import Ruleset from './ruleset';\nimport Value from './value';\nimport Selector from './selector';\nimport AtRule from './atrule';\nimport NestableAtRulePrototype from './nested-at-rule';\n\nconst Media = function(value, features, index, currentFileInfo, visibilityInfo) {\n    this._index = index;\n    this._fileInfo = currentFileInfo;\n\n    const selectors = (new Selector([], null, null, this._index, this._fileInfo)).createEmptySelectors();\n\n    this.features = new Value(features);\n    this.rules = [new Ruleset(selectors, value)];\n    this.rules[0].allowImports = true;\n    this.copyVisibilityInfo(visibilityInfo);\n    this.allowRoot = true;\n    this.setParent(selectors, this);\n    this.setParent(this.features, this);\n    this.setParent(this.rules, this);\n};\n\nMedia.prototype = Object.assign(new AtRule(), {\n    type: 'Media',\n\n    ...NestableAtRulePrototype,\n\n    genCSS(context, output) {\n        output.add('@media ', this._fileInfo, this._index);\n        this.features.genCSS(context, output);\n        this.outputRuleset(context, output, this.rules);\n    },\n\n    eval(context) {\n        if (!context.mediaBlocks) {\n            context.mediaBlocks = [];\n            context.mediaPath = [];\n        }\n\n        const media = new Media(null, [], this._index, this._fileInfo, this.visibilityInfo());\n        if (this.debugInfo) {\n            this.rules[0].debugInfo = this.debugInfo;\n            media.debugInfo = this.debugInfo;\n        }\n        \n        media.features = this.features.eval(context);\n\n        context.mediaPath.push(media);\n        context.mediaBlocks.push(media);\n\n        this.rules[0].functionRegistry = context.frames[0].functionRegistry.inherit();\n        context.frames.unshift(this.rules[0]);\n        media.rules = [this.rules[0].eval(context)];\n        context.frames.shift();\n\n        context.mediaPath.pop();\n\n        return context.mediaPath.length === 0 ? media.evalTop(context) :\n            media.evalNested(context);\n    }\n});\n\nexport default Media;\n"]}