{"version": 3, "file": "detached-ruleset.js", "sourceRoot": "", "sources": ["../../../src/less/tree/detached-ruleset.js"], "names": [], "mappings": ";;;AAAA,wDAA0B;AAC1B,iEAAmC;AACnC,sDAAkC;AAElC,IAAM,eAAe,GAAG,UAAS,OAAO,EAAE,MAAM;IAC5C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACvB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACrB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AACvC,CAAC,CAAC;AAEF,eAAe,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,cAAI,EAAE,EAAE;IAClD,IAAI,EAAE,iBAAiB;IACvB,SAAS,EAAE,IAAI;IAEf,MAAM,YAAC,OAAO;QACV,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC/C,CAAC;IAED,IAAI,YAAC,OAAO;QACR,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAC9D,OAAO,IAAI,eAAe,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IACrD,CAAC;IAED,QAAQ,YAAC,OAAO;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,kBAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;IACrH,CAAC;CACJ,CAAC,CAAC;AAEH,kBAAe,eAAe,CAAC", "sourcesContent": ["import Node from './node';\nimport contexts from '../contexts';\nimport * as utils from '../utils';\n\nconst DetachedRuleset = function(ruleset, frames) {\n    this.ruleset = ruleset;\n    this.frames = frames;\n    this.setParent(this.ruleset, this);\n};\n\nDetachedRuleset.prototype = Object.assign(new Node(), {\n    type: 'DetachedRuleset',\n    evalFirst: true,\n\n    accept(visitor) {\n        this.ruleset = visitor.visit(this.ruleset);\n    },\n\n    eval(context) {\n        const frames = this.frames || utils.copyArray(context.frames);\n        return new DetachedRuleset(this.ruleset, frames);\n    },\n\n    callEval(context) {\n        return this.ruleset.eval(this.frames ? new contexts.Eval(context, this.frames.concat(context.frames)) : context);\n    }\n});\n\nexport default DetachedRuleset;\n"]}