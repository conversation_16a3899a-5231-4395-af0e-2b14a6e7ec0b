{"version": 3, "file": "property.js", "sourceRoot": "", "sources": ["../../../src/less/tree/property.js"], "names": [], "mappings": ";;;AAAA,wDAA0B;AAC1B,sEAAwC;AAExC,IAAM,QAAQ,GAAG,UAAS,IAAI,EAAE,KAAK,EAAE,eAAe;IAClD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACjB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACpB,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC;AACrC,CAAC,CAAC;AAEF,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,cAAI,EAAE,EAAE;IAC3C,IAAI,EAAE,UAAU;IAEhB,IAAI,YAAC,OAAO;QACR,IAAI,QAAQ,CAAC;QACb,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,+BAA+B;QAC/B,IAAM,UAAU,GAAG,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,SAAS,CAAC,WAAW,CAAC;QAE1F,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,MAAM,EAAE,IAAI,EAAE,MAAM;gBAChB,OAAO,EAAE,2CAAoC,IAAI,CAAE;gBACnD,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,QAAQ;gBAClC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;SAChC;QAED,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QAEvB,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,UAAU,KAAK;YAChD,IAAI,CAAC,CAAC;YACN,IAAM,IAAI,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAClC,IAAI,IAAI,EAAE;gBACN,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAClC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;oBAEZ,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,qBAAW,CAAC,CAAC,CAAC,IAAI,EAC5B,CAAC,CAAC,KAAK,EACP,CAAC,CAAC,SAAS,EACX,CAAC,CAAC,KAAK,EACP,CAAC,CAAC,KAAK,EACP,CAAC,CAAC,eAAe,EACjB,CAAC,CAAC,MAAM,EACR,CAAC,CAAC,QAAQ,CACb,CAAC;iBACL;gBACD,UAAU,CAAC,IAAI,CAAC,CAAC;gBAEjB,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAC1B,IAAI,CAAC,CAAC,SAAS,EAAE;oBACb,IAAM,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;oBACjF,cAAc,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,CAAC;iBAC1C;gBACD,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC1B,OAAO,CAAC,CAAC;aACZ;QACL,CAAC,CAAC,CAAC;QACH,IAAI,QAAQ,EAAE;YACV,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;YACxB,OAAO,QAAQ,CAAC;SACnB;aAAM;YACH,MAAM,EAAE,IAAI,EAAE,MAAM;gBAChB,OAAO,EAAE,oBAAa,IAAI,mBAAgB;gBAC1C,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,QAAQ;gBACvC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC;SAC3B;IACL,CAAC;IAED,IAAI,YAAC,GAAG,EAAE,GAAG;QACT,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,SAAA,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACpC,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1B,IAAI,CAAC,EAAE;gBAAE,OAAO,CAAC,CAAC;aAAE;SACvB;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ,CAAC,CAAC;AAEH,kBAAe,QAAQ,CAAC", "sourcesContent": ["import Node from './node';\nimport Declaration from './declaration';\n\nconst Property = function(name, index, currentFileInfo) {\n    this.name = name;\n    this._index = index;\n    this._fileInfo = currentFileInfo;\n};\n\nProperty.prototype = Object.assign(new Node(), {\n    type: 'Property',\n\n    eval(context) {\n        let property;\n        const name = this.name;\n        // TODO: shorten this reference\n        const mergeRules = context.pluginManager.less.visitors.ToCSSVisitor.prototype._mergeRules;\n\n        if (this.evaluating) {\n            throw { type: 'Name',\n                message: `Recursive property reference for ${name}`,\n                filename: this.fileInfo().filename,\n                index: this.getIndex() };\n        }\n\n        this.evaluating = true;\n\n        property = this.find(context.frames, function (frame) {\n            let v;\n            const vArr = frame.property(name);\n            if (vArr) {\n                for (let i = 0; i < vArr.length; i++) {\n                    v = vArr[i];\n\n                    vArr[i] = new Declaration(v.name,\n                        v.value,\n                        v.important,\n                        v.merge,\n                        v.index,\n                        v.currentFileInfo,\n                        v.inline,\n                        v.variable\n                    );\n                }\n                mergeRules(vArr);\n\n                v = vArr[vArr.length - 1];\n                if (v.important) {\n                    const importantScope = context.importantScope[context.importantScope.length - 1];\n                    importantScope.important = v.important;\n                }\n                v = v.value.eval(context);\n                return v;\n            }\n        });\n        if (property) {\n            this.evaluating = false;\n            return property;\n        } else {\n            throw { type: 'Name',\n                message: `Property '${name}' is undefined`,\n                filename: this.currentFileInfo.filename,\n                index: this.index };\n        }\n    },\n\n    find(obj, fun) {\n        for (let i = 0, r; i < obj.length; i++) {\n            r = fun.call(obj, obj[i]);\n            if (r) { return r; }\n        }\n        return null;\n    }\n});\n\nexport default Property;\n"]}