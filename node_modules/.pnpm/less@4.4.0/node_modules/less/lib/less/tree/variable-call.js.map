{"version": 3, "file": "variable-call.js", "sourceRoot": "", "sources": ["../../../src/less/tree/variable-call.js"], "names": [], "mappings": ";;;AAAA,wDAA0B;AAC1B,gEAAkC;AAClC,8DAAgC;AAChC,gFAAiD;AACjD,qEAAsC;AAEtC,IAAM,YAAY,GAAG,UAAS,QAAQ,EAAE,KAAK,EAAE,eAAe;IAC1D,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACzB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACpB,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC;IACjC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,CAAC,CAAC;AAEF,YAAY,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,cAAI,EAAE,EAAE;IAC/C,IAAI,EAAE,cAAc;IAEpB,IAAI,YAAC,OAAO;QACR,IAAI,KAAK,CAAC;QACV,IAAI,eAAe,GAAG,IAAI,kBAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAClG,IAAM,KAAK,GAAG,IAAI,oBAAS,CAAC,EAAC,OAAO,EAAE,2CAAoC,IAAI,CAAC,QAAQ,CAAE,EAAC,CAAC,CAAC;QAE5F,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE;YAC1B,IAAI,eAAe,CAAC,KAAK,EAAE;gBACvB,KAAK,GAAG,eAAe,CAAC;aAC3B;iBACI,IAAI,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,EAAE;gBACrC,KAAK,GAAG,IAAI,iBAAO,CAAC,EAAE,EAAE,eAAe,CAAC,CAAC;aAC5C;iBACI,IAAI,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE;gBAC3C,KAAK,GAAG,IAAI,iBAAO,CAAC,EAAE,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC;aAClD;iBACI;gBACD,MAAM,KAAK,CAAC;aACf;YACD,eAAe,GAAG,IAAI,0BAAe,CAAC,KAAK,CAAC,CAAC;SAChD;QAED,IAAI,eAAe,CAAC,OAAO,EAAE;YACzB,OAAO,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;SAC5C;QACD,MAAM,KAAK,CAAC;IAChB,CAAC;CACJ,CAAC,CAAC;AAEH,kBAAe,YAAY,CAAC", "sourcesContent": ["import Node from './node';\nimport Variable from './variable';\nimport Ruleset from './ruleset';\nimport DetachedRuleset from './detached-ruleset';\nimport LessError from '../less-error';\n\nconst VariableCall = function(variable, index, currentFileInfo) {\n    this.variable = variable;\n    this._index = index;\n    this._fileInfo = currentFileInfo;\n    this.allowRoot = true;\n};\n\nVariableCall.prototype = Object.assign(new Node(), {\n    type: 'VariableCall',\n\n    eval(context) {\n        let rules;\n        let detachedRuleset = new Variable(this.variable, this.getIndex(), this.fileInfo()).eval(context);\n        const error = new LessError({message: `Could not evaluate variable call ${this.variable}`});\n\n        if (!detachedRuleset.ruleset) {\n            if (detachedRuleset.rules) {\n                rules = detachedRuleset;\n            }\n            else if (Array.isArray(detachedRuleset)) {\n                rules = new Ruleset('', detachedRuleset);\n            }\n            else if (Array.isArray(detachedRuleset.value)) {\n                rules = new Ruleset('', detachedRuleset.value);\n            }\n            else {\n                throw error;\n            }\n            detachedRuleset = new DetachedRuleset(rules);\n        }\n\n        if (detachedRuleset.ruleset) {\n            return detachedRuleset.callEval(context);\n        }\n        throw error;\n    }\n});\n\nexport default VariableCall;\n"]}