{"version": 3, "file": "value.js", "sourceRoot": "", "sources": ["../../../src/less/tree/value.js"], "names": [], "mappings": ";;;AAAA,wDAA0B;AAE1B,IAAM,KAAK,GAAG,UAAS,KAAK;IACxB,IAAI,CAAC,KAAK,EAAE;QACR,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;KACvD;IACD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;QACvB,IAAI,CAAC,KAAK,GAAG,CAAE,KAAK,CAAE,CAAC;KAC1B;SACI;QACD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;KACtB;AACL,CAAC,CAAC;AAEF,KAAK,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,cAAI,EAAE,EAAE;IACxC,IAAI,EAAE,OAAO;IAEb,MAAM,YAAC,OAAO;QACV,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAC/C;IACL,CAAC;IAED,IAAI,YAAC,OAAO;QACR,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YACzB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SACtC;aAAM;YACH,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC;gBACvC,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC3B,CAAC,CAAC,CAAC,CAAC;SACP;IACL,CAAC;IAED,MAAM,YAAC,OAAO,EAAE,MAAM;QAClB,IAAI,CAAC,CAAC;QACN,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACpC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YACtC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;gBAC3B,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,IAAI,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;aAC1D;SACJ;IACL,CAAC;CACJ,CAAC,CAAC;AAEH,kBAAe,KAAK,CAAC", "sourcesContent": ["import Node from './node';\n\nconst Value = function(value) {\n    if (!value) {\n        throw new Error('Value requires an array argument');\n    }\n    if (!Array.isArray(value)) {\n        this.value = [ value ];\n    }\n    else {\n        this.value = value;\n    }\n};\n\nValue.prototype = Object.assign(new Node(), {\n    type: 'Value',\n\n    accept(visitor) {\n        if (this.value) {\n            this.value = visitor.visitArray(this.value);\n        }\n    },\n\n    eval(context) {\n        if (this.value.length === 1) {\n            return this.value[0].eval(context);\n        } else {\n            return new Value(this.value.map(function (v) {\n                return v.eval(context);\n            }));\n        }\n    },\n\n    genCSS(context, output) {\n        let i;\n        for (i = 0; i < this.value.length; i++) {\n            this.value[i].genCSS(context, output);\n            if (i + 1 < this.value.length) {\n                output.add((context && context.compress) ? ',' : ', ');\n            }\n        }\n    }\n});\n\nexport default Value;\n"]}