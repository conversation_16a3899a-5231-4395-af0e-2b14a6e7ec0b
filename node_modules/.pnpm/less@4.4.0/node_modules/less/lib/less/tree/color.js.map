{"version": 3, "file": "color.js", "sourceRoot": "", "sources": ["../../../src/less/tree/color.js"], "names": [], "mappings": ";;;AAAA,wDAA0B;AAC1B,kEAAoC;AAEpC,EAAE;AACF,6BAA6B;AAC7B,EAAE;AACF,IAAM,KAAK,GAAG,UAAS,GAAG,EAAE,CAAC,EAAE,YAAY;IACvC,IAAM,IAAI,GAAG,IAAI,CAAC;IAClB,EAAE;IACF,+CAA+C;IAC/C,iDAAiD;IACjD,EAAE;IACF,+CAA+C;IAC/C,EAAE;IACF,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QACpB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;KAClB;SAAM,IAAI,GAAG,CAAC,MAAM,IAAI,CAAC,EAAE;QACxB,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;QACd,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC;YACjC,IAAI,CAAC,GAAG,CAAC,EAAE;gBACP,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;aAClC;iBAAM;gBACH,IAAI,CAAC,KAAK,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC;aACxC;QACL,CAAC,CAAC,CAAC;KACN;SAAM;QACH,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;QACd,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC;YAC5B,IAAI,CAAC,GAAG,CAAC,EAAE;gBACP,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;aACtC;iBAAM;gBACH,IAAI,CAAC,KAAK,GAAG,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC;aAC5C;QACL,CAAC,CAAC,CAAC;KACN;IACD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3D,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE;QACrC,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC;KAC7B;AACL,CAAC,CAAA;AAED,KAAK,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,cAAI,EAAE,EAAE;IACxC,IAAI,EAAE,OAAO;IAEb,IAAI;QACA,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;QAExE,CAAC,GAAG,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC;QACtE,CAAC,GAAG,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC;QACtE,CAAC,GAAG,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC;QAEtE,OAAO,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC;IAChD,CAAC;IAED,MAAM,YAAC,OAAO,EAAE,MAAM;QAClB,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;IACpC,CAAC;IAED,KAAK,YAAC,OAAO,EAAE,aAAa;QACxB,IAAM,QAAQ,GAAG,OAAO,IAAI,OAAO,CAAC,QAAQ,IAAI,CAAC,aAAa,CAAC;QAC/D,IAAI,KAAK,CAAC;QACV,IAAI,KAAK,CAAC;QACV,IAAI,aAAa,CAAC;QAClB,IAAI,IAAI,GAAG,EAAE,CAAC;QAEd,8CAA8C;QAC9C,iDAAiD;QACjD,qDAAqD;QACrD,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAEzC,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;gBACjC,IAAI,KAAK,GAAG,CAAC,EAAE;oBACX,aAAa,GAAG,MAAM,CAAC;iBAC1B;aACJ;iBAAM,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;gBACxC,IAAI,KAAK,GAAG,CAAC,EAAE;oBACX,aAAa,GAAG,MAAM,CAAC;iBAC1B;qBAAM;oBACH,aAAa,GAAG,KAAK,CAAC;iBACzB;aACJ;iBAAM;gBACH,OAAO,IAAI,CAAC,KAAK,CAAC;aACrB;SACJ;aAAM;YACH,IAAI,KAAK,GAAG,CAAC,EAAE;gBACX,aAAa,GAAG,MAAM,CAAC;aAC1B;SACJ;QAED,QAAQ,aAAa,EAAE;YACnB,KAAK,MAAM;gBACP,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,UAAU,CAAC;oBAC3B,OAAO,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;gBACrC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;gBAC3B,MAAM;YACV,KAAK,MAAM;gBACP,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;YAC/B,0CAA0C;YAC1C,KAAK,KAAK;gBACN,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;gBACrB,IAAI,GAAG;oBACH,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;oBAC7B,UAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,MAAG;oBACzC,UAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,MAAG;iBAC5C,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;SACtB;QAED,IAAI,aAAa,EAAE;YACf,oEAAoE;YACpE,OAAO,UAAG,aAAa,cAAI,IAAI,CAAC,IAAI,CAAC,WAAI,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAE,CAAC,MAAG,CAAC;SACtE;QAED,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;QAErB,IAAI,QAAQ,EAAE;YACV,IAAM,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YAEnC,gCAAgC;YAChC,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,UAAU,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,UAAU,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,UAAU,CAAC,CAAC,CAAC,EAAE;gBACvG,KAAK,GAAG,WAAI,UAAU,CAAC,CAAC,CAAC,SAAG,UAAU,CAAC,CAAC,CAAC,SAAG,UAAU,CAAC,CAAC,CAAC,CAAE,CAAC;aAC/D;SACJ;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,EAAE;IACF,kDAAkD;IAClD,oDAAoD;IACpD,iDAAiD;IACjD,iDAAiD;IACjD,EAAE;IACF,OAAO,YAAC,OAAO,EAAE,EAAE,EAAE,KAAK;QACtB,IAAM,GAAG,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;QACzB,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC;QAC3D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YACxB,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;SAClE;QACD,OAAO,IAAI,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IACjC,CAAC;IAED,KAAK;QACD,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC3B,CAAC;IAED,KAAK;QACD,IAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;QAE1F,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACvD,IAAI,CAAC,CAAC;QACN,IAAI,CAAC,CAAC;QACN,IAAM,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;QAC1B,IAAM,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;QAEpB,IAAI,GAAG,KAAK,GAAG,EAAE;YACb,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;SACb;aAAM;YACH,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;YAEpD,QAAQ,GAAG,EAAE;gBACT,KAAK,CAAC;oBAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAAC,MAAM;gBACjD,KAAK,CAAC;oBAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;oBAAe,MAAM;gBACjD,KAAK,CAAC;oBAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;oBAAe,MAAM;aACpD;YACD,CAAC,IAAI,CAAC,CAAC;SACV;QACD,OAAO,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAA,EAAE,CAAC,GAAA,EAAE,CAAC,GAAA,EAAE,CAAC;IACnC,CAAC;IAED,uHAAuH;IACvH,KAAK;QACD,IAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;QAE1F,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACvD,IAAI,CAAC,CAAC;QACN,IAAI,CAAC,CAAC;QACN,IAAM,CAAC,GAAG,GAAG,CAAC;QAEd,IAAM,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;QACpB,IAAI,GAAG,KAAK,CAAC,EAAE;YACX,CAAC,GAAG,CAAC,CAAC;SACT;aAAM;YACH,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;SACf;QAED,IAAI,GAAG,KAAK,GAAG,EAAE;YACb,CAAC,GAAG,CAAC,CAAC;SACT;aAAM;YACH,QAAQ,GAAG,EAAE;gBACT,KAAK,CAAC;oBAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAAC,MAAM;gBACjD,KAAK,CAAC;oBAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;oBAAC,MAAM;gBACnC,KAAK,CAAC;oBAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;oBAAC,MAAM;aACtC;YACD,CAAC,IAAI,CAAC,CAAC;SACV;QACD,OAAO,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAA,EAAE,CAAC,GAAA,EAAE,CAAC,GAAA,EAAE,CAAC;IACnC,CAAC;IAED,MAAM;QACF,OAAO,KAAK,CAAC,CAAC,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IACtD,CAAC;IAED,OAAO,YAAC,CAAC;QACL,OAAO,CAAC,CAAC,CAAC,GAAG;YACT,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;YACxB,CAAC,CAAC,KAAK,KAAM,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IACjD,CAAC;CACJ,CAAC,CAAC;AAEH,KAAK,CAAC,WAAW,GAAG,UAAS,OAAO;IAChC,IAAI,CAAC,CAAC;IACN,IAAM,GAAG,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;IAClC,iDAAiD;IACjD,IAAI,gBAAM,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;QAC5B,CAAC,GAAG,IAAI,KAAK,CAAC,gBAAM,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;KACvC;SACI,IAAI,GAAG,KAAK,aAAa,EAAE;QAC5B,CAAC,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;KAC/B;IAED,IAAI,CAAC,EAAE;QACH,CAAC,CAAC,KAAK,GAAG,OAAO,CAAC;QAClB,OAAO,CAAC,CAAC;KACZ;AACL,CAAC,CAAC;AAEF,SAAS,KAAK,CAAC,CAAC,EAAE,GAAG;IACjB,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AACzC,CAAC;AAED,SAAS,KAAK,CAAC,CAAC;IACZ,OAAO,WAAI,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC;QACxB,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QAC9B,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IAChD,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAE,CAAC;AAClB,CAAC;AAED,kBAAe,KAAK,CAAC", "sourcesContent": ["import Node from './node';\nimport colors from '../data/colors';\n\n//\n// RGB Colors - #ff0014, #eee\n//\nconst Color = function(rgb, a, originalForm) {\n    const self = this;\n    //\n    // The end goal here, is to parse the arguments\n    // into an integer triplet, such as `128, 255, 0`\n    //\n    // This facilitates operations and conversions.\n    //\n    if (Array.isArray(rgb)) {\n        this.rgb = rgb;\n    } else if (rgb.length >= 6) {\n        this.rgb = [];\n        rgb.match(/.{2}/g).map(function (c, i) {\n            if (i < 3) {\n                self.rgb.push(parseInt(c, 16));\n            } else {\n                self.alpha = (parseInt(c, 16)) / 255;\n            }\n        });\n    } else {\n        this.rgb = [];\n        rgb.split('').map(function (c, i) {\n            if (i < 3) {\n                self.rgb.push(parseInt(c + c, 16));\n            } else {\n                self.alpha = (parseInt(c + c, 16)) / 255;\n            }\n        });\n    }\n    this.alpha = this.alpha || (typeof a === 'number' ? a : 1);\n    if (typeof originalForm !== 'undefined') {\n        this.value = originalForm;\n    }\n}\n\nColor.prototype = Object.assign(new Node(), {\n    type: 'Color',\n\n    luma() {\n        let r = this.rgb[0] / 255, g = this.rgb[1] / 255, b = this.rgb[2] / 255;\n\n        r = (r <= 0.03928) ? r / 12.92 : Math.pow(((r + 0.055) / 1.055), 2.4);\n        g = (g <= 0.03928) ? g / 12.92 : Math.pow(((g + 0.055) / 1.055), 2.4);\n        b = (b <= 0.03928) ? b / 12.92 : Math.pow(((b + 0.055) / 1.055), 2.4);\n\n        return 0.2126 * r + 0.7152 * g + 0.0722 * b;\n    },\n\n    genCSS(context, output) {\n        output.add(this.toCSS(context));\n    },\n\n    toCSS(context, doNotCompress) {\n        const compress = context && context.compress && !doNotCompress;\n        let color;\n        let alpha;\n        let colorFunction;\n        let args = [];\n\n        // `value` is set if this color was originally\n        // converted from a named color string so we need\n        // to respect this and try to output named color too.\n        alpha = this.fround(context, this.alpha);\n\n        if (this.value) {\n            if (this.value.indexOf('rgb') === 0) {\n                if (alpha < 1) {\n                    colorFunction = 'rgba';\n                }\n            } else if (this.value.indexOf('hsl') === 0) {\n                if (alpha < 1) {\n                    colorFunction = 'hsla';\n                } else {\n                    colorFunction = 'hsl';\n                }\n            } else {\n                return this.value;\n            }\n        } else {\n            if (alpha < 1) {\n                colorFunction = 'rgba';\n            }\n        }\n\n        switch (colorFunction) {\n            case 'rgba':\n                args = this.rgb.map(function (c) {\n                    return clamp(Math.round(c), 255);\n                }).concat(clamp(alpha, 1));\n                break;\n            case 'hsla':\n                args.push(clamp(alpha, 1));\n            // eslint-disable-next-line no-fallthrough\n            case 'hsl':\n                color = this.toHSL();\n                args = [\n                    this.fround(context, color.h),\n                    `${this.fround(context, color.s * 100)}%`,\n                    `${this.fround(context, color.l * 100)}%`\n                ].concat(args);\n        }\n\n        if (colorFunction) {\n            // Values are capped between `0` and `255`, rounded and zero-padded.\n            return `${colorFunction}(${args.join(`,${compress ? '' : ' '}`)})`;\n        }\n\n        color = this.toRGB();\n\n        if (compress) {\n            const splitcolor = color.split('');\n\n            // Convert color to short format\n            if (splitcolor[1] === splitcolor[2] && splitcolor[3] === splitcolor[4] && splitcolor[5] === splitcolor[6]) {\n                color = `#${splitcolor[1]}${splitcolor[3]}${splitcolor[5]}`;\n            }\n        }\n\n        return color;\n    },\n\n    //\n    // Operations have to be done per-channel, if not,\n    // channels will spill onto each other. Once we have\n    // our result, in the form of an integer triplet,\n    // we create a new Color node to hold the result.\n    //\n    operate(context, op, other) {\n        const rgb = new Array(3);\n        const alpha = this.alpha * (1 - other.alpha) + other.alpha;\n        for (let c = 0; c < 3; c++) {\n            rgb[c] = this._operate(context, op, this.rgb[c], other.rgb[c]);\n        }\n        return new Color(rgb, alpha);\n    },\n\n    toRGB() {\n        return toHex(this.rgb);\n    },\n\n    toHSL() {\n        const r = this.rgb[0] / 255, g = this.rgb[1] / 255, b = this.rgb[2] / 255, a = this.alpha;\n\n        const max = Math.max(r, g, b), min = Math.min(r, g, b);\n        let h;\n        let s;\n        const l = (max + min) / 2;\n        const d = max - min;\n\n        if (max === min) {\n            h = s = 0;\n        } else {\n            s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n\n            switch (max) {\n                case r: h = (g - b) / d + (g < b ? 6 : 0); break;\n                case g: h = (b - r) / d + 2;               break;\n                case b: h = (r - g) / d + 4;               break;\n            }\n            h /= 6;\n        }\n        return { h: h * 360, s, l, a };\n    },\n\n    // Adapted from http://mjijackson.com/2008/02/rgb-to-hsl-and-rgb-to-hsv-color-model-conversion-algorithms-in-javascript\n    toHSV() {\n        const r = this.rgb[0] / 255, g = this.rgb[1] / 255, b = this.rgb[2] / 255, a = this.alpha;\n\n        const max = Math.max(r, g, b), min = Math.min(r, g, b);\n        let h;\n        let s;\n        const v = max;\n\n        const d = max - min;\n        if (max === 0) {\n            s = 0;\n        } else {\n            s = d / max;\n        }\n\n        if (max === min) {\n            h = 0;\n        } else {\n            switch (max) {\n                case r: h = (g - b) / d + (g < b ? 6 : 0); break;\n                case g: h = (b - r) / d + 2; break;\n                case b: h = (r - g) / d + 4; break;\n            }\n            h /= 6;\n        }\n        return { h: h * 360, s, v, a };\n    },\n\n    toARGB() {\n        return toHex([this.alpha * 255].concat(this.rgb));\n    },\n\n    compare(x) {\n        return (x.rgb &&\n            x.rgb[0] === this.rgb[0] &&\n            x.rgb[1] === this.rgb[1] &&\n            x.rgb[2] === this.rgb[2] &&\n            x.alpha  === this.alpha) ? 0 : undefined;\n    }\n});\n\nColor.fromKeyword = function(keyword) {\n    let c;\n    const key = keyword.toLowerCase();\n    // eslint-disable-next-line no-prototype-builtins\n    if (colors.hasOwnProperty(key)) {\n        c = new Color(colors[key].slice(1));\n    }\n    else if (key === 'transparent') {\n        c = new Color([0, 0, 0], 0);\n    }\n\n    if (c) {\n        c.value = keyword;\n        return c;\n    }\n};\n\nfunction clamp(v, max) {\n    return Math.min(Math.max(v, 0), max);\n}\n\nfunction toHex(v) {\n    return `#${v.map(function (c) {\n        c = clamp(Math.round(c), 255);\n        return (c < 16 ? '0' : '') + c.toString(16);\n    }).join('')}`;\n}\n\nexport default Color;\n"]}