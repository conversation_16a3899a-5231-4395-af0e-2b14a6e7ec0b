{"version": 3, "file": "mixin-definition.js", "sourceRoot": "", "sources": ["../../../src/less/tree/mixin-definition.js"], "names": [], "mappings": ";;;AAAA,gEAAkC;AAClC,8DAAgC;AAChC,8DAAgC;AAChC,sEAAwC;AACxC,gFAAiD;AACjD,oEAAsC;AACtC,iEAAmC;AACnC,sDAAkC;AAElC,IAAM,UAAU,GAAG,UAAS,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,cAAc;IACxF,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI,iBAAiB,CAAC;IACtC,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI,kBAAQ,CAAC,CAAC,IAAI,iBAAO,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/F,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACrB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC3B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACzB,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC;IAC3B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACnB,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;IACnB,IAAM,kBAAkB,GAAG,EAAE,CAAC;IAC9B,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,UAAU,KAAK,EAAE,CAAC;QAC5C,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;YACjC,OAAO,KAAK,GAAG,CAAC,CAAC;SACpB;aACI;YACD,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YAChC,OAAO,KAAK,CAAC;SAChB;IACL,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;IAC7C,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACrB,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;IACxC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,CAAC,CAAA;AAED,UAAU,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,iBAAO,EAAE,EAAE;IAChD,IAAI,EAAE,iBAAiB;IACvB,SAAS,EAAE,IAAI;IAEf,MAAM,YAAC,OAAO;QACV,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;YACnC,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACjD;QACD,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC5C,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SAClD;IACL,CAAC;IAED,UAAU,YAAC,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,cAAc;QAC9C,sBAAsB;QACtB,IAAM,KAAK,GAAG,IAAI,iBAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAEtC,IAAI,OAAO,CAAC;QACZ,IAAI,GAAG,CAAC;QACR,IAAM,MAAM,GAAG,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC5C,IAAI,CAAC,CAAC;QACN,IAAI,CAAC,CAAC;QACN,IAAI,GAAG,CAAC;QACR,IAAI,IAAI,CAAC;QACT,IAAI,YAAY,CAAC;QACjB,IAAI,QAAQ,CAAC;QACb,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,IAAI,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,gBAAgB,EAAE;YAC9E,KAAK,CAAC,gBAAgB,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;SAC1E;QACD,QAAQ,GAAG,IAAI,kBAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;QAExE,IAAI,IAAI,EAAE;YACN,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YAC7B,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC;YAEzB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;gBAC7B,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gBACd,IAAI,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,EAAE;oBAC1B,YAAY,GAAG,KAAK,CAAC;oBACrB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;wBAChC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;4BAC/C,cAAc,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;4BAC5C,KAAK,CAAC,WAAW,CAAC,IAAI,qBAAW,CAAC,IAAI,EAAE,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;4BAClE,YAAY,GAAG,IAAI,CAAC;4BACpB,MAAM;yBACT;qBACJ;oBACD,IAAI,YAAY,EAAE;wBACd,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;wBAClB,CAAC,EAAE,CAAC;wBACJ,SAAS;qBACZ;yBAAM;wBACH,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,6BAAsB,IAAI,CAAC,IAAI,cAAI,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,eAAY,EAAE,CAAC;qBACnG;iBACJ;aACJ;SACJ;QACD,QAAQ,GAAG,CAAC,CAAC;QACb,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAChC,IAAI,cAAc,CAAC,CAAC,CAAC,EAAE;gBAAE,SAAS;aAAE;YAEpC,GAAG,GAAG,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;YAE7B,IAAI,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;gBACvB,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;oBACpB,OAAO,GAAG,EAAE,CAAC;oBACb,KAAK,CAAC,GAAG,QAAQ,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;wBACpC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;qBAC7C;oBACD,KAAK,CAAC,WAAW,CAAC,IAAI,qBAAW,CAAC,IAAI,EAAE,IAAI,oBAAU,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;iBACnF;qBAAM;oBACH,GAAG,GAAG,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC;oBACvB,IAAI,GAAG,EAAE;wBACL,yEAAyE;wBACzE,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;4BACpB,GAAG,GAAG,IAAI,0BAAe,CAAC,IAAI,iBAAO,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC;yBACnD;6BACI;4BACD,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;yBAC3B;qBACJ;yBAAM,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE;wBACxB,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;wBACrC,KAAK,CAAC,UAAU,EAAE,CAAC;qBACtB;yBAAM;wBACH,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,wCAAiC,IAAI,CAAC,IAAI,eAAK,UAAU,kBAAQ,IAAI,CAAC,KAAK,MAAG,EAAE,CAAC;qBACtH;oBAED,KAAK,CAAC,WAAW,CAAC,IAAI,qBAAW,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;oBAC9C,cAAc,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;iBAC3B;aACJ;YAED,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,IAAI,IAAI,EAAE;gBAC5B,KAAK,CAAC,GAAG,QAAQ,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;oBACpC,cAAc,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;iBACnD;aACJ;YACD,QAAQ,EAAE,CAAC;SACd;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,aAAa;QACT,IAAM,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC;YAC/D,IAAI,CAAC,CAAC,aAAa,EAAE;gBACjB,OAAO,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;aAChC;iBAAM;gBACH,OAAO,CAAC,CAAC;aACZ;QACL,CAAC,CAAC,CAAC;QACH,IAAM,MAAM,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QACzG,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,IAAI,YAAC,OAAO;QACR,OAAO,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;IAC7I,CAAC;IAED,QAAQ,YAAC,OAAO,EAAE,IAAI,EAAE,SAAS;QAC7B,IAAM,UAAU,GAAG,EAAE,CAAC;QACtB,IAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC;QACtF,IAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,kBAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;QAClG,IAAI,KAAK,CAAC;QACV,IAAI,OAAO,CAAC;QAEZ,KAAK,CAAC,WAAW,CAAC,IAAI,qBAAW,CAAC,YAAY,EAAE,IAAI,oBAAU,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAE3F,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEpC,OAAO,GAAG,IAAI,iBAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QACnC,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC;QAC/B,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,kBAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QACtF,IAAI,SAAS,EAAE;YACX,OAAO,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;SACrC;QACD,OAAO,OAAO,CAAC;IACnB,CAAC;IAED,cAAc,YAAC,IAAI,EAAE,OAAO;QACxB,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CACtC,IAAI,kBAAQ,CAAC,IAAI,CAAC,OAAO,EACrB,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,6BAA6B,CACnD,IAAI,kBAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;aACxG,MAAM,CAAC,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC,oCAAoC;aAC9D,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,iCAAiC;YACtE,OAAO,KAAK,CAAC;SAChB;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,SAAS,YAAC,IAAI,EAAE,OAAO;QACnB,IAAM,UAAU,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAC9C,IAAI,GAAG,CAAC;QACR,IAAM,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC;QACnD,IAAM,eAAe,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,KAAK,EAAE,CAAC;YAC9D,IAAI,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;gBACxC,OAAO,KAAK,GAAG,CAAC,CAAC;aACpB;iBAAM;gBACH,OAAO,KAAK,CAAC;aAChB;QACL,CAAC,EAAE,CAAC,CAAC,CAAC;QAEN,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,IAAI,eAAe,GAAG,IAAI,CAAC,QAAQ,EAAE;gBACjC,OAAO,KAAK,CAAC;aAChB;YACD,IAAI,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;gBACjC,OAAO,KAAK,CAAC;aAChB;SACJ;aAAM;YACH,IAAI,eAAe,GAAG,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,EAAE;gBACvC,OAAO,KAAK,CAAC;aAChB;SACJ;QAED,iBAAiB;QACjB,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;QAE5C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YAC1B,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;gBAClD,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;oBACnF,OAAO,KAAK,CAAC;iBAChB;aACJ;SACJ;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ,CAAC,CAAC;AAEH,kBAAe,UAAU,CAAC", "sourcesContent": ["import Selector from './selector';\nimport Element from './element';\nimport Ruleset from './ruleset';\nimport Declaration from './declaration';\nimport DetachedRuleset from './detached-ruleset';\nimport Expression from './expression';\nimport contexts from '../contexts';\nimport * as utils from '../utils';\n\nconst Definition = function(name, params, rules, condition, variadic, frames, visibilityInfo) {\n    this.name = name || 'anonymous mixin';\n    this.selectors = [new Selector([new Element(null, name, false, this._index, this._fileInfo)])];\n    this.params = params;\n    this.condition = condition;\n    this.variadic = variadic;\n    this.arity = params.length;\n    this.rules = rules;\n    this._lookups = {};\n    const optionalParameters = [];\n    this.required = params.reduce(function (count, p) {\n        if (!p.name || (p.name && !p.value)) {\n            return count + 1;\n        }\n        else {\n            optionalParameters.push(p.name);\n            return count;\n        }\n    }, 0);\n    this.optionalParameters = optionalParameters;\n    this.frames = frames;\n    this.copyVisibilityInfo(visibilityInfo);\n    this.allowRoot = true;\n}\n\nDefinition.prototype = Object.assign(new Ruleset(), {\n    type: 'MixinDefinition',\n    evalFirst: true,\n\n    accept(visitor) {\n        if (this.params && this.params.length) {\n            this.params = visitor.visitArray(this.params);\n        }\n        this.rules = visitor.visitArray(this.rules);\n        if (this.condition) {\n            this.condition = visitor.visit(this.condition);\n        }\n    },\n\n    evalParams(context, mixinEnv, args, evaldArguments) {\n        /* jshint boss:true */\n        const frame = new Ruleset(null, null);\n\n        let varargs;\n        let arg;\n        const params = utils.copyArray(this.params);\n        let i;\n        let j;\n        let val;\n        let name;\n        let isNamedFound;\n        let argIndex;\n        let argsLength = 0;\n\n        if (mixinEnv.frames && mixinEnv.frames[0] && mixinEnv.frames[0].functionRegistry) {\n            frame.functionRegistry = mixinEnv.frames[0].functionRegistry.inherit();\n        }\n        mixinEnv = new contexts.Eval(mixinEnv, [frame].concat(mixinEnv.frames));\n\n        if (args) {\n            args = utils.copyArray(args);\n            argsLength = args.length;\n\n            for (i = 0; i < argsLength; i++) {\n                arg = args[i];\n                if (name = (arg && arg.name)) {\n                    isNamedFound = false;\n                    for (j = 0; j < params.length; j++) {\n                        if (!evaldArguments[j] && name === params[j].name) {\n                            evaldArguments[j] = arg.value.eval(context);\n                            frame.prependRule(new Declaration(name, arg.value.eval(context)));\n                            isNamedFound = true;\n                            break;\n                        }\n                    }\n                    if (isNamedFound) {\n                        args.splice(i, 1);\n                        i--;\n                        continue;\n                    } else {\n                        throw { type: 'Runtime', message: `Named argument for ${this.name} ${args[i].name} not found` };\n                    }\n                }\n            }\n        }\n        argIndex = 0;\n        for (i = 0; i < params.length; i++) {\n            if (evaldArguments[i]) { continue; }\n\n            arg = args && args[argIndex];\n\n            if (name = params[i].name) {\n                if (params[i].variadic) {\n                    varargs = [];\n                    for (j = argIndex; j < argsLength; j++) {\n                        varargs.push(args[j].value.eval(context));\n                    }\n                    frame.prependRule(new Declaration(name, new Expression(varargs).eval(context)));\n                } else {\n                    val = arg && arg.value;\n                    if (val) {\n                        // This was a mixin call, pass in a detached ruleset of it's eval'd rules\n                        if (Array.isArray(val)) {\n                            val = new DetachedRuleset(new Ruleset('', val));\n                        }\n                        else {\n                            val = val.eval(context);\n                        }\n                    } else if (params[i].value) {\n                        val = params[i].value.eval(mixinEnv);\n                        frame.resetCache();\n                    } else {\n                        throw { type: 'Runtime', message: `wrong number of arguments for ${this.name} (${argsLength} for ${this.arity})` };\n                    }\n\n                    frame.prependRule(new Declaration(name, val));\n                    evaldArguments[i] = val;\n                }\n            }\n\n            if (params[i].variadic && args) {\n                for (j = argIndex; j < argsLength; j++) {\n                    evaldArguments[j] = args[j].value.eval(context);\n                }\n            }\n            argIndex++;\n        }\n\n        return frame;\n    },\n\n    makeImportant() {\n        const rules = !this.rules ? this.rules : this.rules.map(function (r) {\n            if (r.makeImportant) {\n                return r.makeImportant(true);\n            } else {\n                return r;\n            }\n        });\n        const result = new Definition(this.name, this.params, rules, this.condition, this.variadic, this.frames);\n        return result;\n    },\n\n    eval(context) {\n        return new Definition(this.name, this.params, this.rules, this.condition, this.variadic, this.frames || utils.copyArray(context.frames));\n    },\n\n    evalCall(context, args, important) {\n        const _arguments = [];\n        const mixinFrames = this.frames ? this.frames.concat(context.frames) : context.frames;\n        const frame = this.evalParams(context, new contexts.Eval(context, mixinFrames), args, _arguments);\n        let rules;\n        let ruleset;\n\n        frame.prependRule(new Declaration('@arguments', new Expression(_arguments).eval(context)));\n\n        rules = utils.copyArray(this.rules);\n\n        ruleset = new Ruleset(null, rules);\n        ruleset.originalRuleset = this;\n        ruleset = ruleset.eval(new contexts.Eval(context, [this, frame].concat(mixinFrames)));\n        if (important) {\n            ruleset = ruleset.makeImportant();\n        }\n        return ruleset;\n    },\n\n    matchCondition(args, context) {\n        if (this.condition && !this.condition.eval(\n            new contexts.Eval(context,\n                [this.evalParams(context, /* the parameter variables */\n                    new contexts.Eval(context, this.frames ? this.frames.concat(context.frames) : context.frames), args, [])]\n                    .concat(this.frames || []) // the parent namespace/mixin frames\n                    .concat(context.frames)))) { // the current environment frames\n            return false;\n        }\n        return true;\n    },\n\n    matchArgs(args, context) {\n        const allArgsCnt = (args && args.length) || 0;\n        let len;\n        const optionalParameters = this.optionalParameters;\n        const requiredArgsCnt = !args ? 0 : args.reduce(function (count, p) {\n            if (optionalParameters.indexOf(p.name) < 0) {\n                return count + 1;\n            } else {\n                return count;\n            }\n        }, 0);\n\n        if (!this.variadic) {\n            if (requiredArgsCnt < this.required) {\n                return false;\n            }\n            if (allArgsCnt > this.params.length) {\n                return false;\n            }\n        } else {\n            if (requiredArgsCnt < (this.required - 1)) {\n                return false;\n            }\n        }\n\n        // check patterns\n        len = Math.min(requiredArgsCnt, this.arity);\n\n        for (let i = 0; i < len; i++) {\n            if (!this.params[i].name && !this.params[i].variadic) {\n                if (args[i].value.eval(context).toCSS() != this.params[i].value.eval(context).toCSS()) {\n                    return false;\n                }\n            }\n        }\n        return true;\n    }\n});\n\nexport default Definition;\n"]}