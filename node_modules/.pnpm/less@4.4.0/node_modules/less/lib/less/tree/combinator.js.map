{"version": 3, "file": "combinator.js", "sourceRoot": "", "sources": ["../../../src/less/tree/combinator.js"], "names": [], "mappings": ";;;AAAA,wDAA0B;AAC1B,IAAM,mBAAmB,GAAG;IACxB,EAAE,EAAE,IAAI;IACR,GAAG,EAAE,IAAI;IACT,GAAG,EAAE,IAAI;CACZ,CAAC;AAEF,IAAM,UAAU,GAAG,UAAS,KAAK;IAC7B,IAAI,KAAK,KAAK,GAAG,EAAE;QACf,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC;QACjB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;KACjC;SAAM;QACH,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QACvC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,KAAK,KAAK,EAAE,CAAC;KAC9C;AACL,CAAC,CAAA;AAED,UAAU,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,cAAI,EAAE,EAAE;IAC7C,IAAI,EAAE,YAAY;IAElB,MAAM,YAAC,OAAO,EAAE,MAAM;QAClB,IAAM,YAAY,GAAG,CAAC,OAAO,CAAC,QAAQ,IAAI,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;QACtF,MAAM,CAAC,GAAG,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC,CAAC;IACzD,CAAC;CACJ,CAAC,CAAC;AAEH,kBAAe,UAAU,CAAC", "sourcesContent": ["import Node from './node';\nconst _noSpaceCombinators = {\n    '': true,\n    ' ': true,\n    '|': true\n};\n\nconst Combinator = function(value) {\n    if (value === ' ') {\n        this.value = ' ';\n        this.emptyOrWhitespace = true;\n    } else {\n        this.value = value ? value.trim() : '';\n        this.emptyOrWhitespace = this.value === '';\n    }\n}\n\nCombinator.prototype = Object.assign(new Node(), {\n    type: 'Combinator',\n\n    genCSS(context, output) {\n        const spaceOrEmpty = (context.compress || _noSpaceCombinators[this.value]) ? '' : ' ';\n        output.add(spaceOrEmpty + this.value + spaceOrEmpty);\n    }\n});\n\nexport default Combinator;\n"]}