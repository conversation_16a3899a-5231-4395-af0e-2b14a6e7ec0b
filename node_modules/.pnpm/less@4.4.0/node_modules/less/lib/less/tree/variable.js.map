{"version": 3, "file": "variable.js", "sourceRoot": "", "sources": ["../../../src/less/tree/variable.js"], "names": [], "mappings": ";;;AAAA,wDAA0B;AAC1B,wDAA0B;AAE1B,IAAM,QAAQ,GAAG,UAAS,IAAI,EAAE,KAAK,EAAE,eAAe;IAClD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACjB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACpB,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC;AACrC,CAAC,CAAC;AAEF,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,cAAI,EAAE,EAAE;IAC3C,IAAI,EAAE,UAAU;IAEhB,IAAI,YAAC,OAAO;QACR,IAAI,QAAQ,EAAE,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QAE/B,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YAC1B,IAAI,GAAG,WAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,CAAE,CAAC;SAClG;QAED,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,MAAM,EAAE,IAAI,EAAE,MAAM;gBAChB,OAAO,EAAE,4CAAqC,IAAI,CAAE;gBACpD,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,QAAQ;gBAClC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;SAChC;QAED,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QAEvB,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,UAAU,KAAK;YAChD,IAAM,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAC/B,IAAI,CAAC,EAAE;gBACH,IAAI,CAAC,CAAC,SAAS,EAAE;oBACb,IAAM,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;oBACjF,cAAc,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,CAAC;iBAC1C;gBACD,0EAA0E;gBAC1E,IAAI,OAAO,CAAC,MAAM,EAAE;oBAChB,OAAO,CAAC,IAAI,cAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;iBACvD;qBACI;oBACD,OAAO,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;iBAChC;aACJ;QACL,CAAC,CAAC,CAAC;QACH,IAAI,QAAQ,EAAE;YACV,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;YACxB,OAAO,QAAQ,CAAC;SACnB;aAAM;YACH,MAAM,EAAE,IAAI,EAAE,MAAM;gBAChB,OAAO,EAAE,mBAAY,IAAI,kBAAe;gBACxC,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,QAAQ;gBAClC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;SAChC;IACL,CAAC;IAED,IAAI,YAAC,GAAG,EAAE,GAAG;QACT,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,SAAA,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACpC,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1B,IAAI,CAAC,EAAE;gBAAE,OAAO,CAAC,CAAC;aAAE;SACvB;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ,CAAC,CAAC;AAEH,kBAAe,QAAQ,CAAC", "sourcesContent": ["import Node from './node';\nimport Call from './call';\n\nconst Variable = function(name, index, currentFileInfo) {\n    this.name = name;\n    this._index = index;\n    this._fileInfo = currentFileInfo;\n};\n\nVariable.prototype = Object.assign(new Node(), {\n    type: 'Variable',\n\n    eval(context) {\n        let variable, name = this.name;\n\n        if (name.indexOf('@@') === 0) {\n            name = `@${new Variable(name.slice(1), this.getIndex(), this.fileInfo()).eval(context).value}`;\n        }\n\n        if (this.evaluating) {\n            throw { type: 'Name',\n                message: `Recursive variable definition for ${name}`,\n                filename: this.fileInfo().filename,\n                index: this.getIndex() };\n        }\n\n        this.evaluating = true;\n\n        variable = this.find(context.frames, function (frame) {\n            const v = frame.variable(name);\n            if (v) {\n                if (v.important) {\n                    const importantScope = context.importantScope[context.importantScope.length - 1];\n                    importantScope.important = v.important;\n                }\n                // If in calc, wrap vars in a function call to cascade evaluate args first\n                if (context.inCalc) {\n                    return (new Call('_SELF', [v.value])).eval(context);\n                }\n                else {\n                    return v.value.eval(context);\n                }\n            }\n        });\n        if (variable) {\n            this.evaluating = false;\n            return variable;\n        } else {\n            throw { type: 'Name',\n                message: `variable ${name} is undefined`,\n                filename: this.fileInfo().filename,\n                index: this.getIndex() };\n        }\n    },\n\n    find(obj, fun) {\n        for (let i = 0, r; i < obj.length; i++) {\n            r = fun.call(obj, obj[i]);\n            if (r) { return r; }\n        }\n        return null;\n    }\n});\n\nexport default Variable;\n"]}