{"version": 3, "file": "node.js", "sourceRoot": "", "sources": ["../../../src/less/tree/node.js"], "names": [], "mappings": ";;AAAA;;;;;GAKG;AACH;IACI;QACI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACnB,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;QAClC,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC;QAC7B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;IACvB,CAAC;IAED,sBAAI,iCAAe;aAAnB;YACI,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC3B,CAAC;;;OAAA;IAED,sBAAI,uBAAK;aAAT;YACI,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC3B,CAAC;;;OAAA;IAED,wBAAS,GAAT,UAAU,KAAK,EAAE,MAAM;QACnB,SAAS,GAAG,CAAC,IAAI;YACb,IAAI,IAAI,IAAI,IAAI,YAAY,IAAI,EAAE;gBAC9B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;aACxB;QACL,CAAC;QACD,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACtB,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;SACtB;aACI;YACD,GAAG,CAAC,KAAK,CAAC,CAAC;SACd;IACL,CAAC;IAED,uBAAQ,GAAR;QACI,OAAO,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,CAAC;IACvE,CAAC;IAED,uBAAQ,GAAR;QACI,OAAO,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC;IAC3E,CAAC;IAED,4BAAa,GAAb,cAAkB,OAAO,KAAK,CAAC,CAAC,CAAC;IAEjC,oBAAK,GAAL,UAAM,OAAO;QACT,IAAM,IAAI,GAAG,EAAE,CAAC;QAChB,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;YACjB,qCAAqC;YACrC,0CAA0C;YAC1C,GAAG,EAAE,UAAS,KAAK,EAAE,QAAQ,EAAE,KAAK;gBAChC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrB,CAAC;YACD,OAAO,EAAE;gBACL,OAAO,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC;YAC7B,CAAC;SACJ,CAAC,CAAC;QACH,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACzB,CAAC;IAED,qBAAM,GAAN,UAAO,OAAO,EAAE,MAAM;QAClB,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC3B,CAAC;IAED,qBAAM,GAAN,UAAO,OAAO;QACV,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC3C,CAAC;IAED,mBAAI,GAAJ,cAAS,OAAO,IAAI,CAAC,CAAC,CAAC;IAEvB,uBAAQ,GAAR,UAAS,OAAO,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC;QACtB,QAAQ,EAAE,EAAE;YACR,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YACvB,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YACvB,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YACvB,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;SAC1B;IACL,CAAC;IAED,qBAAM,GAAN,UAAO,OAAO,EAAE,KAAK;QACjB,IAAM,SAAS,GAAG,OAAO,IAAI,OAAO,CAAC,YAAY,CAAC;QAClD,4GAA4G;QAC5G,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IAC5E,CAAC;IAEM,YAAO,GAAd,UAAe,CAAC,EAAE,CAAC;QACf;;;;2EAImE;QAEnE,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC;YACX,uDAAuD;YACvD,yDAAyD;YACzD,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,EAAE;YAClD,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;SACvB;aAAM,IAAI,CAAC,CAAC,OAAO,EAAE;YAClB,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;SACxB;aAAM,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,EAAE;YAC1B,OAAO,SAAS,CAAC;SACpB;QAED,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC;QACZ,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC;QACZ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;YACnB,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;SAClC;QACD,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM,EAAE;YACvB,OAAO,SAAS,CAAC;SACpB;QACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC/B,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;gBAChC,OAAO,SAAS,CAAC;aACpB;SACJ;QACD,OAAO,CAAC,CAAC;IACb,CAAC;IAEM,mBAAc,GAArB,UAAsB,CAAC,EAAE,CAAC;QACtB,OAAO,CAAC,GAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAE,CAAC;gBACV,CAAC,CAAC,CAAC,GAAK,CAAC,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IACvC,CAAC;IAED,yEAAyE;IACzE,+BAAgB,GAAhB;QACI,IAAI,IAAI,CAAC,gBAAgB,KAAK,SAAS,EAAE;YACrC,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;SAC7B;QACD,OAAO,IAAI,CAAC,gBAAgB,KAAK,CAAC,CAAC;IACvC,CAAC;IAED,iCAAkB,GAAlB;QACI,IAAI,IAAI,CAAC,gBAAgB,KAAK,SAAS,EAAE;YACrC,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;SAC7B;QACD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;IACtD,CAAC;IAED,oCAAqB,GAArB;QACI,IAAI,IAAI,CAAC,gBAAgB,KAAK,SAAS,EAAE;YACrC,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;SAC7B;QACD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;IACtD,CAAC;IAED,+EAA+E;IAC/E,sDAAsD;IACtD,+BAAgB,GAAhB;QACI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IAC5B,CAAC;IAED,oFAAoF;IACpF,sDAAsD;IACtD,iCAAkB,GAAlB;QACI,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;IAC7B,CAAC;IAED,iBAAiB;IACjB,uCAAuC;IACvC,kCAAkC;IAClC,qEAAqE;IACrE,wBAAS,GAAT;QACI,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,6BAAc,GAAd;QACI,OAAO;YACH,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,WAAW,EAAE,IAAI,CAAC,WAAW;SAChC,CAAC;IACN,CAAC;IAED,iCAAkB,GAAlB,UAAmB,IAAI;QACnB,IAAI,CAAC,IAAI,EAAE;YACP,OAAO;SACV;QACD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAC9C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;IACxC,CAAC;IACL,WAAC;AAAD,CAAC,AAjLD,IAiLC;AAED,kBAAe,IAAI,CAAC", "sourcesContent": ["/**\n * The reason why Node is a class and other nodes simply do not extend\n * from Node (since we're transpiling) is due to this issue:\n * \n * @see https://github.com/less/less.js/issues/3434\n */\nclass Node {\n    constructor() {\n        this.parent = null;\n        this.visibilityBlocks = undefined;\n        this.nodeVisible = undefined;\n        this.rootNode = null;\n        this.parsed = null;\n    }\n\n    get currentFileInfo() {\n        return this.fileInfo();\n    }\n\n    get index() {\n        return this.getIndex();\n    }\n\n    setParent(nodes, parent) {\n        function set(node) {\n            if (node && node instanceof Node) {\n                node.parent = parent;\n            }\n        }\n        if (Array.isArray(nodes)) {\n            nodes.forEach(set);\n        }\n        else {\n            set(nodes);\n        }\n    }\n\n    getIndex() {\n        return this._index || (this.parent && this.parent.getIndex()) || 0;\n    }\n\n    fileInfo() {\n        return this._fileInfo || (this.parent && this.parent.fileInfo()) || {};\n    }\n\n    isRulesetLike() { return false; }\n\n    toCSS(context) {\n        const strs = [];\n        this.genCSS(context, {\n            // remove when genCSS has JSDoc types\n            // eslint-disable-next-line no-unused-vars\n            add: function(chunk, fileInfo, index) {\n                strs.push(chunk);\n            },\n            isEmpty: function () {\n                return strs.length === 0;\n            }\n        });\n        return strs.join('');\n    }\n\n    genCSS(context, output) {\n        output.add(this.value);\n    }\n\n    accept(visitor) {\n        this.value = visitor.visit(this.value);\n    }\n\n    eval() { return this; }\n\n    _operate(context, op, a, b) {\n        switch (op) {\n            case '+': return a + b;\n            case '-': return a - b;\n            case '*': return a * b;\n            case '/': return a / b;\n        }\n    }\n\n    fround(context, value) {\n        const precision = context && context.numPrecision;\n        // add \"epsilon\" to ensure numbers like 1.000000005 (represented as 1.000000004999...) are properly rounded:\n        return (precision) ? Number((value + 2e-16).toFixed(precision)) : value;\n    }\n\n    static compare(a, b) {\n        /* returns:\n         -1: a < b\n         0: a = b\n         1: a > b\n         and *any* other value for a != b (e.g. undefined, NaN, -2 etc.) */\n\n        if ((a.compare) &&\n            // for \"symmetric results\" force toCSS-based comparison\n            // of Quoted or Anonymous if either value is one of those\n            !(b.type === 'Quoted' || b.type === 'Anonymous')) {\n            return a.compare(b);\n        } else if (b.compare) {\n            return -b.compare(a);\n        } else if (a.type !== b.type) {\n            return undefined;\n        }\n\n        a = a.value;\n        b = b.value;\n        if (!Array.isArray(a)) {\n            return a === b ? 0 : undefined;\n        }\n        if (a.length !== b.length) {\n            return undefined;\n        }\n        for (let i = 0; i < a.length; i++) {\n            if (Node.compare(a[i], b[i]) !== 0) {\n                return undefined;\n            }\n        }\n        return 0;\n    }\n\n    static numericCompare(a, b) {\n        return a  <  b ? -1\n            : a === b ?  0\n                : a  >  b ?  1 : undefined;\n    }\n\n    // Returns true if this node represents root of ast imported by reference\n    blocksVisibility() {\n        if (this.visibilityBlocks === undefined) {\n            this.visibilityBlocks = 0;\n        }\n        return this.visibilityBlocks !== 0;\n    }\n\n    addVisibilityBlock() {\n        if (this.visibilityBlocks === undefined) {\n            this.visibilityBlocks = 0;\n        }\n        this.visibilityBlocks = this.visibilityBlocks + 1;\n    }\n\n    removeVisibilityBlock() {\n        if (this.visibilityBlocks === undefined) {\n            this.visibilityBlocks = 0;\n        }\n        this.visibilityBlocks = this.visibilityBlocks - 1;\n    }\n\n    // Turns on node visibility - if called node will be shown in output regardless\n    // of whether it comes from import by reference or not\n    ensureVisibility() {\n        this.nodeVisible = true;\n    }\n\n    // Turns off node visibility - if called node will NOT be shown in output regardless\n    // of whether it comes from import by reference or not\n    ensureInvisibility() {\n        this.nodeVisible = false;\n    }\n\n    // return values:\n    // false - the node must not be visible\n    // true - the node must be visible\n    // undefined or null - the node has the same visibility as its parent\n    isVisible() {\n        return this.nodeVisible;\n    }\n\n    visibilityInfo() {\n        return {\n            visibilityBlocks: this.visibilityBlocks,\n            nodeVisible: this.nodeVisible\n        };\n    }\n\n    copyVisibilityInfo(info) {\n        if (!info) {\n            return;\n        }\n        this.visibilityBlocks = info.visibilityBlocks;\n        this.nodeVisible = info.nodeVisible;\n    }\n}\n\nexport default Node;\n"]}