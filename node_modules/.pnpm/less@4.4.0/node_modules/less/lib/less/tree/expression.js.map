{"version": 3, "file": "expression.js", "sourceRoot": "", "sources": ["../../../src/less/tree/expression.js"], "names": [], "mappings": ";;;AAAA,wDAA0B;AAC1B,0DAA4B;AAC5B,8DAAgC;AAChC,kEAAoC;AACpC,kEAAoC;AAEpC,IAAM,UAAU,GAAG,UAAS,KAAK,EAAE,SAAS;IACxC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACnB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC3B,IAAI,CAAC,KAAK,EAAE;QACR,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;KAC7D;AACL,CAAC,CAAC;AAEF,UAAU,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,cAAI,EAAE,EAAE;IAC7C,IAAI,EAAE,YAAY;IAElB,MAAM,YAAC,OAAO;QACV,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAChD,CAAC;IAED,IAAI,YAAC,OAAO;QACR,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QACjC,IAAI,WAAW,CAAC;QAChB,IAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;QAClC,IAAM,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC;QAElC,IAAI,WAAW,GAAG,KAAK,CAAC;QACxB,IAAI,aAAa,EAAE;YACf,OAAO,CAAC,aAAa,EAAE,CAAC;SAC3B;QACD,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;YACvB,WAAW,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC;gBACnD,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE;oBACT,OAAO,CAAC,CAAC;iBACZ;gBACD,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC3B,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;SACvB;aAAM,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YAChC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;gBACtE,WAAW,GAAG,IAAI,CAAC;aACtB;YACD,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAC7C;aAAM;YACH,WAAW,GAAG,IAAI,CAAC;SACtB;QACD,IAAI,aAAa,EAAE;YACf,OAAO,CAAC,gBAAgB,EAAE,CAAC;SAC9B;QACD,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,MAAM,IAAI,CAAC,WAAW;eACtD,CAAC,CAAC,CAAC,WAAW,YAAY,mBAAS,CAAC,CAAC,EAAE;YAC1C,WAAW,GAAG,IAAI,eAAK,CAAC,WAAW,CAAC,CAAC;SACxC;QACD,WAAW,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS,IAAI,SAAS,CAAC;QAC3D,OAAO,WAAW,CAAC;IACvB,CAAC;IAED,MAAM,YAAC,OAAO,EAAE,MAAM;QAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACxC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YACtC,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;gBAC9C,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,YAAY,mBAAS,CAAC;oBACtE,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,YAAY,mBAAS,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,KAAK,GAAG,EAAE;oBAC3E,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;iBACnB;aACJ;SACJ;IACL,CAAC;IAED,iBAAiB;QACb,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,UAAS,CAAC;YACrC,OAAO,CAAC,CAAC,CAAC,YAAY,iBAAO,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;IACP,CAAC;CACJ,CAAC,CAAC;AAEH,kBAAe,UAAU,CAAC", "sourcesContent": ["import Node from './node';\nimport Paren from './paren';\nimport Comment from './comment';\nimport Dimension from './dimension';\nimport Anonymous from './anonymous';\n\nconst Expression = function(value, noSpacing) {\n    this.value = value;\n    this.noSpacing = noSpacing;\n    if (!value) {\n        throw new Error('Expression requires an array parameter');\n    }\n};\n\nExpression.prototype = Object.assign(new Node(), {\n    type: 'Expression',\n\n    accept(visitor) {\n        this.value = visitor.visitArray(this.value);\n    },\n\n    eval(context) {\n        const noSpacing = this.noSpacing;\n        let returnValue;\n        const mathOn = context.isMathOn();\n        const inParenthesis = this.parens;\n\n        let doubleParen = false;\n        if (inParenthesis) {\n            context.inParenthesis();\n        }\n        if (this.value.length > 1) {\n            returnValue = new Expression(this.value.map(function (e) {\n                if (!e.eval) {\n                    return e;\n                }\n                return e.eval(context);\n            }), this.noSpacing);\n        } else if (this.value.length === 1) {\n            if (this.value[0].parens && !this.value[0].parensInOp && !context.inCalc) {\n                doubleParen = true;\n            }\n            returnValue = this.value[0].eval(context);\n        } else {\n            returnValue = this;\n        }\n        if (inParenthesis) {\n            context.outOfParenthesis();\n        }\n        if (this.parens && this.parensInOp && !mathOn && !doubleParen\n            && (!(returnValue instanceof Dimension))) {\n            returnValue = new Paren(returnValue);\n        }\n        returnValue.noSpacing = returnValue.noSpacing || noSpacing;\n        return returnValue;\n    },\n\n    genCSS(context, output) {\n        for (let i = 0; i < this.value.length; i++) {\n            this.value[i].genCSS(context, output);\n            if (!this.noSpacing && i + 1 < this.value.length) {\n                if (i + 1 < this.value.length && !(this.value[i + 1] instanceof Anonymous) ||\n                    this.value[i + 1] instanceof Anonymous && this.value[i + 1].value !== ',') {\n                    output.add(' ');\n                }\n            }\n        }\n    },\n\n    throwAwayComments() {\n        this.value = this.value.filter(function(v) {\n            return !(v instanceof Comment);\n        });\n    }\n});\n\nexport default Expression;\n"]}