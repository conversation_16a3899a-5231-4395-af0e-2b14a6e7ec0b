{"version": 3, "file": "condition.js", "sourceRoot": "", "sources": ["../../../src/less/tree/condition.js"], "names": [], "mappings": ";;;AAAA,wDAA0B;AAE1B,IAAM,SAAS,GAAG,UAAS,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM;IAC1C,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC;IACpB,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;IAChB,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;IAChB,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;IAChB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,CAAC,CAAC;AAEF,SAAS,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,cAAI,EAAE,EAAE;IAC5C,IAAI,EAAE,WAAW;IAEjB,MAAM,YAAC,OAAO;QACV,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACzC,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC7C,CAAC;IAED,IAAI,YAAC,OAAO;QACR,IAAM,MAAM,GAAG,CAAC,UAAU,EAAE,EAAE,CAAC,EAAE,CAAC;YAC9B,QAAQ,EAAE,EAAE;gBACR,KAAK,KAAK,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBAC1B,KAAK,IAAI,CAAC,CAAE,OAAO,CAAC,IAAI,CAAC,CAAC;gBAC1B;oBACI,QAAQ,cAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;wBACxB,KAAK,CAAC,CAAC;4BACH,OAAO,EAAE,KAAK,GAAG,IAAI,EAAE,KAAK,IAAI,IAAI,EAAE,KAAK,IAAI,CAAC;wBACpD,KAAK,CAAC;4BACF,OAAO,EAAE,KAAK,GAAG,IAAI,EAAE,KAAK,IAAI,IAAI,EAAE,KAAK,IAAI,IAAI,EAAE,KAAK,IAAI,CAAC;wBACnE,KAAK,CAAC;4BACF,OAAO,EAAE,KAAK,GAAG,IAAI,EAAE,KAAK,IAAI,CAAC;wBACrC;4BACI,OAAO,KAAK,CAAC;qBACpB;aACR;QACL,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;QAElE,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC;IAC1C,CAAC;CACJ,CAAC,CAAC;AAEH,kBAAe,SAAS,CAAC", "sourcesContent": ["import Node from './node';\n\nconst Condition = function(op, l, r, i, negate) {\n    this.op = op.trim();\n    this.lvalue = l;\n    this.rvalue = r;\n    this._index = i;\n    this.negate = negate;\n};\n\nCondition.prototype = Object.assign(new Node(), {\n    type: 'Condition',\n\n    accept(visitor) {\n        this.lvalue = visitor.visit(this.lvalue);\n        this.rvalue = visitor.visit(this.rvalue);\n    },\n\n    eval(context) {\n        const result = (function (op, a, b) {\n            switch (op) {\n                case 'and': return a && b;\n                case 'or':  return a || b;\n                default:\n                    switch (Node.compare(a, b)) {\n                        case -1:\n                            return op === '<' || op === '=<' || op === '<=';\n                        case 0:\n                            return op === '=' || op === '>=' || op === '=<' || op === '<=';\n                        case 1:\n                            return op === '>' || op === '>=';\n                        default:\n                            return false;\n                    }\n            }\n        })(this.op, this.lvalue.eval(context), this.rvalue.eval(context));\n\n        return this.negate ? !result : result;\n    }\n});\n\nexport default Condition;\n"]}