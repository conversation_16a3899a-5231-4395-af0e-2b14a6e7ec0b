"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isNullOrUndefined = exports.flattenArray = exports.merge = exports.copyOptions = exports.defaults = exports.clone = exports.copyArray = exports.getLocation = void 0;
var tslib_1 = require("tslib");
/* jshint proto: true */
var Constants = tslib_1.__importStar(require("./constants"));
var copy_anything_1 = require("copy-anything");
function getLocation(index, inputStream) {
    var n = index + 1;
    var line = null;
    var column = -1;
    while (--n >= 0 && inputStream.charAt(n) !== '\n') {
        column++;
    }
    if (typeof index === 'number') {
        line = (inputStream.slice(0, index).match(/\n/g) || '').length;
    }
    return {
        line: line,
        column: column
    };
}
exports.getLocation = getLocation;
function copyArray(arr) {
    var i;
    var length = arr.length;
    var copy = new Array(length);
    for (i = 0; i < length; i++) {
        copy[i] = arr[i];
    }
    return copy;
}
exports.copyArray = copyArray;
function clone(obj) {
    var cloned = {};
    for (var prop in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, prop)) {
            cloned[prop] = obj[prop];
        }
    }
    return cloned;
}
exports.clone = clone;
function defaults(obj1, obj2) {
    var newObj = obj2 || {};
    if (!obj2._defaults) {
        newObj = {};
        var defaults_1 = (0, copy_anything_1.copy)(obj1);
        newObj._defaults = defaults_1;
        var cloned = obj2 ? (0, copy_anything_1.copy)(obj2) : {};
        Object.assign(newObj, defaults_1, cloned);
    }
    return newObj;
}
exports.defaults = defaults;
function copyOptions(obj1, obj2) {
    if (obj2 && obj2._defaults) {
        return obj2;
    }
    var opts = defaults(obj1, obj2);
    if (opts.strictMath) {
        opts.math = Constants.Math.PARENS;
    }
    // Back compat with changed relativeUrls option
    if (opts.relativeUrls) {
        opts.rewriteUrls = Constants.RewriteUrls.ALL;
    }
    if (typeof opts.math === 'string') {
        switch (opts.math.toLowerCase()) {
            case 'always':
                opts.math = Constants.Math.ALWAYS;
                break;
            case 'parens-division':
                opts.math = Constants.Math.PARENS_DIVISION;
                break;
            case 'strict':
            case 'parens':
                opts.math = Constants.Math.PARENS;
                break;
            default:
                opts.math = Constants.Math.PARENS;
        }
    }
    if (typeof opts.rewriteUrls === 'string') {
        switch (opts.rewriteUrls.toLowerCase()) {
            case 'off':
                opts.rewriteUrls = Constants.RewriteUrls.OFF;
                break;
            case 'local':
                opts.rewriteUrls = Constants.RewriteUrls.LOCAL;
                break;
            case 'all':
                opts.rewriteUrls = Constants.RewriteUrls.ALL;
                break;
        }
    }
    return opts;
}
exports.copyOptions = copyOptions;
function merge(obj1, obj2) {
    for (var prop in obj2) {
        if (Object.prototype.hasOwnProperty.call(obj2, prop)) {
            obj1[prop] = obj2[prop];
        }
    }
    return obj1;
}
exports.merge = merge;
function flattenArray(arr, result) {
    if (result === void 0) { result = []; }
    for (var i = 0, length_1 = arr.length; i < length_1; i++) {
        var value = arr[i];
        if (Array.isArray(value)) {
            flattenArray(value, result);
        }
        else {
            if (value !== undefined) {
                result.push(value);
            }
        }
    }
    return result;
}
exports.flattenArray = flattenArray;
function isNullOrUndefined(val) {
    return val === null || val === undefined;
}
exports.isNullOrUndefined = isNullOrUndefined;
//# sourceMappingURL=utils.js.map