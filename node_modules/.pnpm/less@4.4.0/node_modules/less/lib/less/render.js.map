{"version": 3, "file": "render.js", "sourceRoot": "", "sources": ["../../src/less/render.js"], "names": [], "mappings": ";;;AAAA,qDAAiC;AAEjC,mBAAwB,WAAW,EAAE,SAAS;IAC1C,IAAM,MAAM,GAAG,UAAU,KAAK,EAAE,OAAO,EAAE,QAAQ;QAC7C,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;YAC/B,QAAQ,GAAG,OAAO,CAAC;YACnB,OAAO,GAAG,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;SACjD;aACI;YACD,OAAO,GAAG,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI,EAAE,CAAC,CAAC;SAC5D;QAED,IAAI,CAAC,QAAQ,EAAE;YACX,IAAM,MAAI,GAAG,IAAI,CAAC;YAClB,OAAO,IAAI,OAAO,CAAC,UAAU,OAAO,EAAE,MAAM;gBACxC,MAAM,CAAC,IAAI,CAAC,MAAI,EAAE,KAAK,EAAE,OAAO,EAAE,UAAS,GAAG,EAAE,MAAM;oBAClD,IAAI,GAAG,EAAE;wBACL,MAAM,CAAC,GAAG,CAAC,CAAC;qBACf;yBAAM;wBACH,OAAO,CAAC,MAAM,CAAC,CAAC;qBACnB;gBACL,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC;SACN;aAAM;YACH,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,OAAO,EAAE,UAAS,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO;gBAC3D,IAAI,GAAG,EAAE;oBAAE,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC;iBAAE;gBAElC,IAAI,MAAM,CAAC;gBACX,IAAI;oBACA,IAAM,SAAS,GAAG,IAAI,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;oBAC/C,MAAM,GAAG,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;iBACrC;gBACD,OAAO,GAAG,EAAE;oBAAE,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC;iBAAE;gBAErC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;YAC3B,CAAC,CAAC,CAAC;SACN;IACL,CAAC,CAAC;IAEF,OAAO,MAAM,CAAC;AAClB,CAAC;AAtCD,4BAsCC", "sourcesContent": ["import * as utils from './utils';\n\nexport default function(environment, ParseTree) {\n    const render = function (input, options, callback) {\n        if (typeof options === 'function') {\n            callback = options;\n            options = utils.copyOptions(this.options, {});\n        }\n        else {\n            options = utils.copyOptions(this.options, options || {});\n        }\n\n        if (!callback) {\n            const self = this;\n            return new Promise(function (resolve, reject) {\n                render.call(self, input, options, function(err, output) {\n                    if (err) {\n                        reject(err);\n                    } else {\n                        resolve(output);\n                    }\n                });\n            });\n        } else {\n            this.parse(input, options, function(err, root, imports, options) {\n                if (err) { return callback(err); }\n\n                let result;\n                try {\n                    const parseTree = new ParseTree(root, imports);\n                    result = parseTree.toCSS(options);\n                }\n                catch (err) { return callback(err); }\n\n                callback(null, result);\n            });\n        }\n    };\n\n    return render;\n}\n"]}