{"version": 3, "file": "import-visitor.js", "sourceRoot": "", "sources": ["../../../src/less/visitors/import-visitor.js"], "names": [], "mappings": ";;;AAAA,mCAAmC;AACnC;;GAEG;AACH,iEAAmC;AACnC,8DAAgC;AAChC,gFAAiD;AACjD,sDAAkC;AAElC,IAAM,aAAa,GAAG,UAAS,QAAQ,EAAE,MAAM;IAE3C,IAAI,CAAC,QAAQ,GAAG,IAAI,iBAAO,CAAC,IAAI,CAAC,CAAC;IAClC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC1B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IACtB,IAAI,CAAC,OAAO,GAAG,IAAI,kBAAQ,CAAC,IAAI,EAAE,CAAC;IACnC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;IACrB,IAAI,CAAC,oBAAoB,GAAG,EAAE,CAAC;IAC/B,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;IAC5B,IAAI,CAAC,UAAU,GAAG,IAAI,0BAAe,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAC7E,CAAC,CAAC;AAEF,aAAa,CAAC,SAAS,GAAG;IACtB,WAAW,EAAE,KAAK;IAClB,GAAG,EAAE,UAAU,IAAI;QACf,IAAI;YACA,uBAAuB;YACvB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;SAC7B;QACD,OAAO,CAAC,EAAE;YACN,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;SAClB;QAED,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;IAC7B,CAAC;IACD,iBAAiB,EAAE;QACf,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,OAAO;SACV;QACD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IACD,WAAW,EAAE,UAAU,UAAU,EAAE,SAAS;QACxC,IAAM,SAAS,GAAG,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC;QAE5C,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,SAAS,EAAE;YAE9B,IAAM,OAAO,GAAG,IAAI,kBAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;YACtF,IAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAEvC,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,IAAI,UAAU,CAAC,gBAAgB,EAAE,EAAE;gBAC/B,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC,CAAC;aAC3G;iBAAM;gBACH,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;aAC7D;SACJ;QACD,SAAS,CAAC,WAAW,GAAG,KAAK,CAAC;IAClC,CAAC;IACD,iBAAiB,EAAE,UAAS,UAAU,EAAE,OAAO,EAAE,YAAY;QACzD,IAAI,eAAe,CAAC;QACpB,IAAM,SAAS,GAAG,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC;QAE5C,IAAI;YACA,eAAe,GAAG,UAAU,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;SACvD;QAAC,OAAO,CAAC,EAAE;YACR,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE;gBAAE,CAAC,CAAC,KAAK,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC;gBAAC,CAAC,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;aAAE;YAClG,4CAA4C;YAC5C,UAAU,CAAC,GAAG,GAAG,IAAI,CAAC;YACtB,2CAA2C;YAC3C,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC;SACxB;QAED,IAAI,eAAe,IAAI,CAAC,CAAC,eAAe,CAAC,GAAG,IAAI,SAAS,CAAC,EAAE;YAExD,IAAI,eAAe,CAAC,OAAO,CAAC,QAAQ,EAAE;gBAClC,OAAO,CAAC,cAAc,GAAG,IAAI,CAAC;aACjC;YAED,6DAA6D;YAC7D,IAAM,sBAAsB,GAAG,eAAe,CAAC,GAAG,KAAK,SAAS,CAAC;YAEjE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAChD,IAAI,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,UAAU,EAAE;oBACtC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC;oBACxC,MAAM;iBACT;aACJ;YAED,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,eAAe,EAAE,OAAO,CAAC,EAAE,mBAAmB,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;YAErI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,EAAE,sBAAsB,EAAE,eAAe,CAAC,QAAQ,EAAE,EAC7F,eAAe,CAAC,OAAO,EAAE,mBAAmB,CAAC,CAAC;SACrD;aAAM;YACH,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,IAAI,IAAI,CAAC,UAAU,EAAE;gBACjB,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;aAC5B;SACJ;IACL,CAAC;IACD,UAAU,EAAE,UAAU,UAAU,EAAE,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,cAAc,EAAE,QAAQ;QACxE,IAAI,CAAC,EAAE;YACH,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE;gBACb,CAAC,CAAC,KAAK,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC;gBAAC,CAAC,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC;aAChF;YACD,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;SAClB;QAED,IAAM,aAAa,GAAG,IAAI,EACtB,SAAS,GAAG,UAAU,CAAC,OAAO,CAAC,MAAM,EACrC,QAAQ,GAAG,UAAU,CAAC,OAAO,CAAC,QAAQ,EACtC,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,QAAQ,EACxC,eAAe,GAAG,cAAc,IAAI,QAAQ,IAAI,aAAa,CAAC,iBAAiB,CAAC;QAEpF,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;YACzB,IAAI,eAAe,EAAE;gBACjB,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC;aAC1B;iBAAM;gBACH,UAAU,CAAC,IAAI,GAAG;oBACd,IAAI,QAAQ,IAAI,aAAa,CAAC,oBAAoB,EAAE;wBAChD,OAAO,IAAI,CAAC;qBACf;oBACD,aAAa,CAAC,oBAAoB,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC;oBACpD,OAAO,KAAK,CAAC;gBACjB,CAAC,CAAC;aACL;SACJ;QAED,IAAI,CAAC,QAAQ,IAAI,UAAU,EAAE;YACzB,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC;SAC1B;QAED,IAAI,IAAI,EAAE;YACN,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC;YACvB,UAAU,CAAC,gBAAgB,GAAG,QAAQ,CAAC;YAEvC,IAAI,CAAC,SAAS,IAAI,CAAC,QAAQ,IAAI,CAAC,OAAO,CAAC,cAAc,IAAI,CAAC,eAAe,CAAC,EAAE;gBACzE,aAAa,CAAC,iBAAiB,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC;gBAEjD,IAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC;gBAChC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;gBACvB,IAAI;oBACA,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;iBAC7B;gBAAC,OAAO,CAAC,EAAE;oBACR,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;iBAClB;gBACD,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC;aAC7B;SACJ;QAED,aAAa,CAAC,WAAW,EAAE,CAAC;QAE5B,IAAI,aAAa,CAAC,UAAU,EAAE;YAC1B,aAAa,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;SACrC;IACL,CAAC;IACD,gBAAgB,EAAE,UAAU,QAAQ,EAAE,SAAS;QAC3C,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK,iBAAiB,EAAE;YAC3C,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;SACzC;aAAM;YACH,SAAS,CAAC,WAAW,GAAG,KAAK,CAAC;SACjC;IACL,CAAC;IACD,mBAAmB,EAAE,UAAS,QAAQ;QAClC,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK,iBAAiB,EAAE;YAC3C,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;SAC/B;IACL,CAAC;IACD,WAAW,EAAE,UAAU,UAAU,EAAE,SAAS;QACxC,IAAI,UAAU,CAAC,KAAK,EAAE;YAClB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;SAC3C;aAAM,IAAI,UAAU,CAAC,YAAY,IAAI,UAAU,CAAC,YAAY,CAAC,MAAM,EAAE;YAClE,IAAI,UAAU,CAAC,QAAQ,EAAE;gBACrB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;aAC3C;iBAAM;gBACH,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;aAC3D;SACJ;aAAM,IAAI,UAAU,CAAC,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC,MAAM,EAAE;YACpD,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;SAC3C;IACL,CAAC;IACD,cAAc,EAAE,UAAU,UAAU;QAChC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;IAChC,CAAC;IACD,oBAAoB,EAAE,UAAU,mBAAmB,EAAE,SAAS;QAC1D,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;IACrD,CAAC;IACD,uBAAuB,EAAE,UAAU,mBAAmB;QAClD,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;IAChC,CAAC;IACD,YAAY,EAAE,UAAU,WAAW,EAAE,SAAS;QAC1C,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;IAC7C,CAAC;IACD,eAAe,EAAE,UAAU,WAAW;QAClC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;IAChC,CAAC;IACD,UAAU,EAAE,UAAU,SAAS,EAAE,SAAS;QACtC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACpD,CAAC;IACD,aAAa,EAAE,UAAU,SAAS;QAC9B,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;IAChC,CAAC;CACJ,CAAC;AACF,kBAAe,aAAa,CAAC", "sourcesContent": ["/* eslint-disable no-unused-vars */\n/**\n * @todo - Remove unused when JSDoc types are added for visitor methods\n */\nimport contexts from '../contexts';\nimport Visitor from './visitor';\nimport ImportSequencer from './import-sequencer';\nimport * as utils from '../utils';\n\nconst ImportVisitor = function(importer, finish) {\n\n    this._visitor = new Visitor(this);\n    this._importer = importer;\n    this._finish = finish;\n    this.context = new contexts.Eval();\n    this.importCount = 0;\n    this.onceFileDetectionMap = {};\n    this.recursionDetector = {};\n    this._sequencer = new ImportSequencer(this._onSequencerEmpty.bind(this));\n};\n\nImportVisitor.prototype = {\n    isReplacing: false,\n    run: function (root) {\n        try {\n            // process the contents\n            this._visitor.visit(root);\n        }\n        catch (e) {\n            this.error = e;\n        }\n\n        this.isFinished = true;\n        this._sequencer.tryRun();\n    },\n    _onSequencerEmpty: function() {\n        if (!this.isFinished) {\n            return;\n        }\n        this._finish(this.error);\n    },\n    visitImport: function (importNode, visitArgs) {\n        const inlineCSS = importNode.options.inline;\n\n        if (!importNode.css || inlineCSS) {\n\n            const context = new contexts.Eval(this.context, utils.copyArray(this.context.frames));\n            const importParent = context.frames[0];\n\n            this.importCount++;\n            if (importNode.isVariableImport()) {\n                this._sequencer.addVariableImport(this.processImportNode.bind(this, importNode, context, importParent));\n            } else {\n                this.processImportNode(importNode, context, importParent);\n            }\n        }\n        visitArgs.visitDeeper = false;\n    },\n    processImportNode: function(importNode, context, importParent) {\n        let evaldImportNode;\n        const inlineCSS = importNode.options.inline;\n\n        try {\n            evaldImportNode = importNode.evalForImport(context);\n        } catch (e) {\n            if (!e.filename) { e.index = importNode.getIndex(); e.filename = importNode.fileInfo().filename; }\n            // attempt to eval properly and treat as css\n            importNode.css = true;\n            // if that fails, this error will be thrown\n            importNode.error = e;\n        }\n\n        if (evaldImportNode && (!evaldImportNode.css || inlineCSS)) {\n\n            if (evaldImportNode.options.multiple) {\n                context.importMultiple = true;\n            }\n\n            // try appending if we haven't determined if it is css or not\n            const tryAppendLessExtension = evaldImportNode.css === undefined;\n\n            for (let i = 0; i < importParent.rules.length; i++) {\n                if (importParent.rules[i] === importNode) {\n                    importParent.rules[i] = evaldImportNode;\n                    break;\n                }\n            }\n\n            const onImported = this.onImported.bind(this, evaldImportNode, context), sequencedOnImported = this._sequencer.addImport(onImported);\n\n            this._importer.push(evaldImportNode.getPath(), tryAppendLessExtension, evaldImportNode.fileInfo(),\n                evaldImportNode.options, sequencedOnImported);\n        } else {\n            this.importCount--;\n            if (this.isFinished) {\n                this._sequencer.tryRun();\n            }\n        }\n    },\n    onImported: function (importNode, context, e, root, importedAtRoot, fullPath) {\n        if (e) {\n            if (!e.filename) {\n                e.index = importNode.getIndex(); e.filename = importNode.fileInfo().filename;\n            }\n            this.error = e;\n        }\n\n        const importVisitor = this,\n            inlineCSS = importNode.options.inline,\n            isPlugin = importNode.options.isPlugin,\n            isOptional = importNode.options.optional,\n            duplicateImport = importedAtRoot || fullPath in importVisitor.recursionDetector;\n\n        if (!context.importMultiple) {\n            if (duplicateImport) {\n                importNode.skip = true;\n            } else {\n                importNode.skip = function() {\n                    if (fullPath in importVisitor.onceFileDetectionMap) {\n                        return true;\n                    }\n                    importVisitor.onceFileDetectionMap[fullPath] = true;\n                    return false;\n                };\n            }\n        }\n\n        if (!fullPath && isOptional) {\n            importNode.skip = true;\n        }\n\n        if (root) {\n            importNode.root = root;\n            importNode.importedFilename = fullPath;\n\n            if (!inlineCSS && !isPlugin && (context.importMultiple || !duplicateImport)) {\n                importVisitor.recursionDetector[fullPath] = true;\n\n                const oldContext = this.context;\n                this.context = context;\n                try {\n                    this._visitor.visit(root);\n                } catch (e) {\n                    this.error = e;\n                }\n                this.context = oldContext;\n            }\n        }\n\n        importVisitor.importCount--;\n\n        if (importVisitor.isFinished) {\n            importVisitor._sequencer.tryRun();\n        }\n    },\n    visitDeclaration: function (declNode, visitArgs) {\n        if (declNode.value.type === 'DetachedRuleset') {\n            this.context.frames.unshift(declNode);\n        } else {\n            visitArgs.visitDeeper = false;\n        }\n    },\n    visitDeclarationOut: function(declNode) {\n        if (declNode.value.type === 'DetachedRuleset') {\n            this.context.frames.shift();\n        }\n    },\n    visitAtRule: function (atRuleNode, visitArgs) {\n        if (atRuleNode.value) {\n            this.context.frames.unshift(atRuleNode);\n        } else if (atRuleNode.declarations && atRuleNode.declarations.length) {\n            if (atRuleNode.isRooted) {\n                this.context.frames.unshift(atRuleNode);\n            } else {\n                this.context.frames.unshift(atRuleNode.declarations[0]);\n            }\n        } else if (atRuleNode.rules && atRuleNode.rules.length) {\n            this.context.frames.unshift(atRuleNode);\n        }\n    },\n    visitAtRuleOut: function (atRuleNode) {\n        this.context.frames.shift();\n    },\n    visitMixinDefinition: function (mixinDefinitionNode, visitArgs) {\n        this.context.frames.unshift(mixinDefinitionNode);\n    },\n    visitMixinDefinitionOut: function (mixinDefinitionNode) {\n        this.context.frames.shift();\n    },\n    visitRuleset: function (rulesetNode, visitArgs) {\n        this.context.frames.unshift(rulesetNode);\n    },\n    visitRulesetOut: function (rulesetNode) {\n        this.context.frames.shift();\n    },\n    visitMedia: function (mediaNode, visitArgs) {\n        this.context.frames.unshift(mediaNode.rules[0]);\n    },\n    visitMediaOut: function (mediaNode) {\n        this.context.frames.shift();\n    }\n};\nexport default ImportVisitor;\n"]}