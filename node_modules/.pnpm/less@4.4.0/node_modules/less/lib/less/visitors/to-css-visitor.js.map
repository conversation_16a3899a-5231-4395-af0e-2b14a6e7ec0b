{"version": 3, "file": "to-css-visitor.js", "sourceRoot": "", "sources": ["../../../src/less/visitors/to-css-visitor.js"], "names": [], "mappings": ";;;AAAA,mCAAmC;AACnC;;GAEG;AACH,yDAA2B;AAC3B,8DAAgC;AAEhC;IACI,yBAAY,OAAO;QACf,IAAI,CAAC,QAAQ,GAAG,IAAI,iBAAO,CAAC,IAAI,CAAC,CAAC;QAClC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;IAC5B,CAAC;IAED,uDAA6B,GAA7B,UAA8B,SAAS;QACnC,IAAI,IAAI,CAAC;QACT,IAAI,CAAC,SAAS,EAAE;YACZ,OAAO,KAAK,CAAC;SAChB;QACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACvC,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YACpB,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE;gBAC3E,uEAAuE;gBACvE,+CAA+C;gBAC/C,OAAO,IAAI,CAAC;aACf;SACJ;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED,+CAAqB,GAArB,UAAsB,KAAK;QACvB,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE;YACtB,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,UAAA,KAAK,IAAI,OAAA,KAAK,CAAC,SAAS,EAAE,EAAjB,CAAiB,CAAC,CAAC;SAChE;IACL,CAAC;IAED,iCAAO,GAAP,UAAQ,KAAK;QACT,OAAO,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC;YACzB,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC5C,CAAC;IAED,4CAAkB,GAAlB,UAAmB,WAAW;QAC1B,OAAO,CAAC,WAAW,IAAI,WAAW,CAAC,KAAK,CAAC;YACrC,CAAC,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IACjD,CAAC;IAED,2CAAiB,GAAjB,UAAkB,IAAI;QAClB,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,EAAE;YAC1B,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBACpB,OAAQ;aACX;YAED,OAAO,IAAI,CAAC;SACf;QAED,IAAM,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACxC,IAAI,CAAC,qBAAqB,CAAC,iBAAiB,CAAC,CAAC;QAE9C,IAAI,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,EAAE;YACjC,OAAQ;SACX;QAED,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAE7B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,0CAAgB,GAAhB,UAAiB,WAAW;QACxB,IAAI,WAAW,CAAC,SAAS,EAAE;YACvB,OAAO,IAAI,CAAC;SACf;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;YAC3B,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,EAAE;YAC5D,OAAO,KAAK,CAAC;SAChB;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IACL,sBAAC;AAAD,CAAC,AA3ED,IA2EC;AAED,IAAM,YAAY,GAAG,UAAS,OAAO;IACjC,IAAI,CAAC,QAAQ,GAAG,IAAI,iBAAO,CAAC,IAAI,CAAC,CAAC;IAClC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;IACxB,IAAI,CAAC,KAAK,GAAG,IAAI,eAAe,CAAC,OAAO,CAAC,CAAC;AAC9C,CAAC,CAAC;AAEF,YAAY,CAAC,SAAS,GAAG;IACrB,WAAW,EAAE,IAAI;IACjB,GAAG,EAAE,UAAU,IAAI;QACf,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACrC,CAAC;IAED,gBAAgB,EAAE,UAAU,QAAQ,EAAE,SAAS;QAC3C,IAAI,QAAQ,CAAC,gBAAgB,EAAE,IAAI,QAAQ,CAAC,QAAQ,EAAE;YAClD,OAAO;SACV;QACD,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED,oBAAoB,EAAE,UAAU,SAAS,EAAE,SAAS;QAChD,mEAAmE;QACnE,gFAAgF;QAChF,SAAS,CAAC,MAAM,GAAG,EAAE,CAAC;IAC1B,CAAC;IAED,WAAW,EAAE,UAAU,UAAU,EAAE,SAAS;IAC5C,CAAC;IAED,YAAY,EAAE,UAAU,WAAW,EAAE,SAAS;QAC1C,IAAI,WAAW,CAAC,gBAAgB,EAAE,IAAI,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;YACvE,OAAO;SACV;QACD,OAAO,WAAW,CAAC;IACvB,CAAC;IAED,UAAU,EAAE,UAAS,SAAS,EAAE,SAAS;QACrC,IAAM,aAAa,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QAC/C,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAChC,SAAS,CAAC,WAAW,GAAG,KAAK,CAAC;QAE9B,OAAO,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;IAClE,CAAC;IAED,WAAW,EAAE,UAAU,UAAU,EAAE,SAAS;QACxC,IAAI,UAAU,CAAC,gBAAgB,EAAE,EAAE;YAC/B,OAAQ;SACX;QACD,OAAO,UAAU,CAAC;IACtB,CAAC;IAED,WAAW,EAAE,UAAS,UAAU,EAAE,SAAS;QACvC,IAAI,UAAU,CAAC,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC,MAAM,EAAE;YAC7C,OAAO,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;SAC1D;aAAM;YACH,OAAO,IAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;SAC7D;IACL,CAAC;IAED,cAAc,EAAE,UAAS,aAAa,EAAE,SAAS;QAC7C,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE,EAAE;YACnC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACpC,OAAO,aAAa,CAAC;SACxB;IACL,CAAC;IAED,mBAAmB,EAAE,UAAS,UAAU,EAAE,SAAS;QAC/C,2EAA2E;QAC3E,oBAAoB;QACpB,SAAS,cAAc,CAAC,UAAU;YAC9B,IAAM,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC;YACnC,OAAO,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC;QAC9F,CAAC;QACD,SAAS,YAAY,CAAC,UAAU;YAC5B,IAAM,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC;YACnC,IAAI,cAAc,CAAC,UAAU,CAAC,EAAE;gBAC5B,OAAO,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;aAC7B;YAED,OAAO,SAAS,CAAC;QACrB,CAAC;QACD,wDAAwD;QACxD,2BAA2B;QAC3B,iBAAiB;QACjB,IAAM,aAAa,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC;QAC/C,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACjC,SAAS,CAAC,WAAW,GAAG,KAAK,CAAC;QAE9B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;YACjC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;SAC/C;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;IACnE,CAAC;IAED,sBAAsB,EAAE,UAAS,UAAU,EAAE,SAAS;QAClD,IAAI,UAAU,CAAC,gBAAgB,EAAE,EAAE;YAC/B,OAAO;SACV;QAED,IAAI,UAAU,CAAC,IAAI,KAAK,UAAU,EAAE;YAChC,2EAA2E;YAC3E,0EAA0E;YAC1E,8DAA8D;YAC9D,IAAI,IAAI,CAAC,OAAO,EAAE;gBACd,IAAI,UAAU,CAAC,SAAS,EAAE;oBACtB,IAAM,OAAO,GAAG,IAAI,cAAI,CAAC,OAAO,CAAC,aAAM,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,UAAO,CAAC,CAAC;oBAClG,OAAO,CAAC,SAAS,GAAG,UAAU,CAAC,SAAS,CAAC;oBACzC,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;iBACvC;gBACD,OAAO;aACV;YACD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;SACvB;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IAED,eAAe,EAAE,UAAS,KAAK,EAAE,MAAM;QACnC,IAAI,CAAC,KAAK,EAAE;YACR,OAAO;SACV;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACnC,IAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAC1B,IAAI,MAAM,IAAI,QAAQ,YAAY,cAAI,CAAC,WAAW,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;gBACtE,MAAM,EAAE,OAAO,EAAE,uEAAuE;oBACpF,KAAK,EAAE,QAAQ,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE,QAAQ,CAAC,QAAQ,EAAE,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAC,CAAC;aAClG;YACD,IAAI,QAAQ,YAAY,cAAI,CAAC,IAAI,EAAE;gBAC/B,MAAM,EAAE,OAAO,EAAE,oBAAa,QAAQ,CAAC,IAAI,iCAA8B;oBACrE,KAAK,EAAE,QAAQ,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE,QAAQ,CAAC,QAAQ,EAAE,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAC,CAAC;aAClG;YACD,IAAI,QAAQ,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE;gBACtC,MAAM,EAAE,OAAO,EAAE,UAAG,QAAQ,CAAC,IAAI,mDAAgD;oBAC7E,KAAK,EAAE,QAAQ,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE,QAAQ,CAAC,QAAQ,EAAE,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAC,CAAC;aAClG;SACJ;IACL,CAAC;IAED,YAAY,EAAE,UAAU,WAAW,EAAE,SAAS;QAC1C,oDAAoD;QACpD,IAAI,IAAI,CAAC;QAET,IAAM,QAAQ,GAAG,EAAE,CAAC;QAEpB,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,KAAK,EAAE,WAAW,CAAC,SAAS,CAAC,CAAC;QAE/D,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE;YACnB,yBAAyB;YACzB,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;YAEvC,qEAAqE;YACrE,IAAM,SAAS,GAAG,WAAW,CAAC,KAAK,CAAC;YAEpC,IAAI,WAAW,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;YACnD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,GAAI;gBAC/B,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;gBACpB,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;oBACpB,0DAA0D;oBAC1D,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;oBACzC,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;oBACvB,WAAW,EAAE,CAAC;oBACd,SAAS;iBACZ;gBACD,CAAC,EAAE,CAAC;aACP;YACD,yDAAyD;YACzD,oDAAoD;YACpD,eAAe;YACf,IAAI,WAAW,GAAG,CAAC,EAAE;gBACjB,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aACrC;iBAAM;gBACH,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC;aAC5B;YACD,SAAS,CAAC,WAAW,GAAG,KAAK,CAAC;SACjC;aAAM,EAAE,4BAA4B;YACjC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAClC,SAAS,CAAC,WAAW,GAAG,KAAK,CAAC;SACjC;QAED,IAAI,WAAW,CAAC,KAAK,EAAE;YACnB,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YACpC,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;SACjD;QAED,yCAAyC;QACzC,IAAI,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,WAAW,CAAC,EAAE;YAC1C,WAAW,CAAC,gBAAgB,EAAE,CAAC;YAC/B,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC;SACtC;QAED,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;YACvB,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC;SACtB;QACD,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED,oBAAoB,EAAE,UAAS,WAAW;QACtC,IAAI,WAAW,CAAC,KAAK,EAAE;YACnB,WAAW,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK;iBAChC,MAAM,CAAC,UAAA,CAAC;gBACL,IAAI,CAAC,CAAC;gBACN,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,KAAK,GAAG,EAAE;oBAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,IAAG,CAAC,cAAI,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;iBAC1D;gBACD,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAC3B,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,EAAE;wBACxC,OAAO,IAAI,CAAC;qBACf;iBACJ;gBACD,OAAO,KAAK,CAAC;YACjB,CAAC,CAAC,CAAC;SACV;IACL,CAAC;IAED,qBAAqB,EAAE,UAAS,KAAK;QACjC,IAAI,CAAC,KAAK,EAAE;YAAE,OAAO;SAAE;QAEvB,oBAAoB;QACpB,IAAM,SAAS,GAAG,EAAE,CAAC;QAErB,IAAI,QAAQ,CAAC;QACb,IAAI,IAAI,CAAC;QACT,IAAI,CAAC,CAAC;QAEN,KAAK,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAG,CAAC,EAAE,EAAE;YACrC,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAChB,IAAI,IAAI,YAAY,cAAI,CAAC,WAAW,EAAE;gBAClC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;oBACvB,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;iBAC/B;qBAAM;oBACH,QAAQ,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBAChC,IAAI,QAAQ,YAAY,cAAI,CAAC,WAAW,EAAE;wBACtC,QAAQ,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;qBACjF;oBACD,IAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBAC1C,IAAI,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;wBAClC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;qBACtB;yBAAM;wBACH,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;qBAC1B;iBACJ;aACJ;SACJ;IACL,CAAC;IAED,WAAW,EAAE,UAAS,KAAK;QACvB,IAAI,CAAC,KAAK,EAAE;YACR,OAAO;SACV;QAED,IAAM,MAAM,GAAM,EAAE,CAAC;QACrB,IAAM,SAAS,GAAG,EAAE,CAAC;QAErB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACnC,IAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACtB,IAAI,IAAI,CAAC,KAAK,EAAE;gBACZ,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;gBACtB,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;oBAChC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC;gBACrC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aAC1B;SACJ;QAED,SAAS,CAAC,OAAO,CAAC,UAAA,KAAK;YACnB,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;gBAClB,IAAM,QAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBACxB,IAAI,OAAK,GAAI,EAAE,CAAC;gBAChB,IAAM,OAAK,GAAI,CAAC,IAAI,cAAI,CAAC,UAAU,CAAC,OAAK,CAAC,CAAC,CAAC;gBAC5C,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;oBACd,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,GAAG,CAAC,IAAI,CAAC,OAAK,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;wBAC5C,OAAK,CAAC,IAAI,CAAC,IAAI,cAAI,CAAC,UAAU,CAAC,OAAK,GAAG,EAAE,CAAC,CAAC,CAAC;qBAC/C;oBACD,OAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBACvB,QAAM,CAAC,SAAS,GAAG,QAAM,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC;gBAC1D,CAAC,CAAC,CAAC;gBACH,QAAM,CAAC,KAAK,GAAG,IAAI,cAAI,CAAC,KAAK,CAAC,OAAK,CAAC,CAAC;aACxC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;CACJ,CAAC;AAEF,kBAAe,YAAY,CAAC", "sourcesContent": ["/* eslint-disable no-unused-vars */\n/**\n * @todo - Remove unused when JSDoc types are added for visitor methods\n */\nimport tree from '../tree';\nimport Visitor from './visitor';\n\nclass CSSVisitorUtils {\n    constructor(context) {\n        this._visitor = new Visitor(this);\n        this._context = context;\n    }\n\n    containsSilentNonBlockedChild(bodyRules) {\n        let rule;\n        if (!bodyRules) {\n            return false;\n        }\n        for (let r = 0; r < bodyRules.length; r++) {\n            rule = bodyRules[r];\n            if (rule.isSilent && rule.isSilent(this._context) && !rule.blocksVisibility()) {\n                // the atrule contains something that was referenced (likely by extend)\n                // therefore it needs to be shown in output too\n                return true;\n            }\n        }\n        return false;\n    }\n\n    keepOnlyVisibleChilds(owner) {\n        if (owner && owner.rules) {\n            owner.rules = owner.rules.filter(thing => thing.isVisible());\n        }\n    }\n\n    isEmpty(owner) {\n        return (owner && owner.rules) \n            ? (owner.rules.length === 0) : true;\n    }\n\n    hasVisibleSelector(rulesetNode) {\n        return (rulesetNode && rulesetNode.paths)\n            ? (rulesetNode.paths.length > 0) : false;\n    }\n\n    resolveVisibility(node) {\n        if (!node.blocksVisibility()) {\n            if (this.isEmpty(node)) {\n                return ;\n            }\n\n            return node;\n        }\n\n        const compiledRulesBody = node.rules[0];\n        this.keepOnlyVisibleChilds(compiledRulesBody);\n\n        if (this.isEmpty(compiledRulesBody)) {\n            return ;\n        }\n\n        node.ensureVisibility();\n        node.removeVisibilityBlock();\n\n        return node;\n    }\n\n    isVisibleRuleset(rulesetNode) {\n        if (rulesetNode.firstRoot) {\n            return true;\n        }\n\n        if (this.isEmpty(rulesetNode)) {\n            return false;\n        }\n\n        if (!rulesetNode.root && !this.hasVisibleSelector(rulesetNode)) {\n            return false;\n        }\n\n        return true;\n    }\n}\n\nconst ToCSSVisitor = function(context) {\n    this._visitor = new Visitor(this);\n    this._context = context;\n    this.utils = new CSSVisitorUtils(context);\n};\n\nToCSSVisitor.prototype = {\n    isReplacing: true,\n    run: function (root) {\n        return this._visitor.visit(root);\n    },\n\n    visitDeclaration: function (declNode, visitArgs) {\n        if (declNode.blocksVisibility() || declNode.variable) {\n            return;\n        }\n        return declNode;\n    },\n\n    visitMixinDefinition: function (mixinNode, visitArgs) {\n        // mixin definitions do not get eval'd - this means they keep state\n        // so we have to clear that state here so it isn't used if toCSS is called twice\n        mixinNode.frames = [];\n    },\n\n    visitExtend: function (extendNode, visitArgs) {\n    },\n\n    visitComment: function (commentNode, visitArgs) {\n        if (commentNode.blocksVisibility() || commentNode.isSilent(this._context)) {\n            return;\n        }\n        return commentNode;\n    },\n\n    visitMedia: function(mediaNode, visitArgs) {\n        const originalRules = mediaNode.rules[0].rules;\n        mediaNode.accept(this._visitor);\n        visitArgs.visitDeeper = false;\n\n        return this.utils.resolveVisibility(mediaNode, originalRules);\n    },\n\n    visitImport: function (importNode, visitArgs) {\n        if (importNode.blocksVisibility()) {\n            return ;\n        }\n        return importNode;\n    },\n\n    visitAtRule: function(atRuleNode, visitArgs) {\n        if (atRuleNode.rules && atRuleNode.rules.length) {\n            return this.visitAtRuleWithBody(atRuleNode, visitArgs);\n        } else {\n            return this.visitAtRuleWithoutBody(atRuleNode, visitArgs);\n        }\n    },\n\n    visitAnonymous: function(anonymousNode, visitArgs) {\n        if (!anonymousNode.blocksVisibility()) {\n            anonymousNode.accept(this._visitor);\n            return anonymousNode;\n        }\n    },\n\n    visitAtRuleWithBody: function(atRuleNode, visitArgs) {\n        // if there is only one nested ruleset and that one has no path, then it is\n        // just fake ruleset\n        function hasFakeRuleset(atRuleNode) {\n            const bodyRules = atRuleNode.rules;\n            return bodyRules.length === 1 && (!bodyRules[0].paths || bodyRules[0].paths.length === 0);\n        }\n        function getBodyRules(atRuleNode) {\n            const nodeRules = atRuleNode.rules;\n            if (hasFakeRuleset(atRuleNode)) {\n                return nodeRules[0].rules;\n            }\n\n            return nodeRules;\n        }\n        // it is still true that it is only one ruleset in array\n        // this is last such moment\n        // process childs\n        const originalRules = getBodyRules(atRuleNode);\n        atRuleNode.accept(this._visitor);\n        visitArgs.visitDeeper = false;\n\n        if (!this.utils.isEmpty(atRuleNode)) {\n            this._mergeRules(atRuleNode.rules[0].rules);\n        }\n\n        return this.utils.resolveVisibility(atRuleNode, originalRules);\n    },\n\n    visitAtRuleWithoutBody: function(atRuleNode, visitArgs) {\n        if (atRuleNode.blocksVisibility()) {\n            return;\n        }\n\n        if (atRuleNode.name === '@charset') {\n            // Only output the debug info together with subsequent @charset definitions\n            // a comment (or @media statement) before the actual @charset atrule would\n            // be considered illegal css as it has to be on the first line\n            if (this.charset) {\n                if (atRuleNode.debugInfo) {\n                    const comment = new tree.Comment(`/* ${atRuleNode.toCSS(this._context).replace(/\\n/g, '')} */\\n`);\n                    comment.debugInfo = atRuleNode.debugInfo;\n                    return this._visitor.visit(comment);\n                }\n                return;\n            }\n            this.charset = true;\n        }\n\n        return atRuleNode;\n    },\n\n    checkValidNodes: function(rules, isRoot) {\n        if (!rules) {\n            return;\n        }\n\n        for (let i = 0; i < rules.length; i++) {\n            const ruleNode = rules[i];\n            if (isRoot && ruleNode instanceof tree.Declaration && !ruleNode.variable) {\n                throw { message: 'Properties must be inside selector blocks. They cannot be in the root',\n                    index: ruleNode.getIndex(), filename: ruleNode.fileInfo() && ruleNode.fileInfo().filename};\n            }\n            if (ruleNode instanceof tree.Call) {\n                throw { message: `Function '${ruleNode.name}' did not return a root node`,\n                    index: ruleNode.getIndex(), filename: ruleNode.fileInfo() && ruleNode.fileInfo().filename};\n            }\n            if (ruleNode.type && !ruleNode.allowRoot) {\n                throw { message: `${ruleNode.type} node returned by a function is not valid here`,\n                    index: ruleNode.getIndex(), filename: ruleNode.fileInfo() && ruleNode.fileInfo().filename};\n            }\n        }\n    },\n\n    visitRuleset: function (rulesetNode, visitArgs) {\n        // at this point rulesets are nested into each other\n        let rule;\n\n        const rulesets = [];\n\n        this.checkValidNodes(rulesetNode.rules, rulesetNode.firstRoot);\n\n        if (!rulesetNode.root) {\n            // remove invisible paths\n            this._compileRulesetPaths(rulesetNode);\n\n            // remove rulesets from this ruleset body and compile them separately\n            const nodeRules = rulesetNode.rules;\n\n            let nodeRuleCnt = nodeRules ? nodeRules.length : 0;\n            for (let i = 0; i < nodeRuleCnt; ) {\n                rule = nodeRules[i];\n                if (rule && rule.rules) {\n                    // visit because we are moving them out from being a child\n                    rulesets.push(this._visitor.visit(rule));\n                    nodeRules.splice(i, 1);\n                    nodeRuleCnt--;\n                    continue;\n                }\n                i++;\n            }\n            // accept the visitor to remove rules and refactor itself\n            // then we can decide nogw whether we want it or not\n            // compile body\n            if (nodeRuleCnt > 0) {\n                rulesetNode.accept(this._visitor);\n            } else {\n                rulesetNode.rules = null;\n            }\n            visitArgs.visitDeeper = false;\n        } else { // if (! rulesetNode.root) {\n            rulesetNode.accept(this._visitor);\n            visitArgs.visitDeeper = false;\n        }\n\n        if (rulesetNode.rules) {\n            this._mergeRules(rulesetNode.rules);\n            this._removeDuplicateRules(rulesetNode.rules);\n        }\n\n        // now decide whether we keep the ruleset\n        if (this.utils.isVisibleRuleset(rulesetNode)) {\n            rulesetNode.ensureVisibility();\n            rulesets.splice(0, 0, rulesetNode);\n        }\n\n        if (rulesets.length === 1) {\n            return rulesets[0];\n        }\n        return rulesets;\n    },\n\n    _compileRulesetPaths: function(rulesetNode) {\n        if (rulesetNode.paths) {\n            rulesetNode.paths = rulesetNode.paths\n                .filter(p => {\n                    let i;\n                    if (p[0].elements[0].combinator.value === ' ') {\n                        p[0].elements[0].combinator = new(tree.Combinator)('');\n                    }\n                    for (i = 0; i < p.length; i++) {\n                        if (p[i].isVisible() && p[i].getIsOutput()) {\n                            return true;\n                        }\n                    }\n                    return false;\n                });\n        }\n    },\n\n    _removeDuplicateRules: function(rules) {\n        if (!rules) { return; }\n\n        // remove duplicates\n        const ruleCache = {};\n\n        let ruleList;\n        let rule;\n        let i;\n\n        for (i = rules.length - 1; i >= 0 ; i--) {\n            rule = rules[i];\n            if (rule instanceof tree.Declaration) {\n                if (!ruleCache[rule.name]) {\n                    ruleCache[rule.name] = rule;\n                } else {\n                    ruleList = ruleCache[rule.name];\n                    if (ruleList instanceof tree.Declaration) {\n                        ruleList = ruleCache[rule.name] = [ruleCache[rule.name].toCSS(this._context)];\n                    }\n                    const ruleCSS = rule.toCSS(this._context);\n                    if (ruleList.indexOf(ruleCSS) !== -1) {\n                        rules.splice(i, 1);\n                    } else {\n                        ruleList.push(ruleCSS);\n                    }\n                }\n            }\n        }\n    },\n\n    _mergeRules: function(rules) {\n        if (!rules) {\n            return; \n        }\n\n        const groups    = {};\n        const groupsArr = [];\n\n        for (let i = 0; i < rules.length; i++) {\n            const rule = rules[i];\n            if (rule.merge) {\n                const key = rule.name;\n                groups[key] ? rules.splice(i--, 1) : \n                    groupsArr.push(groups[key] = []);\n                groups[key].push(rule);\n            }\n        }\n\n        groupsArr.forEach(group => {\n            if (group.length > 0) {\n                const result = group[0];\n                let space  = [];\n                const comma  = [new tree.Expression(space)];\n                group.forEach(rule => {\n                    if ((rule.merge === '+') && (space.length > 0)) {\n                        comma.push(new tree.Expression(space = []));\n                    }\n                    space.push(rule.value);\n                    result.important = result.important || rule.important;\n                });\n                result.value = new tree.Value(comma);\n            }\n        });\n    }\n};\n\nexport default ToCSSVisitor;\n"]}