{"version": 3, "file": "visitor.js", "sourceRoot": "", "sources": ["../../../src/less/visitors/visitor.js"], "names": [], "mappings": ";;;AAAA,yDAA2B;AAE3B,IAAM,UAAU,GAAG,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC;AACzC,IAAI,WAAW,GAAG,KAAK,CAAC;AAExB,SAAS,KAAK,CAAC,IAAI;IACf,OAAO,IAAI,CAAC;AAChB,CAAC;AAED,SAAS,cAAc,CAAC,MAAM,EAAE,MAAM;IAClC,qDAAqD;IACrD,IAAI,GAAG,EAAE,KAAK,CAAC;IACf,KAAK,GAAG,IAAI,MAAM,EAAE;QAChB,4BAA4B;QAC5B,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;QACpB,QAAQ,OAAO,KAAK,EAAE;YAClB,KAAK,UAAU;gBACX,wEAAwE;gBACxE,kBAAkB;gBAClB,IAAI,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,SAAS,CAAC,IAAI,EAAE;oBACzC,KAAK,CAAC,SAAS,CAAC,SAAS,GAAG,MAAM,EAAE,CAAC;iBACxC;gBACD,MAAM;YACV,KAAK,QAAQ;gBACT,MAAM,GAAG,cAAc,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;gBACvC,MAAM;SAEb;KACJ;IACD,OAAO,MAAM,CAAC;AAClB,CAAC;AAED;IACI,iBAAY,cAAc;QACtB,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QACtC,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QAEzB,IAAI,CAAC,WAAW,EAAE;YACd,cAAc,CAAC,cAAI,EAAE,CAAC,CAAC,CAAC;YACxB,WAAW,GAAG,IAAI,CAAC;SACtB;IACL,CAAC;IAED,uBAAK,GAAL,UAAM,IAAI;QACN,IAAI,CAAC,IAAI,EAAE;YACP,OAAO,IAAI,CAAC;SACf;QAED,IAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC;QACrC,IAAI,CAAC,aAAa,EAAE;YAChB,qCAAqC;YACrC,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;gBACpC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aAC1B;YACD,OAAO,IAAI,CAAC;SACf;QAED,IAAM,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC;QAClC,IAAI,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;QAC7C,IAAI,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;QACjD,IAAM,SAAS,GAAG,UAAU,CAAC;QAC7B,IAAI,MAAM,CAAC;QAEX,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC;QAE7B,IAAI,CAAC,IAAI,EAAE;YACP,MAAM,GAAG,eAAQ,IAAI,CAAC,IAAI,CAAE,CAAC;YAC7B,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC;YAC7B,OAAO,GAAG,IAAI,CAAC,UAAG,MAAM,QAAK,CAAC,IAAI,KAAK,CAAC;YACxC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC;YACzC,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,GAAG,OAAO,CAAC;SAChD;QAED,IAAI,IAAI,KAAK,KAAK,EAAE;YAChB,IAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;YACjD,IAAI,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE;gBAC1B,IAAI,GAAG,OAAO,CAAC;aAClB;SACJ;QAED,IAAI,SAAS,CAAC,WAAW,IAAI,IAAI,EAAE;YAC/B,IAAI,IAAI,CAAC,MAAM,EAAE;gBACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;oBAC7C,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;wBAChB,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;qBACxB;iBACJ;aACJ;iBAAM,IAAI,IAAI,CAAC,MAAM,EAAE;gBACpB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;aACrB;SACJ;QAED,IAAI,OAAO,IAAI,KAAK,EAAE;YAClB,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SAC5B;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,4BAAU,GAAV,UAAW,KAAK,EAAE,YAAY;QAC1B,IAAI,CAAC,KAAK,EAAE;YACR,OAAO,KAAK,CAAC;SAChB;QAED,IAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC;QACzB,IAAI,CAAC,CAAC;QAEN,gBAAgB;QAChB,IAAI,YAAY,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE;YACnD,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;gBACtB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;aACxB;YACD,OAAO,KAAK,CAAC;SAChB;QAED,YAAY;QACZ,IAAM,GAAG,GAAG,EAAE,CAAC;QACf,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YACtB,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YACnC,IAAI,KAAK,KAAK,SAAS,EAAE;gBAAE,SAAS;aAAE;YACtC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;gBACf,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACnB;iBAAM,IAAI,KAAK,CAAC,MAAM,EAAE;gBACrB,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;aAC5B;SACJ;QACD,OAAO,GAAG,CAAC;IACf,CAAC;IAED,yBAAO,GAAP,UAAQ,GAAG,EAAE,GAAG;QACZ,IAAI,CAAC,GAAG,EAAE;YACN,GAAG,GAAG,EAAE,CAAC;SACZ;QAED,IAAI,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,EAAE,UAAU,CAAC;QAE3C,KAAK,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YACxC,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;YACd,IAAI,IAAI,KAAK,SAAS,EAAE;gBACpB,SAAS;aACZ;YACD,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;gBACd,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACf,SAAS;aACZ;YAED,KAAK,CAAC,GAAG,CAAC,EAAE,SAAS,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;gBACrD,UAAU,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gBACrB,IAAI,UAAU,KAAK,SAAS,EAAE;oBAC1B,SAAS;iBACZ;gBACD,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;oBACpB,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;iBACxB;qBAAM,IAAI,UAAU,CAAC,MAAM,EAAE;oBAC1B,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;iBACjC;aACJ;SACJ;QAED,OAAO,GAAG,CAAC;IACf,CAAC;IACL,cAAC;AAAD,CAAC,AAlID,IAkIC;AAED,kBAAe,OAAO,CAAC", "sourcesContent": ["import tree from '../tree';\n\nconst _visitArgs = { visitDeeper: true };\nlet _hasIndexed = false;\n\nfunction _noop(node) {\n    return node;\n}\n\nfunction indexNodeTypes(parent, ticker) {\n    // add .typeIndex to tree node types for lookup table\n    let key, child;\n    for (key in parent) { \n        /* eslint guard-for-in: 0 */\n        child = parent[key];\n        switch (typeof child) {\n            case 'function':\n                // ignore bound functions directly on tree which do not have a prototype\n                // or aren't nodes\n                if (child.prototype && child.prototype.type) {\n                    child.prototype.typeIndex = ticker++;\n                }\n                break;\n            case 'object':\n                ticker = indexNodeTypes(child, ticker);\n                break;\n        \n        }\n    }\n    return ticker;\n}\n\nclass Visitor {\n    constructor(implementation) {\n        this._implementation = implementation;\n        this._visitInCache = {};\n        this._visitOutCache = {};\n\n        if (!_hasIndexed) {\n            indexNodeTypes(tree, 1);\n            _hasIndexed = true;\n        }\n    }\n\n    visit(node) {\n        if (!node) {\n            return node;\n        }\n\n        const nodeTypeIndex = node.typeIndex;\n        if (!nodeTypeIndex) {\n            // MixinCall args aren't a node type?\n            if (node.value && node.value.typeIndex) {\n                this.visit(node.value);\n            }\n            return node;\n        }\n\n        const impl = this._implementation;\n        let func = this._visitInCache[nodeTypeIndex];\n        let funcOut = this._visitOutCache[nodeTypeIndex];\n        const visitArgs = _visitArgs;\n        let fnName;\n\n        visitArgs.visitDeeper = true;\n\n        if (!func) {\n            fnName = `visit${node.type}`;\n            func = impl[fnName] || _noop;\n            funcOut = impl[`${fnName}Out`] || _noop;\n            this._visitInCache[nodeTypeIndex] = func;\n            this._visitOutCache[nodeTypeIndex] = funcOut;\n        }\n\n        if (func !== _noop) {\n            const newNode = func.call(impl, node, visitArgs);\n            if (node && impl.isReplacing) {\n                node = newNode;\n            }\n        }\n\n        if (visitArgs.visitDeeper && node) {\n            if (node.length) {\n                for (let i = 0, cnt = node.length; i < cnt; i++) {\n                    if (node[i].accept) {\n                        node[i].accept(this);\n                    }\n                }\n            } else if (node.accept) {\n                node.accept(this);\n            }\n        }\n\n        if (funcOut != _noop) {\n            funcOut.call(impl, node);\n        }\n\n        return node;\n    }\n\n    visitArray(nodes, nonReplacing) {\n        if (!nodes) {\n            return nodes;\n        }\n\n        const cnt = nodes.length;\n        let i;\n\n        // Non-replacing\n        if (nonReplacing || !this._implementation.isReplacing) {\n            for (i = 0; i < cnt; i++) {\n                this.visit(nodes[i]);\n            }\n            return nodes;\n        }\n\n        // Replacing\n        const out = [];\n        for (i = 0; i < cnt; i++) {\n            const evald = this.visit(nodes[i]);\n            if (evald === undefined) { continue; }\n            if (!evald.splice) {\n                out.push(evald);\n            } else if (evald.length) {\n                this.flatten(evald, out);\n            }\n        }\n        return out;\n    }\n\n    flatten(arr, out) {\n        if (!out) {\n            out = [];\n        }\n\n        let cnt, i, item, nestedCnt, j, nestedItem;\n\n        for (i = 0, cnt = arr.length; i < cnt; i++) {\n            item = arr[i];\n            if (item === undefined) {\n                continue;\n            }\n            if (!item.splice) {\n                out.push(item);\n                continue;\n            }\n\n            for (j = 0, nestedCnt = item.length; j < nestedCnt; j++) {\n                nestedItem = item[j];\n                if (nestedItem === undefined) {\n                    continue;\n                }\n                if (!nestedItem.splice) {\n                    out.push(nestedItem);\n                } else if (nestedItem.length) {\n                    this.flatten(nestedItem, out);\n                }\n            }\n        }\n\n        return out;\n    }\n}\n\nexport default Visitor;\n"]}