{"version": 3, "file": "environment-api.js", "sourceRoot": "", "sources": ["../../../src/less/environment/environment-api.ts"], "names": [], "mappings": "", "sourcesContent": ["export interface Environment {\n    /**\n     * Converts a string to a base 64 string\n     */\n    encodeBase64(str: string): string\n    /**\n     * Lookup the mime-type of a filename\n     */\n    mimeLookup(filename: string): string\n    /**\n     * Look up the charset of a mime type\n     * @param mime\n     */\n    charsetLookup(mime: string): string\n    /**\n     * Gets a source map generator\n     *\n     * @todo - Figure out precise type\n     */\n    getSourceMapGenerator(): any\n}\n"]}