{"version": 3, "file": "abstract-plugin-loader.js", "sourceRoot": "", "sources": ["../../../src/less/environment/abstract-plugin-loader.js"], "names": [], "mappings": ";;;AAAA,6FAA8D;AAC9D,qEAAsC;AAEtC;IACI;QACI,uCAAuC;QACvC,IAAI,CAAC,OAAO,GAAG;YACX,OAAO,IAAI,CAAC;QAChB,CAAC,CAAA;IACL,CAAC;IAED,yCAAU,GAAV,UAAW,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,aAAa,EAAE,QAAQ;QAE1D,IAAI,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,EAAE,QAAQ,EAAE,MAAM,CAAC;QAE9E,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;QAEtC,IAAI,QAAQ,EAAE;YACV,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;gBAC9B,QAAQ,GAAG,QAAQ,CAAC;aACvB;iBACI;gBACD,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC;aAChC;SACJ;QACD,IAAM,SAAS,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC;QAEnF,IAAI,QAAQ,EAAE;YACV,SAAS,GAAG,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAExC,IAAI,SAAS,EAAE;gBACX,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;gBAC3E,IAAI,MAAM,EAAE;oBACR,OAAO,MAAM,CAAC;iBACjB;gBACD,IAAI;oBACA,IAAI,SAAS,CAAC,GAAG,EAAE;wBACf,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;qBAC/C;iBACJ;gBACD,OAAO,CAAC,EAAE;oBACN,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,OAAO,IAAI,2BAA2B,CAAC;oBACrD,OAAO,IAAI,oBAAS,CAAC,CAAC,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;iBAC9C;gBACD,OAAO,SAAS,CAAC;aACpB;SACJ;QACD,WAAW,GAAG;YACV,OAAO,EAAE,EAAE;YACX,aAAa,eAAA;YACb,QAAQ,UAAA;SACX,CAAC;QACF,QAAQ,GAAG,2BAAgB,CAAC,MAAM,EAAE,CAAC;QAErC,IAAM,cAAc,GAAG,UAAS,GAAG;YAC/B,SAAS,GAAG,GAAG,CAAC;QACpB,CAAC,CAAC;QAEF,IAAI;YACA,MAAM,GAAG,IAAI,QAAQ,CAAC,QAAQ,EAAE,SAAS,EAAE,gBAAgB,EAAE,WAAW,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;YAChH,MAAM,CAAC,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,cAAc,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;SAC9G;QACD,OAAO,CAAC,EAAE;YACN,OAAO,IAAI,oBAAS,CAAC,CAAC,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;SAC9C;QAED,IAAI,CAAC,SAAS,EAAE;YACZ,SAAS,GAAG,WAAW,CAAC,OAAO,CAAC;SACnC;QACD,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;QAEhE,IAAI,SAAS,YAAY,oBAAS,EAAE;YAChC,OAAO,SAAS,CAAC;SACpB;QAED,IAAI,SAAS,EAAE;YACX,SAAS,CAAC,OAAO,GAAG,OAAO,CAAC;YAC5B,SAAS,CAAC,QAAQ,GAAG,QAAQ,CAAC;YAE9B,wEAAwE;YACxE,IAAI,CAAC,SAAS,CAAC,UAAU,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;gBACjF,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;gBAE3E,IAAI,MAAM,EAAE;oBACR,OAAO,MAAM,CAAC;iBACjB;aACJ;YAED,oBAAoB;YACpB,aAAa,CAAC,SAAS,CAAC,SAAS,EAAE,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAChE,SAAS,CAAC,SAAS,GAAG,QAAQ,CAAC,iBAAiB,EAAE,CAAC;YAEnD,2EAA2E;YAC3E,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;YAC3E,IAAI,MAAM,EAAE;gBACR,OAAO,MAAM,CAAC;aACjB;YAED,yBAAyB;YACzB,IAAI;gBACA,IAAI,SAAS,CAAC,GAAG,EAAE;oBACf,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;iBAC/C;aACJ;YACD,OAAO,CAAC,EAAE;gBACN,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,OAAO,IAAI,2BAA2B,CAAC;gBACrD,OAAO,IAAI,oBAAS,CAAC,CAAC,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;aAC9C;SAEJ;aACI;YACD,OAAO,IAAI,oBAAS,CAAC,EAAE,OAAO,EAAE,oBAAoB,EAAE,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;SAC9E;QAED,OAAO,SAAS,CAAC;IAErB,CAAC;IAED,4CAAa,GAAb,UAAc,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO;QACzC,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;YAC/B,OAAO,IAAI,oBAAS,CAAC;gBACjB,OAAO,EAAE,oDAA6C,IAAI,mCAAgC;aAC7F,CAAC,CAAC;SACN;QACD,IAAI;YACA,MAAM,CAAC,UAAU,IAAI,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;SACnD;QACD,OAAO,CAAC,EAAE;YACN,OAAO,IAAI,oBAAS,CAAC,CAAC,CAAC,CAAC;SAC3B;IACL,CAAC;IAED,6CAAc,GAAd,UAAe,MAAM,EAAE,QAAQ,EAAE,IAAI;QACjC,IAAI,MAAM,EAAE;YACR,mCAAmC;YACnC,yDAAyD;YACzD,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE;gBAC9B,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;aACzB;YAED,IAAI,MAAM,CAAC,UAAU,EAAE;gBACnB,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;oBAC/D,OAAO,IAAI,oBAAS,CAAC;wBACjB,OAAO,EAAE,iBAAU,IAAI,+BAAqB,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,UAAU,CAAC,CAAE;qBACxF,CAAC,CAAC;iBACN;aACJ;YACD,OAAO,MAAM,CAAC;SACjB;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,6CAAc,GAAd,UAAe,QAAQ,EAAE,QAAQ;QAC7B,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;YAC9B,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;YACtD,QAAQ,CAAC,KAAK,EAAE,CAAC;SACpB;QACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtC,IAAI,QAAQ,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,CAAC,CAAC,EAAE;gBAC7B,OAAO,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACjE;SACJ;QACD,OAAO,CAAC,CAAC;IACb,CAAC;IAED,8CAAe,GAAf,UAAgB,OAAO;QACnB,IAAI,aAAa,GAAG,EAAE,CAAC;QACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrC,aAAa,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;SAC5D;QACD,OAAO,aAAa,CAAC;IACzB,CAAC;IAED,yCAAU,GAAV,UAAW,OAAO;QACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrC,IAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC1B,IAAI,MAAM,CAAC,UAAU,EAAE;gBACnB,MAAM,CAAC,UAAU,EAAE,CAAC;aACvB;SACJ;IACL,CAAC;IACL,2BAAC;AAAD,CAAC,AAlLD,IAkLC;AAED,kBAAe,oBAAoB,CAAC", "sourcesContent": ["import functionRegistry from '../functions/function-registry';\nimport LessError from '../less-error';\n\nclass AbstractPluginLoader {\n    constructor() {\n        // Implemented by Node.js plugin loader\n        this.require = function() {\n            return null;\n        }\n    }\n\n    evalPlugin(contents, context, imports, pluginOptions, fileInfo) {\n\n        let loader, registry, pluginObj, localModule, pluginManager, filename, result;\n\n        pluginManager = context.pluginManager;\n\n        if (fileInfo) {\n            if (typeof fileInfo === 'string') {\n                filename = fileInfo;\n            }\n            else {\n                filename = fileInfo.filename;\n            }\n        }\n        const shortname = (new this.less.FileManager()).extractUrlParts(filename).filename;\n\n        if (filename) {\n            pluginObj = pluginManager.get(filename);\n\n            if (pluginObj) {\n                result = this.trySetOptions(pluginObj, filename, shortname, pluginOptions);\n                if (result) {\n                    return result;\n                }\n                try {\n                    if (pluginObj.use) {\n                        pluginObj.use.call(this.context, pluginObj);\n                    }\n                }\n                catch (e) {\n                    e.message = e.message || 'Error during @plugin call';\n                    return new LessError(e, imports, filename);\n                }\n                return pluginObj;\n            }\n        }\n        localModule = {\n            exports: {},\n            pluginManager,\n            fileInfo\n        };\n        registry = functionRegistry.create();\n\n        const registerPlugin = function(obj) {\n            pluginObj = obj;\n        };\n\n        try {\n            loader = new Function('module', 'require', 'registerPlugin', 'functions', 'tree', 'less', 'fileInfo', contents);\n            loader(localModule, this.require(filename), registerPlugin, registry, this.less.tree, this.less, fileInfo);\n        }\n        catch (e) {\n            return new LessError(e, imports, filename);\n        }\n\n        if (!pluginObj) {\n            pluginObj = localModule.exports;\n        }\n        pluginObj = this.validatePlugin(pluginObj, filename, shortname);\n\n        if (pluginObj instanceof LessError) {\n            return pluginObj;\n        }\n\n        if (pluginObj) {\n            pluginObj.imports = imports;\n            pluginObj.filename = filename;\n\n            // For < 3.x (or unspecified minVersion) - setOptions() before install()\n            if (!pluginObj.minVersion || this.compareVersion('3.0.0', pluginObj.minVersion) < 0) {\n                result = this.trySetOptions(pluginObj, filename, shortname, pluginOptions);\n\n                if (result) {\n                    return result;\n                }\n            }\n\n            // Run on first load\n            pluginManager.addPlugin(pluginObj, fileInfo.filename, registry);\n            pluginObj.functions = registry.getLocalFunctions();\n\n            // Need to call setOptions again because the pluginObj might have functions\n            result = this.trySetOptions(pluginObj, filename, shortname, pluginOptions);\n            if (result) {\n                return result;\n            }\n\n            // Run every @plugin call\n            try {\n                if (pluginObj.use) {\n                    pluginObj.use.call(this.context, pluginObj);\n                }\n            }\n            catch (e) {\n                e.message = e.message || 'Error during @plugin call';\n                return new LessError(e, imports, filename);\n            }\n\n        }\n        else {\n            return new LessError({ message: 'Not a valid plugin' }, imports, filename);\n        }\n\n        return pluginObj;\n\n    }\n\n    trySetOptions(plugin, filename, name, options) {\n        if (options && !plugin.setOptions) {\n            return new LessError({\n                message: `Options have been provided but the plugin ${name} does not support any options.`\n            });\n        }\n        try {\n            plugin.setOptions && plugin.setOptions(options);\n        }\n        catch (e) {\n            return new LessError(e);\n        }\n    }\n\n    validatePlugin(plugin, filename, name) {\n        if (plugin) {\n            // support plugins being a function\n            // so that the plugin can be more usable programmatically\n            if (typeof plugin === 'function') {\n                plugin = new plugin();\n            }\n\n            if (plugin.minVersion) {\n                if (this.compareVersion(plugin.minVersion, this.less.version) < 0) {\n                    return new LessError({\n                        message: `Plugin ${name} requires version ${this.versionToString(plugin.minVersion)}`\n                    });\n                }\n            }\n            return plugin;\n        }\n        return null;\n    }\n\n    compareVersion(aVersion, bVersion) {\n        if (typeof aVersion === 'string') {\n            aVersion = aVersion.match(/^(\\d+)\\.?(\\d+)?\\.?(\\d+)?/);\n            aVersion.shift();\n        }\n        for (let i = 0; i < aVersion.length; i++) {\n            if (aVersion[i] !== bVersion[i]) {\n                return parseInt(aVersion[i]) > parseInt(bVersion[i]) ? -1 : 1;\n            }\n        }\n        return 0;\n    }\n\n    versionToString(version) {\n        let versionString = '';\n        for (let i = 0; i < version.length; i++) {\n            versionString += (versionString ? '.' : '') + version[i];\n        }\n        return versionString;\n    }\n\n    printUsage(plugins) {\n        for (let i = 0; i < plugins.length; i++) {\n            const plugin = plugins[i];\n            if (plugin.printUsage) {\n                plugin.printUsage();\n            }\n        }\n    }\n}\n\nexport default AbstractPluginLoader;\n\n"]}