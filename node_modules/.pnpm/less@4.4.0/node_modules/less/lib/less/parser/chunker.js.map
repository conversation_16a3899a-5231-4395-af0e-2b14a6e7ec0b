{"version": 3, "file": "chunker.js", "sourceRoot": "", "sources": ["../../../src/less/parser/chunker.js"], "names": [], "mappings": ";;AAAA,+BAA+B;AAC/B,mBAAyB,KAAK,EAAE,IAAI;IAChC,IAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC;IACzB,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,UAAU,GAAG,CAAC,CAAC;IACnB,IAAI,WAAW,CAAC;IAChB,IAAI,gBAAgB,CAAC;IACrB,IAAI,gBAAgB,CAAC;IACrB,IAAI,wBAAwB,CAAC;IAC7B,IAAM,MAAM,GAAG,EAAE,CAAC;IAClB,IAAI,QAAQ,GAAG,CAAC,CAAC;IACjB,IAAI,mBAAmB,CAAC;IACxB,IAAI,sBAAsB,CAAC;IAC3B,IAAI,EAAE,CAAC;IACP,IAAI,GAAG,CAAC;IACR,IAAI,OAAO,CAAC;IAEZ,SAAS,SAAS,CAAC,KAAK;QACpB,IAAM,GAAG,GAAG,mBAAmB,GAAG,QAAQ,CAAC;QAC3C,IAAI,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE;YACjC,OAAO;SACV;QACD,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,EAAE,mBAAmB,GAAG,CAAC,CAAC,CAAC,CAAC;QAC5D,QAAQ,GAAG,mBAAmB,GAAG,CAAC,CAAC;IACvC,CAAC;IAED,KAAK,mBAAmB,GAAG,CAAC,EAAE,mBAAmB,GAAG,GAAG,EAAE,mBAAmB,EAAE,EAAE;QAC5E,EAAE,GAAG,KAAK,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC;QAC3C,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,IAAI,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE;YAC1C,oBAAoB;YACpB,SAAS;SACZ;QAED,QAAQ,EAAE,EAAE;YACR,KAAK,EAAE,EAAyB,IAAI;gBAChC,UAAU,EAAE,CAAC;gBACb,gBAAgB,GAAG,mBAAmB,CAAC;gBACvC,SAAS;YACb,KAAK,EAAE,EAAyB,IAAI;gBAChC,IAAI,EAAE,UAAU,GAAG,CAAC,EAAE;oBAClB,OAAO,IAAI,CAAC,qBAAqB,EAAE,mBAAmB,CAAC,CAAC;iBAC3D;gBACD,SAAS;YACb,KAAK,EAAE,EAAyB,IAAI;gBAChC,IAAI,CAAC,UAAU,EAAE;oBAAE,SAAS,EAAE,CAAC;iBAAE;gBACjC,SAAS;YACb,KAAK,GAAG,EAAwB,IAAI;gBAChC,KAAK,EAAE,CAAC;gBACR,WAAW,GAAG,mBAAmB,CAAC;gBAClC,SAAS;YACb,KAAK,GAAG,EAAwB,IAAI;gBAChC,IAAI,EAAE,KAAK,GAAG,CAAC,EAAE;oBACb,OAAO,IAAI,CAAC,qBAAqB,EAAE,mBAAmB,CAAC,CAAC;iBAC3D;gBACD,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,EAAE;oBAAE,SAAS,EAAE,CAAC;iBAAE;gBAC3C,SAAS;YACb,KAAK,EAAE,EAAyB,IAAI;gBAChC,IAAI,mBAAmB,GAAG,GAAG,GAAG,CAAC,EAAE;oBAAE,mBAAmB,EAAE,CAAC;oBAAC,SAAS;iBAAE;gBACvE,OAAO,IAAI,CAAC,gBAAgB,EAAE,mBAAmB,CAAC,CAAC;YACvD,KAAK,EAAE,CAAC;YACR,KAAK,EAAE,CAAC;YACR,KAAK,EAAE,EAAyB,aAAa;gBACzC,OAAO,GAAG,CAAC,CAAC;gBACZ,sBAAsB,GAAG,mBAAmB,CAAC;gBAC7C,KAAK,mBAAmB,GAAG,mBAAmB,GAAG,CAAC,EAAE,mBAAmB,GAAG,GAAG,EAAE,mBAAmB,EAAE,EAAE;oBAClG,GAAG,GAAG,KAAK,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC;oBAC5C,IAAI,GAAG,GAAG,EAAE,EAAE;wBAAE,SAAS;qBAAE;oBAC3B,IAAI,GAAG,IAAI,EAAE,EAAE;wBAAE,OAAO,GAAG,CAAC,CAAC;wBAAC,MAAM;qBAAE;oBACtC,IAAI,GAAG,IAAI,EAAE,EAAE,EAAS,IAAI;wBACxB,IAAI,mBAAmB,IAAI,GAAG,GAAG,CAAC,EAAE;4BAChC,OAAO,IAAI,CAAC,gBAAgB,EAAE,mBAAmB,CAAC,CAAC;yBACtD;wBACD,mBAAmB,EAAE,CAAC;qBACzB;iBACJ;gBACD,IAAI,OAAO,EAAE;oBAAE,SAAS;iBAAE;gBAC1B,OAAO,IAAI,CAAC,qBAAe,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,MAAI,EAAE,sBAAsB,CAAC,CAAC;YACpF,KAAK,EAAE,EAAyB,uBAAuB;gBACnD,IAAI,UAAU,IAAI,CAAC,mBAAmB,IAAI,GAAG,GAAG,CAAC,CAAC,EAAE;oBAAE,SAAS;iBAAE;gBACjE,GAAG,GAAG,KAAK,CAAC,UAAU,CAAC,mBAAmB,GAAG,CAAC,CAAC,CAAC;gBAChD,IAAI,GAAG,IAAI,EAAE,EAAE;oBACX,kBAAkB;oBAClB,KAAK,mBAAmB,GAAG,mBAAmB,GAAG,CAAC,EAAE,mBAAmB,GAAG,GAAG,EAAE,mBAAmB,EAAE,EAAE;wBAClG,GAAG,GAAG,KAAK,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC;wBAC5C,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,EAAE;4BAAE,MAAM;yBAAE;qBAC9D;iBACJ;qBAAM,IAAI,GAAG,IAAI,EAAE,EAAE;oBAClB,cAAc;oBACd,gBAAgB,GAAG,sBAAsB,GAAG,mBAAmB,CAAC;oBAChE,KAAK,mBAAmB,GAAG,mBAAmB,GAAG,CAAC,EAAE,mBAAmB,GAAG,GAAG,GAAG,CAAC,EAAE,mBAAmB,EAAE,EAAE;wBACtG,GAAG,GAAG,KAAK,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC;wBAC5C,IAAI,GAAG,IAAI,GAAG,EAAE;4BAAE,wBAAwB,GAAG,mBAAmB,CAAC;yBAAE;wBACnE,IAAI,GAAG,IAAI,EAAE,EAAE;4BAAE,SAAS;yBAAE;wBAC5B,IAAI,KAAK,CAAC,UAAU,CAAC,mBAAmB,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;4BAAE,MAAM;yBAAE;qBAClE;oBACD,IAAI,mBAAmB,IAAI,GAAG,GAAG,CAAC,EAAE;wBAChC,OAAO,IAAI,CAAC,sBAAsB,EAAE,sBAAsB,CAAC,CAAC;qBAC/D;oBACD,mBAAmB,EAAE,CAAC;iBACzB;gBACD,SAAS;YACb,KAAK,EAAE,EAAwB,4BAA4B;gBACvD,IAAI,CAAC,mBAAmB,GAAG,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,mBAAmB,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE;oBACtF,OAAO,IAAI,CAAC,gBAAgB,EAAE,mBAAmB,CAAC,CAAC;iBACtD;gBACD,SAAS;SAChB;KACJ;IAED,IAAI,KAAK,KAAK,CAAC,EAAE;QACb,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC,IAAI,CAAC,wBAAwB,GAAG,gBAAgB,CAAC,EAAE;YACnF,OAAO,IAAI,CAAC,6BAA6B,EAAE,WAAW,CAAC,CAAC;SAC3D;aAAM;YACH,OAAO,IAAI,CAAC,qBAAqB,EAAE,WAAW,CAAC,CAAC;SACnD;KACJ;SAAM,IAAI,UAAU,KAAK,CAAC,EAAE;QACzB,OAAO,IAAI,CAAC,qBAAqB,EAAE,gBAAgB,CAAC,CAAC;KACxD;IAED,SAAS,CAAC,IAAI,CAAC,CAAC;IAChB,OAAO,MAAM,CAAC;AAClB,CAAC;AAxHD,4BAwHC", "sourcesContent": ["// Split the input into chunks.\nexport default function (input, fail) {\n    const len = input.length;\n    let level = 0;\n    let parenLevel = 0;\n    let lastOpening;\n    let lastOpeningParen;\n    let lastMultiComment;\n    let lastMultiCommentEndBrace;\n    const chunks = [];\n    let emitFrom = 0;\n    let chunkerCurrentIndex;\n    let currentChunkStartIndex;\n    let cc;\n    let cc2;\n    let matched;\n\n    function emitChunk(force) {\n        const len = chunkerCurrentIndex - emitFrom;\n        if (((len < 512) && !force) || !len) {\n            return;\n        }\n        chunks.push(input.slice(emitFrom, chunkerCurrentIndex + 1));\n        emitFrom = chunkerCurrentIndex + 1;\n    }\n\n    for (chunkerCurrentIndex = 0; chunkerCurrentIndex < len; chunkerCurrentIndex++) {\n        cc = input.charCodeAt(chunkerCurrentIndex);\n        if (((cc >= 97) && (cc <= 122)) || (cc < 34)) {\n            // a-z or whitespace\n            continue;\n        }\n\n        switch (cc) {\n            case 40:                        // (\n                parenLevel++;\n                lastOpeningParen = chunkerCurrentIndex;\n                continue;\n            case 41:                        // )\n                if (--parenLevel < 0) {\n                    return fail('missing opening `(`', chunkerCurrentIndex);\n                }\n                continue;\n            case 59:                        // ;\n                if (!parenLevel) { emitChunk(); }\n                continue;\n            case 123:                       // {\n                level++;\n                lastOpening = chunkerCurrentIndex;\n                continue;\n            case 125:                       // }\n                if (--level < 0) {\n                    return fail('missing opening `{`', chunkerCurrentIndex);\n                }\n                if (!level && !parenLevel) { emitChunk(); }\n                continue;\n            case 92:                        // \\\n                if (chunkerCurrentIndex < len - 1) { chunkerCurrentIndex++; continue; }\n                return fail('unescaped `\\\\`', chunkerCurrentIndex);\n            case 34:\n            case 39:\n            case 96:                        // \", ' and `\n                matched = 0;\n                currentChunkStartIndex = chunkerCurrentIndex;\n                for (chunkerCurrentIndex = chunkerCurrentIndex + 1; chunkerCurrentIndex < len; chunkerCurrentIndex++) {\n                    cc2 = input.charCodeAt(chunkerCurrentIndex);\n                    if (cc2 > 96) { continue; }\n                    if (cc2 == cc) { matched = 1; break; }\n                    if (cc2 == 92) {        // \\\n                        if (chunkerCurrentIndex == len - 1) {\n                            return fail('unescaped `\\\\`', chunkerCurrentIndex);\n                        }\n                        chunkerCurrentIndex++;\n                    }\n                }\n                if (matched) { continue; }\n                return fail(`unmatched \\`${String.fromCharCode(cc)}\\``, currentChunkStartIndex);\n            case 47:                        // /, check for comment\n                if (parenLevel || (chunkerCurrentIndex == len - 1)) { continue; }\n                cc2 = input.charCodeAt(chunkerCurrentIndex + 1);\n                if (cc2 == 47) {\n                    // //, find lnfeed\n                    for (chunkerCurrentIndex = chunkerCurrentIndex + 2; chunkerCurrentIndex < len; chunkerCurrentIndex++) {\n                        cc2 = input.charCodeAt(chunkerCurrentIndex);\n                        if ((cc2 <= 13) && ((cc2 == 10) || (cc2 == 13))) { break; }\n                    }\n                } else if (cc2 == 42) {\n                    // /*, find */\n                    lastMultiComment = currentChunkStartIndex = chunkerCurrentIndex;\n                    for (chunkerCurrentIndex = chunkerCurrentIndex + 2; chunkerCurrentIndex < len - 1; chunkerCurrentIndex++) {\n                        cc2 = input.charCodeAt(chunkerCurrentIndex);\n                        if (cc2 == 125) { lastMultiCommentEndBrace = chunkerCurrentIndex; }\n                        if (cc2 != 42) { continue; }\n                        if (input.charCodeAt(chunkerCurrentIndex + 1) == 47) { break; }\n                    }\n                    if (chunkerCurrentIndex == len - 1) {\n                        return fail('missing closing `*/`', currentChunkStartIndex);\n                    }\n                    chunkerCurrentIndex++;\n                }\n                continue;\n            case 42:                       // *, check for unmatched */\n                if ((chunkerCurrentIndex < len - 1) && (input.charCodeAt(chunkerCurrentIndex + 1) == 47)) {\n                    return fail('unmatched `/*`', chunkerCurrentIndex);\n                }\n                continue;\n        }\n    }\n\n    if (level !== 0) {\n        if ((lastMultiComment > lastOpening) && (lastMultiCommentEndBrace > lastMultiComment)) {\n            return fail('missing closing `}` or `*/`', lastOpening);\n        } else {\n            return fail('missing closing `}`', lastOpening);\n        }\n    } else if (parenLevel !== 0) {\n        return fail('missing closing `)`', lastOpeningParen);\n    }\n\n    emitChunk(true);\n    return chunks;\n}\n"]}