{"version": 3, "file": "parser-input.js", "sourceRoot": "", "sources": ["../../../src/less/parser/parser-input.js"], "names": [], "mappings": ";;;AAAA,8DAAgC;AAEhC,mBAAe;IACX,IAAI,oBAAoB;IACpB,KAAK,CAAC;IAEV,IAAI,gBAAgB;IAChB,CAAC,CAAC;IAEN,IAAM,+BAA+B;IACjC,SAAS,GAAG,EAAE,CAAC;IAEnB,IAAI,wCAAwC;IACxC,QAAQ,CAAC;IAEb,IAAI,4DAA4D;IAC5D,4BAA4B,CAAC;IAEjC,IAAI,mBAAmB;IACnB,MAAM,CAAC;IAEX,IAAI,gBAAgB;IAChB,OAAO,CAAC;IAEZ,IAAI,qCAAqC;IACrC,UAAU,CAAC;IAEf,IAAM,WAAW,GAAG,EAAE,CAAC;IACvB,IAAM,cAAc,GAAG,EAAE,CAAC;IAC1B,IAAM,YAAY,GAAG,CAAC,CAAC;IACvB,IAAM,WAAW,GAAG,EAAE,CAAC;IACvB,IAAM,WAAW,GAAG,EAAE,CAAC;IACvB,IAAM,aAAa,GAAG,EAAE,CAAC;IACzB,IAAM,cAAc,GAAG,EAAE,CAAC;IAC1B,IAAM,sBAAsB,GAAG,EAAE,CAAC;IAClC,IAAM,UAAU,GAAG,EAAE,CAAC;IAEtB,SAAS,cAAc,CAAC,MAAM;QAC1B,IAAM,IAAI,GAAG,WAAW,CAAC,CAAC,CAAC;QAC3B,IAAM,IAAI,GAAG,CAAC,CAAC;QACf,IAAM,IAAI,GAAG,WAAW,CAAC,CAAC,GAAG,UAAU,CAAC;QACxC,IAAM,QAAQ,GAAG,WAAW,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;QACvD,IAAM,GAAG,GAAG,CAAC,WAAW,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC;QACtC,IAAM,GAAG,GAAG,KAAK,CAAC;QAClB,IAAI,CAAC,CAAC;QACN,IAAI,QAAQ,CAAC;QACb,IAAI,OAAO,CAAC;QAEZ,OAAO,WAAW,CAAC,CAAC,GAAG,QAAQ,EAAE,WAAW,CAAC,CAAC,EAAE,EAAE;YAC9C,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;YAElC,IAAI,WAAW,CAAC,iBAAiB,IAAI,CAAC,KAAK,sBAAsB,EAAE;gBAC/D,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBACzC,IAAI,QAAQ,KAAK,GAAG,EAAE;oBAClB,OAAO,GAAG,EAAC,KAAK,EAAE,WAAW,CAAC,CAAC,EAAE,aAAa,EAAE,IAAI,EAAC,CAAC;oBACtD,IAAI,WAAW,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;oBACvD,IAAI,WAAW,GAAG,CAAC,EAAE;wBACjB,WAAW,GAAG,QAAQ,CAAC;qBAC1B;oBACD,WAAW,CAAC,CAAC,GAAG,WAAW,CAAC;oBAC5B,OAAO,CAAC,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;oBACxE,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBACvC,SAAS;iBACZ;qBAAM,IAAI,QAAQ,KAAK,GAAG,EAAE;oBACzB,IAAM,aAAa,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;oBAC3D,IAAI,aAAa,IAAI,CAAC,EAAE;wBACpB,OAAO,GAAG;4BACN,KAAK,EAAE,WAAW,CAAC,CAAC;4BACpB,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,EAAE,aAAa,GAAG,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC;4BAClE,aAAa,EAAE,KAAK;yBACvB,CAAC;wBACF,WAAW,CAAC,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;wBACzC,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;wBACvC,SAAS;qBACZ;iBACJ;gBACD,MAAM;aACT;YAED,IAAI,CAAC,CAAC,KAAK,cAAc,CAAC,IAAI,CAAC,CAAC,KAAK,WAAW,CAAC,IAAI,CAAC,CAAC,KAAK,YAAY,CAAC,IAAI,CAAC,CAAC,KAAK,WAAW,CAAC,EAAE;gBAC9F,MAAM;aACT;SACJ;QAED,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,WAAW,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC;QAC7D,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC;QAE3B,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACjB,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;gBACvB,OAAO,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;gBACtB,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,yCAAyC;gBAC5D,OAAO,IAAI,CAAC,CAAC,iBAAiB;aACjC;YACD,WAAW,CAAC,QAAQ,GAAG,IAAI,CAAC;SAC/B;QAED,OAAO,IAAI,KAAK,WAAW,CAAC,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC;IAChD,CAAC;IAED,WAAW,CAAC,IAAI,GAAG;QACf,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC;QAC3B,SAAS,CAAC,IAAI,CAAE,EAAE,OAAO,SAAA,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,CAAC,GAAA,EAAE,CAAC,CAAC;IACtD,CAAC,CAAC;IACF,WAAW,CAAC,OAAO,GAAG,UAAA,oBAAoB;QAEtC,IAAI,WAAW,CAAC,CAAC,GAAG,QAAQ,IAAI,CAAC,WAAW,CAAC,CAAC,KAAK,QAAQ,IAAI,oBAAoB,IAAI,CAAC,4BAA4B,CAAC,EAAE;YACnH,QAAQ,GAAG,WAAW,CAAC,CAAC,CAAC;YACzB,4BAA4B,GAAG,oBAAoB,CAAC;SACvD;QACD,IAAM,KAAK,GAAG,SAAS,CAAC,GAAG,EAAE,CAAC;QAC9B,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;QACxB,UAAU,GAAG,WAAW,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;QACrC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;IAChB,CAAC,CAAC;IACF,WAAW,CAAC,MAAM,GAAG;QACjB,SAAS,CAAC,GAAG,EAAE,CAAC;IACpB,CAAC,CAAC;IACF,WAAW,CAAC,YAAY,GAAG,UAAA,MAAM;QAC7B,IAAM,GAAG,GAAG,WAAW,CAAC,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC;QAC1C,IAAM,IAAI,GAAG,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QACnC,OAAO,CAAC,IAAI,KAAK,cAAc,IAAI,IAAI,KAAK,WAAW,IAAI,IAAI,KAAK,YAAY,IAAI,IAAI,KAAK,WAAW,CAAC,CAAC;IAC9G,CAAC,CAAC;IAEF,2BAA2B;IAC3B,WAAW,CAAC,GAAG,GAAG,UAAA,GAAG;QACjB,IAAI,WAAW,CAAC,CAAC,GAAG,UAAU,EAAE;YAC5B,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC;YACpD,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC;SAC9B;QAED,IAAM,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC5B,IAAI,CAAC,CAAC,EAAE;YACJ,OAAO,IAAI,CAAC;SACf;QAED,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;QAC5B,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;YACvB,OAAO,CAAC,CAAC;SACZ;QAED,OAAO,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC,CAAC,CAAC;IAEF,WAAW,CAAC,KAAK,GAAG,UAAA,GAAG;QACnB,IAAI,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YACrC,OAAO,IAAI,CAAC;SACf;QACD,cAAc,CAAC,CAAC,CAAC,CAAC;QAClB,OAAO,GAAG,CAAC;IACf,CAAC,CAAC;IAEF,WAAW,CAAC,SAAS,GAAG,UAAA,GAAG;QACvB,IAAI,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YACrC,OAAO,IAAI,CAAC;SACf;QACD,OAAO,GAAG,CAAC;IACf,CAAC,CAAC;IAEF,WAAW,CAAC,IAAI,GAAG,UAAA,GAAG;QAClB,IAAM,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC;QAE7B,0CAA0C;QAC1C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;YAChC,IAAI,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;gBACnD,OAAO,IAAI,CAAC;aACf;SACJ;QAED,cAAc,CAAC,SAAS,CAAC,CAAC;QAC1B,OAAO,GAAG,CAAC;IACf,CAAC,CAAC;IAEF,WAAW,CAAC,OAAO,GAAG,UAAA,GAAG;QACrB,IAAM,GAAG,GAAG,GAAG,IAAI,WAAW,CAAC,CAAC,CAAC;QACjC,IAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAEpC,IAAI,SAAS,KAAK,IAAI,IAAI,SAAS,KAAK,GAAG,EAAE;YACzC,OAAO;SACV;QACD,IAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC5B,IAAM,eAAe,GAAG,GAAG,CAAC;QAE5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;YAC/C,IAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC;YACnD,QAAQ,QAAQ,EAAE;gBACd,KAAK,IAAI;oBACL,CAAC,EAAE,CAAC;oBACJ,SAAS;gBACb,KAAK,IAAI,CAAC;gBACV,KAAK,IAAI;oBACL,MAAM;gBACV,KAAK,SAAS,CAAC,CAAC;oBACZ,IAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;oBACjD,IAAI,CAAC,GAAG,IAAI,GAAG,KAAK,CAAC,EAAE;wBACnB,cAAc,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;wBACtB,OAAO,GAAG,CAAA;qBACb;oBACD,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;iBAC3B;gBACD,QAAQ;aACX;SACJ;QACD,OAAO,IAAI,CAAC;IAChB,CAAC,CAAC;IAEF;;;OAGG;IACH,WAAW,CAAC,WAAW,GAAG,UAAA,GAAG;QACzB,IAAI,KAAK,GAAG,EAAE,CAAC;QACf,IAAI,SAAS,GAAG,IAAI,CAAC;QACrB,IAAI,SAAS,GAAG,KAAK,CAAC;QACtB,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,IAAM,UAAU,GAAG,EAAE,CAAC;QACtB,IAAM,WAAW,GAAG,EAAE,CAAC;QACvB,IAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;QAC5B,IAAM,QAAQ,GAAG,WAAW,CAAC,CAAC,CAAC;QAC/B,IAAI,OAAO,GAAG,WAAW,CAAC,CAAC,CAAC;QAC5B,IAAI,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC;QACtB,IAAI,IAAI,GAAG,IAAI,CAAC;QAChB,IAAI,QAAQ,CAAC;QAEb,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;YACzB,QAAQ,GAAG,UAAA,IAAI,IAAI,OAAA,IAAI,KAAK,GAAG,EAAZ,CAAY,CAAA;SAClC;aAAM;YACH,QAAQ,GAAG,UAAA,IAAI,IAAI,OAAA,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAd,CAAc,CAAA;SACpC;QAED,GAAG;YACC,IAAI,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC/B,IAAI,UAAU,KAAK,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,EAAE;gBACxC,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,GAAG,OAAO,CAAC,CAAC;gBAC/C,IAAI,SAAS,EAAE;oBACX,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;iBAC/B;qBACI;oBACD,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;iBACzB;gBACD,SAAS,GAAG,WAAW,CAAC;gBACxB,cAAc,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC;gBAC7B,IAAI,GAAG,KAAK,CAAA;aACf;iBAAM;gBACH,IAAI,SAAS,EAAE;oBACX,IAAI,QAAQ,KAAK,GAAG;wBAChB,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;wBAC7B,CAAC,EAAE,CAAC;wBACJ,UAAU,EAAE,CAAC;wBACb,SAAS,GAAG,KAAK,CAAC;qBACrB;oBACD,CAAC,EAAE,CAAC;oBACJ,SAAS;iBACZ;gBACD,QAAQ,QAAQ,EAAE;oBACd,KAAK,IAAI;wBACL,CAAC,EAAE,CAAC;wBACJ,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;wBAC3B,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,GAAG,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;wBACzD,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;wBAChB,MAAM;oBACV,KAAK,GAAG;wBACJ,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;4BAC7B,CAAC,EAAE,CAAC;4BACJ,SAAS,GAAG,IAAI,CAAC;4BACjB,UAAU,EAAE,CAAC;yBAChB;wBACD,MAAM;oBACV,KAAK,IAAI,CAAC;oBACV,KAAK,GAAG;wBACJ,KAAK,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;wBAC/B,IAAI,KAAK,EAAE;4BACP,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,GAAG,OAAO,CAAC,EAAE,KAAK,CAAC,CAAC;4BAC5D,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;4BACzB,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;yBACnB;6BACI;4BACD,cAAc,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC;4BAC7B,SAAS,GAAG,QAAQ,CAAC;4BACrB,IAAI,GAAG,KAAK,CAAC;yBAChB;wBACD,MAAM;oBACV,KAAK,GAAG;wBACJ,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;wBACrB,UAAU,EAAE,CAAC;wBACb,MAAM;oBACV,KAAK,GAAG;wBACJ,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;wBACrB,UAAU,EAAE,CAAC;wBACb,MAAM;oBACV,KAAK,GAAG;wBACJ,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;wBACrB,UAAU,EAAE,CAAC;wBACb,MAAM;oBACV,KAAK,GAAG,CAAC;oBACT,KAAK,GAAG,CAAC;oBACT,KAAK,GAAG,CAAC,CAAC;wBACN,IAAM,QAAQ,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC;wBAClC,IAAI,QAAQ,KAAK,QAAQ,EAAE;4BACvB,UAAU,EAAE,CAAC;yBAChB;6BAAM;4BACH,mDAAmD;4BACnD,cAAc,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC;4BAC7B,SAAS,GAAG,QAAQ,CAAC;4BACrB,IAAI,GAAG,KAAK,CAAC;yBAChB;qBACJ;iBACJ;gBACD,CAAC,EAAE,CAAC;gBACJ,IAAI,CAAC,GAAG,MAAM,EAAE;oBACZ,IAAI,GAAG,KAAK,CAAC;iBAChB;aACJ;SACJ,QAAQ,IAAI,EAAE;QAEf,OAAO,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC;IACxC,CAAC,CAAA;IAED,WAAW,CAAC,iBAAiB,GAAG,IAAI,CAAC;IACrC,WAAW,CAAC,YAAY,GAAG,EAAE,CAAC;IAC9B,WAAW,CAAC,QAAQ,GAAG,KAAK,CAAC;IAE7B,yDAAyD;IACzD,yBAAyB;IACzB,WAAW,CAAC,IAAI,GAAG,UAAA,GAAG;QAClB,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;YACzB,0CAA0C;YAC1C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACjC,IAAI,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;oBACnD,OAAO,KAAK,CAAC;iBAChB;aACJ;YACD,OAAO,IAAI,CAAC;SACf;aAAM;YACH,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAC5B;IACL,CAAC,CAAC;IAEF,2BAA2B;IAC3B,2DAA2D;IAC3D,WAAW,CAAC,QAAQ,GAAG,UAAA,GAAG,IAAI,OAAA,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,EAAnC,CAAmC,CAAC;IAElE,WAAW,CAAC,WAAW,GAAG,cAAM,OAAA,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,EAA3B,CAA2B,CAAC;IAE5D,WAAW,CAAC,QAAQ,GAAG,cAAM,OAAA,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,EAA/B,CAA+B,CAAC;IAE7D,WAAW,CAAC,QAAQ,GAAG,cAAM,OAAA,KAAK,EAAL,CAAK,CAAC;IAEnC,WAAW,CAAC,cAAc,GAAG;QACzB,IAAM,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAC1C,0DAA0D;QAC1D,OAAO,CAAC,CAAC,GAAG,UAAU,IAAI,CAAC,GAAG,aAAa,CAAC,IAAI,CAAC,KAAK,sBAAsB,IAAI,CAAC,KAAK,cAAc,CAAC;IACzG,CAAC,CAAC;IAEF,WAAW,CAAC,KAAK,GAAG,UAAC,GAAG,EAAE,UAAU,EAAE,YAAY;QAC9C,KAAK,GAAG,GAAG,CAAC;QACZ,WAAW,CAAC,CAAC,GAAG,CAAC,GAAG,UAAU,GAAG,QAAQ,GAAG,CAAC,CAAC;QAE9C,kEAAkE;QAClE,yDAAyD;QACzD,qDAAqD;QACrD,uDAAuD;QACvD,6DAA6D;QAC7D,iCAAiC;QACjC,iEAAiE;QACjE,oDAAoD;QACpD,qEAAqE;QACrE,qDAAqD;QACrD,IAAI,UAAU,EAAE;YACZ,MAAM,GAAG,IAAA,iBAAO,EAAC,GAAG,EAAE,YAAY,CAAC,CAAC;SACvC;aAAM;YACH,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;SAClB;QAED,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QAEpB,cAAc,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC;IAEF,WAAW,CAAC,GAAG,GAAG;QACd,IAAI,OAAO,CAAC;QACZ,IAAM,UAAU,GAAG,WAAW,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC;QAEjD,IAAI,WAAW,CAAC,CAAC,GAAG,QAAQ,EAAE;YAC1B,OAAO,GAAG,4BAA4B,CAAC;YACvC,WAAW,CAAC,CAAC,GAAG,QAAQ,CAAC;SAC5B;QACD,OAAO;YACH,UAAU,YAAA;YACV,QAAQ,EAAE,WAAW,CAAC,CAAC;YACvB,4BAA4B,EAAE,OAAO;YACrC,kBAAkB,EAAE,WAAW,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC;YACrD,YAAY,EAAE,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC;SACrC,CAAC;IACN,CAAC,CAAC;IAEF,OAAO,WAAW,CAAC;AACvB,CAAC,EAAC", "sourcesContent": ["import chunker from './chunker';\n\nexport default () => {\n    let // Less input string\n        input;\n\n    let // current chunk\n        j;\n\n    const // holds state for backtracking\n        saveStack = [];\n\n    let // furthest index the parser has gone to\n        furthest;\n\n    let // if this is furthest we got to, this is the probably cause\n        furthestPossibleErrorMessage;\n\n    let // chunkified input\n        chunks;\n\n    let // current chunk\n        current;\n\n    let // index of current chunk, in `input`\n        currentPos;\n\n    const parserInput = {};\n    const CHARCODE_SPACE = 32;\n    const CHARCODE_TAB = 9;\n    const CHARCODE_LF = 10;\n    const CHARCODE_CR = 13;\n    const CHARCODE_PLUS = 43;\n    const CHARCODE_COMMA = 44;\n    const CHARCODE_FORWARD_SLASH = 47;\n    const CHARCODE_9 = 57;\n\n    function skipWhitespace(length) {\n        const oldi = parserInput.i;\n        const oldj = j;\n        const curr = parserInput.i - currentPos;\n        const endIndex = parserInput.i + current.length - curr;\n        const mem = (parserInput.i += length);\n        const inp = input;\n        let c;\n        let nextChar;\n        let comment;\n\n        for (; parserInput.i < endIndex; parserInput.i++) {\n            c = inp.charCodeAt(parserInput.i);\n\n            if (parserInput.autoCommentAbsorb && c === CHARCODE_FORWARD_SLASH) {\n                nextChar = inp.charAt(parserInput.i + 1);\n                if (nextChar === '/') {\n                    comment = {index: parserInput.i, isLineComment: true};\n                    let nextNewLine = inp.indexOf('\\n', parserInput.i + 2);\n                    if (nextNewLine < 0) {\n                        nextNewLine = endIndex;\n                    }\n                    parserInput.i = nextNewLine;\n                    comment.text = inp.substr(comment.index, parserInput.i - comment.index);\n                    parserInput.commentStore.push(comment);\n                    continue;\n                } else if (nextChar === '*') {\n                    const nextStarSlash = inp.indexOf('*/', parserInput.i + 2);\n                    if (nextStarSlash >= 0) {\n                        comment = {\n                            index: parserInput.i,\n                            text: inp.substr(parserInput.i, nextStarSlash + 2 - parserInput.i),\n                            isLineComment: false\n                        };\n                        parserInput.i += comment.text.length - 1;\n                        parserInput.commentStore.push(comment);\n                        continue;\n                    }\n                }\n                break;\n            }\n\n            if ((c !== CHARCODE_SPACE) && (c !== CHARCODE_LF) && (c !== CHARCODE_TAB) && (c !== CHARCODE_CR)) {\n                break;\n            }\n        }\n\n        current = current.slice(length + parserInput.i - mem + curr);\n        currentPos = parserInput.i;\n\n        if (!current.length) {\n            if (j < chunks.length - 1) {\n                current = chunks[++j];\n                skipWhitespace(0); // skip space at the beginning of a chunk\n                return true; // things changed\n            }\n            parserInput.finished = true;\n        }\n\n        return oldi !== parserInput.i || oldj !== j;\n    }\n\n    parserInput.save = () => {\n        currentPos = parserInput.i;\n        saveStack.push( { current, i: parserInput.i, j });\n    };\n    parserInput.restore = possibleErrorMessage => {\n\n        if (parserInput.i > furthest || (parserInput.i === furthest && possibleErrorMessage && !furthestPossibleErrorMessage)) {\n            furthest = parserInput.i;\n            furthestPossibleErrorMessage = possibleErrorMessage;\n        }\n        const state = saveStack.pop();\n        current = state.current;\n        currentPos = parserInput.i = state.i;\n        j = state.j;\n    };\n    parserInput.forget = () => {\n        saveStack.pop();\n    };\n    parserInput.isWhitespace = offset => {\n        const pos = parserInput.i + (offset || 0);\n        const code = input.charCodeAt(pos);\n        return (code === CHARCODE_SPACE || code === CHARCODE_CR || code === CHARCODE_TAB || code === CHARCODE_LF);\n    };\n\n    // Specialization of $(tok)\n    parserInput.$re = tok => {\n        if (parserInput.i > currentPos) {\n            current = current.slice(parserInput.i - currentPos);\n            currentPos = parserInput.i;\n        }\n\n        const m = tok.exec(current);\n        if (!m) {\n            return null;\n        }\n\n        skipWhitespace(m[0].length);\n        if (typeof m === 'string') {\n            return m;\n        }\n\n        return m.length === 1 ? m[0] : m;\n    };\n\n    parserInput.$char = tok => {\n        if (input.charAt(parserInput.i) !== tok) {\n            return null;\n        }\n        skipWhitespace(1);\n        return tok;\n    };\n\n    parserInput.$peekChar = tok => {\n        if (input.charAt(parserInput.i) !== tok) {\n            return null;\n        }\n        return tok;\n    };\n\n    parserInput.$str = tok => {\n        const tokLength = tok.length;\n\n        // https://jsperf.com/string-startswith/21\n        for (let i = 0; i < tokLength; i++) {\n            if (input.charAt(parserInput.i + i) !== tok.charAt(i)) {\n                return null;\n            }\n        }\n\n        skipWhitespace(tokLength);\n        return tok;\n    };\n\n    parserInput.$quoted = loc => {\n        const pos = loc || parserInput.i;\n        const startChar = input.charAt(pos);\n\n        if (startChar !== '\\'' && startChar !== '\"') {\n            return;\n        }\n        const length = input.length;\n        const currentPosition = pos;\n\n        for (let i = 1; i + currentPosition < length; i++) {\n            const nextChar = input.charAt(i + currentPosition);\n            switch (nextChar) {\n                case '\\\\':\n                    i++;\n                    continue;\n                case '\\r':\n                case '\\n':\n                    break;\n                case startChar: {\n                    const str = input.substr(currentPosition, i + 1);\n                    if (!loc && loc !== 0) {\n                        skipWhitespace(i + 1);\n                        return str\n                    }\n                    return [startChar, str];\n                }\n                default:\n            }\n        }\n        return null;\n    };\n\n    /**\n     * Permissive parsing. Ignores everything except matching {} [] () and quotes\n     * until matching token (outside of blocks)\n     */\n    parserInput.$parseUntil = tok => {\n        let quote = '';\n        let returnVal = null;\n        let inComment = false;\n        let blockDepth = 0;\n        const blockStack = [];\n        const parseGroups = [];\n        const length = input.length;\n        const startPos = parserInput.i;\n        let lastPos = parserInput.i;\n        let i = parserInput.i;\n        let loop = true;\n        let testChar;\n\n        if (typeof tok === 'string') {\n            testChar = char => char === tok\n        } else {\n            testChar = char => tok.test(char)\n        }\n\n        do {\n            let nextChar = input.charAt(i);\n            if (blockDepth === 0 && testChar(nextChar)) {\n                returnVal = input.substr(lastPos, i - lastPos);\n                if (returnVal) {\n                    parseGroups.push(returnVal);\n                }\n                else {\n                    parseGroups.push(' ');\n                }\n                returnVal = parseGroups;\n                skipWhitespace(i - startPos);\n                loop = false\n            } else {\n                if (inComment) {\n                    if (nextChar === '*' && \n                        input.charAt(i + 1) === '/') {\n                        i++;\n                        blockDepth--;\n                        inComment = false;\n                    }\n                    i++;\n                    continue;\n                }\n                switch (nextChar) {\n                    case '\\\\':\n                        i++;\n                        nextChar = input.charAt(i);\n                        parseGroups.push(input.substr(lastPos, i - lastPos + 1));\n                        lastPos = i + 1;\n                        break;\n                    case '/':\n                        if (input.charAt(i + 1) === '*') {\n                            i++;\n                            inComment = true;\n                            blockDepth++;\n                        }\n                        break;\n                    case '\\'':\n                    case '\"':\n                        quote = parserInput.$quoted(i);\n                        if (quote) {\n                            parseGroups.push(input.substr(lastPos, i - lastPos), quote);\n                            i += quote[1].length - 1;\n                            lastPos = i + 1;\n                        }\n                        else {\n                            skipWhitespace(i - startPos);\n                            returnVal = nextChar;\n                            loop = false;\n                        }\n                        break;\n                    case '{':\n                        blockStack.push('}');\n                        blockDepth++;\n                        break;\n                    case '(':\n                        blockStack.push(')');\n                        blockDepth++;\n                        break;\n                    case '[':\n                        blockStack.push(']');\n                        blockDepth++;\n                        break;\n                    case '}':\n                    case ')':\n                    case ']': {\n                        const expected = blockStack.pop();\n                        if (nextChar === expected) {\n                            blockDepth--;\n                        } else {\n                            // move the parser to the error and return expected\n                            skipWhitespace(i - startPos);\n                            returnVal = expected;\n                            loop = false;\n                        }\n                    }\n                }\n                i++;\n                if (i > length) {\n                    loop = false;\n                }\n            }\n        } while (loop);\n\n        return returnVal ? returnVal : null;\n    }\n\n    parserInput.autoCommentAbsorb = true;\n    parserInput.commentStore = [];\n    parserInput.finished = false;\n\n    // Same as $(), but don't change the state of the parser,\n    // just return the match.\n    parserInput.peek = tok => {\n        if (typeof tok === 'string') {\n            // https://jsperf.com/string-startswith/21\n            for (let i = 0; i < tok.length; i++) {\n                if (input.charAt(parserInput.i + i) !== tok.charAt(i)) {\n                    return false;\n                }\n            }\n            return true;\n        } else {\n            return tok.test(current);\n        }\n    };\n\n    // Specialization of peek()\n    // TODO remove or change some currentChar calls to peekChar\n    parserInput.peekChar = tok => input.charAt(parserInput.i) === tok;\n\n    parserInput.currentChar = () => input.charAt(parserInput.i);\n\n    parserInput.prevChar = () => input.charAt(parserInput.i - 1);\n\n    parserInput.getInput = () => input;\n\n    parserInput.peekNotNumeric = () => {\n        const c = input.charCodeAt(parserInput.i);\n        // Is the first char of the dimension 0-9, '.', '+' or '-'\n        return (c > CHARCODE_9 || c < CHARCODE_PLUS) || c === CHARCODE_FORWARD_SLASH || c === CHARCODE_COMMA;\n    };\n\n    parserInput.start = (str, chunkInput, failFunction) => {\n        input = str;\n        parserInput.i = j = currentPos = furthest = 0;\n\n        // chunking apparently makes things quicker (but my tests indicate\n        // it might actually make things slower in node at least)\n        // and it is a non-perfect parse - it can't recognise\n        // unquoted urls, meaning it can't distinguish comments\n        // meaning comments with quotes or {}() in them get 'counted'\n        // and then lead to parse errors.\n        // In addition if the chunking chunks in the wrong place we might\n        // not be able to parse a parser statement in one go\n        // this is officially deprecated but can be switched on via an option\n        // in the case it causes too much performance issues.\n        if (chunkInput) {\n            chunks = chunker(str, failFunction);\n        } else {\n            chunks = [str];\n        }\n\n        current = chunks[0];\n\n        skipWhitespace(0);\n    };\n\n    parserInput.end = () => {\n        let message;\n        const isFinished = parserInput.i >= input.length;\n\n        if (parserInput.i < furthest) {\n            message = furthestPossibleErrorMessage;\n            parserInput.i = furthest;\n        }\n        return {\n            isFinished,\n            furthest: parserInput.i,\n            furthestPossibleErrorMessage: message,\n            furthestReachedEnd: parserInput.i >= input.length - 1,\n            furthestChar: input[parserInput.i]\n        };\n    };\n\n    return parserInput;\n};\n"]}