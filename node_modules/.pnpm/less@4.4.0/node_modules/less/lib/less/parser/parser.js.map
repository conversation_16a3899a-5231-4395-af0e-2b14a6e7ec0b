{"version": 3, "file": "parser.js", "sourceRoot": "", "sources": ["../../../src/less/parser/parser.js"], "names": [], "mappings": ";;;AAAA,qEAAsC;AACtC,yDAA2B;AAC3B,iEAAmC;AACnC,wEAA4C;AAC5C,sDAAkC;AAClC,6FAA8D;AAC9D,uDAAmF;AACnF,6DAA+B;AAC/B,sEAAwC;AACxC,wEAA0C;AAE1C,EAAE;AACF,mBAAmB;AACnB,EAAE;AACF,sDAAsD;AACtD,gEAAgE;AAChE,mBAAmB;AACnB,EAAE;AACF,mEAAmE;AACnE,kCAAkC;AAClC,EAAE;AACF,yEAAyE;AACzE,mEAAmE;AACnE,kDAAkD;AAClD,iEAAiE;AACjE,8DAA8D;AAC9D,4CAA4C;AAC5C,EAAE;AACF,gEAAgE;AAChE,sEAAsE;AACtE,oEAAoE;AACpE,iCAAiC;AACjC,yEAAyE;AACzE,iEAAiE;AACjE,qEAAqE;AACrE,+EAA+E;AAC/E,+CAA+C;AAC/C,EAAE;AACF,EAAE;AACF,sEAAsE;AACtE,sEAAsE;AACtE,4DAA4D;AAC5D,EAAE;AAEF,IAAM,MAAM,GAAG,SAAS,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,YAAY;IACnE,YAAY,GAAG,YAAY,IAAI,CAAC,CAAC;IACjC,IAAI,OAAO,CAAC;IACZ,IAAM,WAAW,GAAG,IAAA,sBAAc,GAAE,CAAC;IAErC,SAAS,KAAK,CAAC,GAAG,EAAE,IAAI;QACpB,MAAM,IAAI,oBAAS,CACf;YACI,KAAK,EAAE,WAAW,CAAC,CAAC;YACpB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;YAC3B,IAAI,EAAE,IAAI,IAAI,QAAQ;YACtB,OAAO,EAAE,GAAG;SACf,EACD,OAAO,CACV,CAAC;IACN,CAAC;IAED;;;;;OAKG;IACH,SAAS,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI;QAC1B,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;YAChB,gBAAM,CAAC,IAAI,CACP,CAAC,IAAI,oBAAS,CACV;gBACI,KAAK,EAAE,KAAK,aAAL,KAAK,cAAL,KAAK,GAAI,WAAW,CAAC,CAAC;gBAC7B,QAAQ,EAAE,QAAQ,CAAC,QAAQ;gBAC3B,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,UAAG,IAAI,CAAC,WAAW,EAAE,aAAU,CAAC,CAAC,CAAC,SAAS;gBACxD,OAAO,EAAE,GAAG;aACf,EACD,OAAO,CACV,CAAC,CAAC,QAAQ,EAAE,CAChB,CAAC;SACL;IACL,CAAC;IAED,SAAS,MAAM,CAAC,GAAG,EAAE,GAAG;QACpB,0DAA0D;QAC1D,IAAM,MAAM,GAAG,CAAC,GAAG,YAAY,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACpF,IAAI,MAAM,EAAE;YACR,OAAO,MAAM,CAAC;SACjB;QAED,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,GAAG,KAAK,QAAQ;YACjC,CAAC,CAAC,oBAAa,GAAG,oBAAU,WAAW,CAAC,WAAW,EAAE,MAAG;YACxD,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC;IAC/B,CAAC;IAED,6BAA6B;IAC7B,SAAS,UAAU,CAAC,GAAG,EAAE,GAAG;QACxB,IAAI,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;YACxB,OAAO,GAAG,CAAC;SACd;QACD,KAAK,CAAC,GAAG,IAAI,oBAAa,GAAG,oBAAU,WAAW,CAAC,WAAW,EAAE,MAAG,CAAC,CAAC;IACzE,CAAC;IAED,SAAS,YAAY,CAAC,KAAK;QACvB,IAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC;QAEnC,OAAO;YACH,UAAU,EAAE,KAAK,CAAC,WAAW,CAAC,KAAK,EAAE,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC,IAAI,GAAG,CAAC;YACrE,QAAQ,EAAE,QAAQ;SACrB,CAAC;IACN,CAAC;IAED;;;;;;;OAOG;IACH,SAAS,SAAS,CAAC,GAAG,EAAE,SAAS,EAAE,QAAQ;QACvC,IAAI,MAAM,CAAC;QACX,IAAM,WAAW,GAAG,EAAE,CAAC;QACvB,IAAM,MAAM,GAAG,WAAW,CAAC;QAE3B,IAAI;YACA,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,IAAI,CAAC,GAAG,EAAE,KAAK;gBAC7C,QAAQ,CAAC;oBACL,OAAO,EAAE,GAAG;oBACZ,KAAK,EAAE,KAAK,GAAG,YAAY;iBAC9B,CAAC,CAAC;YACP,CAAC,CAAC,CAAC;YACH,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,SAAA,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;gBACxC,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;gBACtB,WAAW,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,CAAC;aACpC;YAED,IAAM,OAAO,GAAG,MAAM,CAAC,GAAG,EAAE,CAAC;YAC7B,IAAI,OAAO,CAAC,UAAU,EAAE;gBACpB,QAAQ,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;aAC/B;iBACI;gBACD,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;aACxB;SACJ;QAAC,OAAO,CAAC,EAAE;YACR,MAAM,IAAI,oBAAS,CAAC;gBAChB,KAAK,EAAE,CAAC,CAAC,KAAK,GAAG,YAAY;gBAC7B,OAAO,EAAE,CAAC,CAAC,OAAO;aACrB,EAAE,OAAO,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC;SAClC;IACL,CAAC;IAED,EAAE;IACF,aAAa;IACb,EAAE;IACF,OAAO;QACH,WAAW,aAAA;QACX,OAAO,SAAA;QACP,QAAQ,UAAA;QACR,SAAS,WAAA;QACT,EAAE;QACF,sDAAsD;QACtD,+CAA+C;QAC/C,6CAA6C;QAC7C,6GAA6G;QAC7G,EAAE;QACF,KAAK,EAAE,UAAU,GAAG,EAAE,QAAQ,EAAE,cAAc;YAC1C,IAAI,IAAI,CAAC;YACT,IAAI,GAAG,GAAG,IAAI,CAAC;YACf,IAAI,UAAU,CAAC;YACf,IAAI,UAAU,CAAC;YACf,IAAI,OAAO,CAAC;YACZ,IAAI,OAAO,GAAG,EAAE,CAAC;YAEjB,qCAAqC;YACrC,IAAI,cAAc,IAAI,cAAc,CAAC,iBAAiB,EAAE;gBACpD,OAAO,CAAC,MAAM,GAAG;oBACb,IAAI,GAAG,GAAG,WAAW,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;oBAC1C,IAAI,GAAG,EAAE;wBACL,KAAK,CAAC,0EAA0E,CAAC,CAAC;qBACrF;gBACL,CAAC,CAAA;aACJ;YAED,UAAU,GAAG,CAAC,cAAc,IAAI,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAG,MAAM,CAAC,aAAa,CAAC,cAAc,CAAC,UAAU,CAAC,OAAI,CAAC,CAAC,CAAC,EAAE,CAAC;YACzH,UAAU,GAAG,CAAC,cAAc,IAAI,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,YAAK,MAAM,CAAC,aAAa,CAAC,cAAc,CAAC,UAAU,CAAC,CAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YAEzH,IAAI,OAAO,CAAC,aAAa,EAAE;gBACvB,IAAM,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC,gBAAgB,EAAE,CAAC;gBAC/D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAC3C,GAAG,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,OAAO,SAAA,EAAE,OAAO,SAAA,EAAE,QAAQ,UAAA,EAAE,CAAC,CAAC;iBACvE;aACJ;YAED,IAAI,UAAU,IAAI,CAAC,cAAc,IAAI,cAAc,CAAC,MAAM,CAAC,EAAE;gBACzD,OAAO,GAAG,CAAC,CAAC,cAAc,IAAI,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC;gBAChG,OAAO,GAAG,OAAO,CAAC,oBAAoB,CAAC;gBACvC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAC7D,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,MAAM,CAAC;aAChD;YAED,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YAClC,uCAAuC;YACvC,GAAG,GAAG,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,GAAG,UAAU,CAAC;YACxD,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC;YAE1C,+BAA+B;YAC/B,sDAAsD;YACtD,uDAAuD;YACvD,2DAA2D;YAC3D,IAAI;gBACA,WAAW,CAAC,KAAK,CAAC,GAAG,EAAE,OAAO,CAAC,UAAU,EAAE,SAAS,IAAI,CAAC,GAAG,EAAE,KAAK;oBAC/D,MAAM,IAAI,oBAAS,CAAC;wBAChB,KAAK,OAAA;wBACL,IAAI,EAAE,OAAO;wBACb,OAAO,EAAE,GAAG;wBACZ,QAAQ,EAAE,QAAQ,CAAC,QAAQ;qBAC9B,EAAE,OAAO,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC;gBAEH,cAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC;gBACjC,IAAI,GAAG,IAAI,cAAI,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;gBACtD,cAAI,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC;gBACpC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;gBACjB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;gBACtB,IAAI,CAAC,gBAAgB,GAAG,2BAAgB,CAAC,OAAO,EAAE,CAAC;aAEtD;YAAC,OAAO,CAAC,EAAE;gBACR,OAAO,QAAQ,CAAC,IAAI,oBAAS,CAAC,CAAC,EAAE,OAAO,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;aACjE;YAED,iDAAiD;YACjD,qDAAqD;YACrD,wCAAwC;YACxC,EAAE;YACF,2CAA2C;YAC3C,mDAAmD;YACnD,wDAAwD;YACxD,gEAAgE;YAChE,IAAM,OAAO,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;YAClC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;gBAErB,IAAI,OAAO,GAAG,OAAO,CAAC,4BAA4B,CAAC;gBAEnD,IAAI,CAAC,OAAO,EAAE;oBACV,OAAO,GAAG,oBAAoB,CAAC;oBAC/B,IAAI,OAAO,CAAC,YAAY,KAAK,GAAG,EAAE;wBAC9B,OAAO,IAAI,kCAAkC,CAAC;qBACjD;yBAAM,IAAI,OAAO,CAAC,YAAY,KAAK,GAAG,EAAE;wBACrC,OAAO,IAAI,kCAAkC,CAAC;qBACjD;yBAAM,IAAI,OAAO,CAAC,kBAAkB,EAAE;wBACnC,OAAO,IAAI,8BAA8B,CAAC;qBAC7C;iBACJ;gBAED,GAAG,GAAG,IAAI,oBAAS,CAAC;oBAChB,IAAI,EAAE,OAAO;oBACb,OAAO,SAAA;oBACP,KAAK,EAAE,OAAO,CAAC,QAAQ;oBACvB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;iBAC9B,EAAE,OAAO,CAAC,CAAC;aACf;YAED,IAAM,MAAM,GAAG,UAAA,CAAC;gBACZ,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC;gBAE9B,IAAI,CAAC,EAAE;oBACH,IAAI,CAAC,CAAC,CAAC,YAAY,oBAAS,CAAC,EAAE;wBAC3B,CAAC,GAAG,IAAI,oBAAS,CAAC,CAAC,EAAE,OAAO,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC;qBACpD;oBAED,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC;iBACtB;qBACI;oBACD,OAAO,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;iBAC/B;YACL,CAAC,CAAC;YAEF,IAAI,OAAO,CAAC,cAAc,KAAK,KAAK,EAAE;gBAClC,IAAI,kBAAQ,CAAC,aAAa,CAAC,OAAO,EAAE,MAAM,CAAC;qBACtC,GAAG,CAAC,IAAI,CAAC,CAAC;aAClB;iBAAM;gBACH,OAAO,MAAM,EAAE,CAAC;aACnB;QACL,CAAC;QAED,EAAE;QACF,uCAAuC;QACvC,EAAE;QACF,kEAAkE;QAClE,EAAE;QACF,6DAA6D;QAC7D,EAAE;QACF,yBAAyB;QACzB,EAAE;QACF,cAAc;QACd,oBAAoB;QACpB,+BAA+B;QAC/B,wBAAwB;QACxB,sBAAsB;QACtB,OAAO;QACP,EAAE;QACF,kDAAkD;QAClD,EAAE;QACF,oCAAoC;QACpC,oEAAoE;QACpE,oGAAoG;QACpG,wGAAwG;QACxG,4DAA4D;QAC5D,SAAS;QACT,EAAE;QACF,iGAAiG;QACjG,6FAA6F;QAC7F,uDAAuD;QACvD,EAAE;QACF,OAAO,EAAE,OAAO,GAAG;YACf,EAAE;YACF,oEAAoE;YACpE,4DAA4D;YAC5D,EAAE;YACF,0EAA0E;YAC1E,iFAAiF;YACjF,6CAA6C;YAC7C,EAAE;YACF,2CAA2C;YAC3C,kCAAkC;YAClC,kCAAkC;YAClC,EAAE;YACF,4DAA4D;YAC5D,iCAAiC;YACjC,EAAE;YACF,OAAO,EAAE;gBACL,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;gBACzB,IAAI,IAAI,GAAG,EAAE,CAAC;gBACd,IAAI,IAAI,CAAC;gBAET,OAAO,IAAI,EAAE;oBACT,OAAO,IAAI,EAAE;wBACT,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;wBACtB,IAAI,CAAC,IAAI,EAAE;4BAAE,MAAM;yBAAE;wBACrB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;qBACnB;oBACD,sDAAsD;oBACtD,IAAI,WAAW,CAAC,QAAQ,EAAE;wBACtB,MAAM;qBACT;oBACD,IAAI,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;wBACvB,MAAM;qBACT;oBAED,IAAI,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;oBACzB,IAAI,IAAI,EAAE;wBACN,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;wBACzB,SAAS;qBACZ;oBAED,IAAI,GAAG,KAAK,CAAC,UAAU,EAAE,IAAI,IAAI,CAAC,WAAW,EAAE,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC;wBACvE,IAAI,CAAC,OAAO,EAAE,IAAI,IAAI,CAAC,YAAY,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;oBACnF,IAAI,IAAI,EAAE;wBACN,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;qBACnB;yBAAM;wBACH,IAAI,cAAc,GAAG,KAAK,CAAC;wBAC3B,OAAO,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;4BAC3B,cAAc,GAAG,IAAI,CAAC;yBACzB;wBACD,IAAI,CAAC,cAAc,EAAE;4BACjB,MAAM;yBACT;qBACJ;iBACJ;gBAED,OAAO,IAAI,CAAC;YAChB,CAAC;YAED,kFAAkF;YAClF,wCAAwC;YACxC,OAAO,EAAE;gBACL,IAAI,WAAW,CAAC,YAAY,CAAC,MAAM,EAAE;oBACjC,IAAM,OAAO,GAAG,WAAW,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;oBACjD,OAAO,IAAG,CAAC,cAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,aAAa,EAAE,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,QAAQ,CAAC,CAAC;iBACzG;YACL,CAAC;YAED,EAAE;YACF,8DAA8D;YAC9D,EAAE;YACF,QAAQ,EAAE;gBACN,WAAW,EAAE;oBACT,OAAO,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBAC1C,CAAC;gBACD,EAAE;gBACF,4CAA4C;gBAC5C,EAAE;gBACF,mCAAmC;gBACnC,EAAE;gBACF,MAAM,EAAE,UAAU,YAAY;oBAC1B,IAAI,GAAG,CAAC;oBACR,IAAM,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;oBAC5B,IAAI,SAAS,GAAG,KAAK,CAAC;oBAEtB,WAAW,CAAC,IAAI,EAAE,CAAC;oBACnB,IAAI,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;wBACxB,SAAS,GAAG,IAAI,CAAC;qBACpB;yBAAM,IAAI,YAAY,EAAE;wBACrB,WAAW,CAAC,OAAO,EAAE,CAAC;wBACtB,OAAO;qBACV;oBAED,GAAG,GAAG,WAAW,CAAC,OAAO,EAAE,CAAC;oBAC5B,IAAI,CAAC,GAAG,EAAE;wBACN,WAAW,CAAC,OAAO,EAAE,CAAC;wBACtB,OAAO;qBACV;oBACD,WAAW,CAAC,MAAM,EAAE,CAAC;oBAErB,OAAO,IAAG,CAAC,cAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,SAAS,EAAE,KAAK,GAAG,YAAY,EAAE,QAAQ,CAAC,CAAC;gBACrH,CAAC;gBAED,EAAE;gBACF,6BAA6B;gBAC7B,EAAE;gBACF,4BAA4B;gBAC5B,EAAE;gBACF,OAAO,EAAE;oBACL,IAAM,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,WAAW,CAAC,GAAG,CAAC,yDAAyD,CAAC,CAAC;oBAC/G,IAAI,CAAC,EAAE;wBACH,OAAO,cAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,IAAG,CAAC,cAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;qBAC5D;gBACL,CAAC;gBAED,EAAE;gBACF,kBAAkB;gBAClB,EAAE;gBACF,uBAAuB;gBACvB,EAAE;gBACF,iEAAiE;gBACjE,EAAE;gBACF,IAAI,EAAE;oBACF,IAAI,IAAI,CAAC;oBACT,IAAI,IAAI,CAAC;oBACT,IAAI,IAAI,CAAC;oBACT,IAAM,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;oBAE5B,uEAAuE;oBACvE,IAAI,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;wBAC7B,OAAO;qBACV;oBAED,WAAW,CAAC,IAAI,EAAE,CAAC;oBAEnB,IAAI,GAAG,WAAW,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;oBACxD,IAAI,CAAC,IAAI,EAAE;wBACP,WAAW,CAAC,MAAM,EAAE,CAAC;wBACrB,OAAO;qBACV;oBAED,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;oBACf,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;oBACjC,IAAI,IAAI,EAAE;wBACN,IAAI,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;wBACpB,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE;4BACnB,WAAW,CAAC,MAAM,EAAE,CAAC;4BACrB,OAAO,IAAI,CAAC;yBACf;qBACJ;oBAED,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;oBAE5B,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;wBACzB,WAAW,CAAC,OAAO,CAAC,iDAAiD,CAAC,CAAC;wBACvE,OAAO;qBACV;oBAED,WAAW,CAAC,MAAM,EAAE,CAAC;oBAErB,OAAO,IAAG,CAAC,cAAI,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,GAAG,YAAY,EAAE,QAAQ,CAAC,CAAC;gBACtE,CAAC;gBAED,eAAe,EAAE;oBACb,IAAI,SAAS,CAAC;oBACd,IAAI,IAAI,CAAC;oBACT,IAAM,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;oBAE5B,WAAW,CAAC,IAAI,EAAE,CAAC;oBAEnB,SAAS,GAAG,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;oBACxC,IAAI,CAAC,SAAS,EAAE;wBACZ,WAAW,CAAC,MAAM,EAAE,CAAC;wBACrB,OAAO;qBACV;oBAED,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;oBAEzD,IAAI,IAAI,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;oBAC/B,IAAI,KAAK,CAAC;oBAEV,IAAI,IAAI,EAAE;wBACN,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;qBACxB;oBAED,IAAI,IAAI,IAAI,KAAK,EAAE;wBACf,IAAI,GAAG,CAAC,IAAI,CAAC,cAAI,CAAC,WAAW,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC,GAAG,YAAY,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC;qBAC1G;oBAED,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;wBACzB,WAAW,CAAC,OAAO,CAAC,iDAAiD,CAAC,CAAC;wBACvE,OAAO;qBACV;oBAED,WAAW,CAAC,MAAM,EAAE,CAAC;oBAErB,OAAO,IAAG,CAAC,cAAI,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE,KAAK,GAAG,YAAY,EAAE,QAAQ,CAAC,CAAC;gBAC3E,CAAC;gBAED,EAAE;gBACF,4DAA4D;gBAC5D,EAAE;gBACF,0BAA0B;gBAC1B,EAAE;gBACF,8DAA8D;gBAC9D,2DAA2D;gBAC3D,EAAE;gBAEF,cAAc,EAAE,UAAU,IAAI;oBAC1B;;0DAEsC;oBACtC,OAAO;wBACH,KAAK,EAAI,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC;wBACjC,OAAO,EAAE,CAAC,CAAC,SAAS,CAAC;wBACrB,IAAI,EAAK,CAAC,CAAC,SAAS,CAAC;qBACxB,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;oBAEtB,SAAS,CAAC,CAAC,KAAK,EAAE,IAAI;wBAClB,OAAO;4BACH,KAAK,OAAA;4BACL,IAAI,MAAA,CAAG,wDAAwD;4BAC/D,oCAAoC;yBACvC,CAAC;oBACN,CAAC;oBAED,SAAS,SAAS;wBACd,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,oBAAoB,CAAC,CAAC,CAAC;oBAC7D,CAAC;gBACL,CAAC;gBAED,SAAS,EAAE,UAAU,QAAQ;oBACzB,IAAI,SAAS,GAAG,QAAQ,IAAI,EAAE,CAAC;oBAC/B,IAAM,aAAa,GAAG,EAAE,CAAC;oBACzB,IAAI,oBAAoB,CAAC;oBACzB,IAAI,KAAK,CAAC;oBAEV,WAAW,CAAC,IAAI,EAAE,CAAC;oBAEnB,OAAO,IAAI,EAAE;wBACT,IAAI,QAAQ,EAAE;4BACV,QAAQ,GAAG,KAAK,CAAC;yBACpB;6BAAM;4BACH,KAAK,GAAG,OAAO,CAAC,eAAe,EAAE,IAAI,IAAI,CAAC,UAAU,EAAE,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;4BAC/E,IAAI,CAAC,KAAK,EAAE;gCACR,MAAM;6BACT;4BAED,IAAI,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE;gCACxC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;6BAC1B;4BAED,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;yBACzB;wBAED,IAAI,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;4BACxB,SAAS;yBACZ;wBAED,IAAI,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,oBAAoB,EAAE;4BAChD,oBAAoB,GAAG,IAAI,CAAC;4BAC5B,KAAK,GAAG,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;gCACzC,CAAC,CAAC,IAAI,cAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;4BAChC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;4BAC1B,SAAS,GAAG,EAAE,CAAC;yBAClB;qBACJ;oBAED,WAAW,CAAC,MAAM,EAAE,CAAC;oBACrB,OAAO,oBAAoB,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC;gBAC5D,CAAC;gBACD,OAAO,EAAE;oBACL,OAAO,IAAI,CAAC,SAAS,EAAE;wBAChB,IAAI,CAAC,KAAK,EAAE;wBACZ,IAAI,CAAC,MAAM,EAAE;wBACb,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBACpC,CAAC;gBAED,+CAA+C;gBAC/C,2DAA2D;gBAC3D,EAAE;gBACF,sEAAsE;gBACtE,EAAE;gBAEF,UAAU,EAAE;oBACR,IAAI,GAAG,CAAC;oBACR,IAAI,KAAK,CAAC;oBACV,WAAW,CAAC,IAAI,EAAE,CAAC;oBACnB,GAAG,GAAG,WAAW,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;oBACvC,IAAI,CAAC,GAAG,EAAE;wBACN,WAAW,CAAC,OAAO,EAAE,CAAC;wBACtB,OAAO;qBACV;oBACD,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;wBACzB,WAAW,CAAC,OAAO,EAAE,CAAC;wBACtB,OAAO;qBACV;oBACD,KAAK,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;oBACzB,IAAI,KAAK,EAAE;wBACP,WAAW,CAAC,MAAM,EAAE,CAAC;wBACrB,OAAO,IAAG,CAAC,cAAI,CAAC,UAAU,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;qBAC3C;yBAAM;wBACH,WAAW,CAAC,OAAO,EAAE,CAAC;qBACzB;gBACL,CAAC;gBAED,EAAE;gBACF,qBAAqB;gBACrB,EAAE;gBACF,yEAAyE;gBACzE,4EAA4E;gBAC5E,0EAA0E;gBAC1E,EAAE;gBACF,GAAG,EAAE;oBACD,IAAI,KAAK,CAAC;oBACV,IAAM,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;oBAE5B,WAAW,CAAC,iBAAiB,GAAG,KAAK,CAAC;oBAEtC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;wBAC3B,WAAW,CAAC,iBAAiB,GAAG,IAAI,CAAC;wBACrC,OAAO;qBACV;oBAED,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,IAAI,IAAI,CAAC,QAAQ,EAAE,IAAI,IAAI,CAAC,QAAQ,EAAE;wBACnD,WAAW,CAAC,GAAG,CAAC,4BAA4B,CAAC,IAAI,EAAE,CAAC;oBAE5D,WAAW,CAAC,iBAAiB,GAAG,IAAI,CAAC;oBAErC,UAAU,CAAC,GAAG,CAAC,CAAC;oBAEhB,OAAO,IAAG,CAAC,cAAI,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,KAAK,SAAS;wBAC3C,KAAK,YAAY,cAAI,CAAC,QAAQ;wBAC9B,KAAK,YAAY,cAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;wBACjC,KAAK,CAAC,CAAC,CAAC,IAAG,CAAC,cAAI,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,KAAK,GAAG,YAAY,EAAE,QAAQ,CAAC,CAAC;gBACnF,CAAC;gBAED,EAAE;gBACF,yCAAyC;gBACzC,EAAE;gBACF,yBAAyB;gBACzB,EAAE;gBACF,sDAAsD;gBACtD,0BAA0B;gBAC1B,EAAE;gBACF,QAAQ,EAAE;oBACN,IAAI,EAAE,CAAC;oBACP,IAAI,IAAI,CAAC;oBACT,IAAM,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;oBAE5B,WAAW,CAAC,IAAI,EAAE,CAAC;oBACnB,IAAI,WAAW,CAAC,WAAW,EAAE,KAAK,GAAG,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,EAAE;wBAC7E,EAAE,GAAG,WAAW,CAAC,WAAW,EAAE,CAAC;wBAC/B,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;4BAClE,oCAAoC;4BACpC,IAAM,MAAM,GAAG,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;4BAC1C,IAAI,MAAM,EAAE;gCACR,WAAW,CAAC,MAAM,EAAE,CAAC;gCACrB,OAAO,MAAM,CAAC;6BACjB;yBACJ;wBACD,WAAW,CAAC,MAAM,EAAE,CAAC;wBACrB,OAAO,IAAG,CAAC,cAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,EAAE,KAAK,GAAG,YAAY,EAAE,QAAQ,CAAC,CAAC;qBACnE;oBACD,WAAW,CAAC,OAAO,EAAE,CAAC;gBAC1B,CAAC;gBAED,wDAAwD;gBACxD,aAAa,EAAE;oBACX,IAAI,KAAK,CAAC;oBACV,IAAM,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;oBAE5B,IAAI,WAAW,CAAC,WAAW,EAAE,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,EAAE;wBAClF,OAAO,IAAG,CAAC,cAAI,CAAC,QAAQ,CAAC,CAAC,WAAI,KAAK,CAAC,CAAC,CAAC,CAAE,EAAE,KAAK,GAAG,YAAY,EAAE,QAAQ,CAAC,CAAC;qBAC7E;gBACL,CAAC;gBACD,EAAE;gBACF,4CAA4C;gBAC5C,EAAE;gBACF,+BAA+B;gBAC/B,EAAE;gBACF,QAAQ,EAAE;oBACN,IAAI,IAAI,CAAC;oBACT,IAAM,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;oBAE5B,IAAI,WAAW,CAAC,WAAW,EAAE,KAAK,GAAG,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,EAAE;wBAC5E,OAAO,IAAG,CAAC,cAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,EAAE,KAAK,GAAG,YAAY,EAAE,QAAQ,CAAC,CAAC;qBACnE;gBACL,CAAC;gBAED,0DAA0D;gBAC1D,aAAa,EAAE;oBACX,IAAI,KAAK,CAAC;oBACV,IAAM,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;oBAE5B,IAAI,WAAW,CAAC,WAAW,EAAE,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC,EAAE;wBACnF,OAAO,IAAG,CAAC,cAAI,CAAC,QAAQ,CAAC,CAAC,WAAI,KAAK,CAAC,CAAC,CAAC,CAAE,EAAE,KAAK,GAAG,YAAY,EAAE,QAAQ,CAAC,CAAC;qBAC7E;gBACL,CAAC;gBACD,EAAE;gBACF,sBAAsB;gBACtB,EAAE;gBACF,cAAc;gBACd,EAAE;gBACF,wEAAwE;gBACxE,EAAE;gBACF,KAAK,EAAE;oBACH,IAAI,GAAG,CAAC;oBACR,WAAW,CAAC,IAAI,EAAE,CAAC;oBAEnB,IAAI,WAAW,CAAC,WAAW,EAAE,KAAK,GAAG,IAAI,CAAC,GAAG,GAAG,WAAW,CAAC,GAAG,CAAC,8DAA8D,CAAC,CAAC,EAAE;wBAC9H,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;4BACT,WAAW,CAAC,MAAM,EAAE,CAAC;4BACrB,OAAO,IAAG,CAAC,cAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;yBACrD;qBACJ;oBACD,WAAW,CAAC,OAAO,EAAE,CAAC;gBAC1B,CAAC;gBAED,YAAY,EAAE;oBACV,WAAW,CAAC,IAAI,EAAE,CAAC;oBACnB,IAAM,iBAAiB,GAAG,WAAW,CAAC,iBAAiB,CAAC;oBACxD,WAAW,CAAC,iBAAiB,GAAG,KAAK,CAAC;oBACtC,IAAM,CAAC,GAAG,WAAW,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;oBACvD,WAAW,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;oBAClD,IAAI,CAAC,CAAC,EAAE;wBACJ,WAAW,CAAC,MAAM,EAAE,CAAC;wBACrB,OAAO;qBACV;oBACD,WAAW,CAAC,OAAO,EAAE,CAAC;oBACtB,IAAM,KAAK,GAAG,cAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;oBACxC,IAAI,KAAK,EAAE;wBACP,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;wBACpB,OAAO,KAAK,CAAC;qBAChB;gBACL,CAAC;gBAED,EAAE;gBACF,4CAA4C;gBAC5C,EAAE;gBACF,gBAAgB;gBAChB,EAAE;gBACF,SAAS,EAAE;oBACP,IAAI,WAAW,CAAC,cAAc,EAAE,EAAE;wBAC9B,OAAO;qBACV;oBAED,IAAM,KAAK,GAAG,WAAW,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;oBAChE,IAAI,KAAK,EAAE;wBACP,OAAO,IAAG,CAAC,cAAI,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;qBAClD;gBACL,CAAC;gBAED,EAAE;gBACF,oDAAoD;gBACpD,EAAE;gBACF,wBAAwB;gBACxB,EAAE;gBACF,iBAAiB,EAAE;oBACf,IAAI,EAAE,CAAC;oBAEP,EAAE,GAAG,WAAW,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;oBAC3D,IAAI,EAAE,EAAE;wBACJ,OAAO,IAAG,CAAC,cAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;qBAC7C;gBACL,CAAC;gBAED,EAAE;gBACF,kCAAkC;gBAClC,EAAE;gBACF,6BAA6B;gBAC7B,EAAE;gBACF,UAAU,EAAE;oBACR,IAAI,EAAE,CAAC;oBACP,IAAM,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;oBAE5B,WAAW,CAAC,IAAI,EAAE,CAAC;oBAEnB,IAAM,MAAM,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;oBACtC,IAAM,OAAO,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;oBAEvC,IAAI,CAAC,OAAO,EAAE;wBACV,WAAW,CAAC,OAAO,EAAE,CAAC;wBACtB,OAAO;qBACV;oBAED,EAAE,GAAG,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;oBAChC,IAAI,EAAE,EAAE;wBACJ,WAAW,CAAC,MAAM,EAAE,CAAC;wBACrB,OAAO,IAAG,CAAC,cAAI,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,MAAM,CAAC,EAAE,KAAK,GAAG,YAAY,EAAE,QAAQ,CAAC,CAAC;qBAC7G;oBACD,WAAW,CAAC,OAAO,CAAC,+BAA+B,CAAC,CAAC;gBACzD,CAAC;aACJ;YAED,EAAE;YACF,wEAAwE;YACxE,EAAE;YACF,aAAa;YACb,EAAE;YACF,QAAQ,EAAE;gBACN,IAAI,IAAI,CAAC;gBAET,IAAI,WAAW,CAAC,WAAW,EAAE,KAAK,GAAG,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,EAAE;oBAAE,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC;iBAAE;YAC5G,CAAC;YAED,EAAE;YACF,uDAAuD;YACvD,8CAA8C;YAC9C,EAAE;YACF,eAAe;YACf,aAAa;YACb,4BAA4B;YAC5B,EAAE;YACF,YAAY,EAAE,UAAU,UAAU;gBAC9B,IAAI,OAAO,CAAC;gBACZ,IAAM,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC;gBACxB,IAAM,OAAO,GAAG,CAAC,CAAC,UAAU,CAAC;gBAC7B,IAAI,IAAI,GAAG,UAAU,CAAC;gBAEtB,WAAW,CAAC,IAAI,EAAE,CAAC;gBAEnB,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,GAAG;uBACvC,CAAC,IAAI,GAAG,WAAW,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC,CAAC,EAAE;oBAEtD,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;oBAEnC,IAAI,CAAC,OAAO,IAAI,CAAC,CAAC,OAAO,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,EAAE;wBAClF,WAAW,CAAC,OAAO,CAAC,2CAA2C,CAAC,CAAC;wBACjE,OAAO;qBACV;oBAED,IAAI,CAAC,OAAO,EAAE;wBACV,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;qBAClB;oBAED,IAAM,IAAI,GAAG,IAAI,cAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC;oBACtD,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,GAAG,EAAE,EAAE;wBAC3B,WAAW,CAAC,MAAM,EAAE,CAAC;wBACrB,OAAO,IAAI,CAAC;qBACf;yBACI;wBACD,WAAW,CAAC,MAAM,EAAE,CAAC;wBACrB,OAAO,IAAI,cAAI,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC;qBAC9D;iBACJ;gBAED,WAAW,CAAC,OAAO,EAAE,CAAC;YAC1B,CAAC;YAED,EAAE;YACF,2CAA2C;YAC3C,EAAE;YACF,MAAM,EAAE,UAAS,MAAM;gBACnB,IAAI,QAAQ,CAAC;gBACb,IAAI,CAAC,CAAC;gBACN,IAAM,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;gBAC5B,IAAI,MAAM,CAAC;gBACX,IAAI,UAAU,CAAC;gBACf,IAAI,MAAM,CAAC;gBAEX,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,EAAE;oBACtD,OAAO;iBACV;gBAED,GAAG;oBACC,MAAM,GAAG,IAAI,CAAC;oBACd,QAAQ,GAAG,IAAI,CAAC;oBAChB,IAAI,KAAK,GAAG,IAAI,CAAC;oBACjB,OAAO,CAAC,CAAC,MAAM,GAAG,WAAW,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC,EAAE;wBACzD,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;wBAEnB,IAAI,CAAC,CAAC,EAAE;4BACJ,MAAM;yBACT;wBACD;;;2BAGG;wBACH,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC,UAAU,CAAC,KAAK,EAAE;4BAC9B,IAAI,CAAC,uGAAuG,EAAE,KAAK,CAAC,CAAA;yBACvH;wBAED,KAAK,GAAG,KAAK,CAAC;wBACd,IAAI,QAAQ,EAAE;4BACV,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;yBACpB;6BAAM;4BACH,QAAQ,GAAG,CAAE,CAAC,CAAE,CAAC;yBACpB;qBACJ;oBAED,MAAM,GAAG,MAAM,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC;oBAC7B,IAAI,CAAC,QAAQ,EAAE;wBACX,KAAK,CAAC,wCAAwC,CAAC,CAAC;qBACnD;oBACD,MAAM,GAAG,IAAG,CAAC,cAAI,CAAC,MAAM,CAAC,CAAC,IAAG,CAAC,cAAI,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE,MAAM,EAAE,KAAK,GAAG,YAAY,EAAE,QAAQ,CAAC,CAAC;oBAChG,IAAI,UAAU,EAAE;wBACZ,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;qBAC3B;yBAAM;wBACH,UAAU,GAAG,CAAE,MAAM,CAAE,CAAC;qBAC3B;iBACJ,QAAQ,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;gBAEjC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAEd,IAAI,MAAM,EAAE;oBACR,MAAM,CAAC,IAAI,CAAC,CAAC;iBAChB;gBAED,OAAO,UAAU,CAAC;YACtB,CAAC;YAED,EAAE;YACF,iEAAiE;YACjE,EAAE;YACF,UAAU,EAAE;gBACR,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAC7B,CAAC;YAED,EAAE;YACF,SAAS;YACT,EAAE;YACF,KAAK,EAAE;gBACH,EAAE;gBACF,+CAA+C;gBAC/C,EAAE;gBACF,+BAA+B;gBAC/B,4BAA4B;gBAC5B,4BAA4B;gBAC5B,eAAe;gBACf,EAAE;gBACF,0DAA0D;gBAC1D,EAAE;gBACF,0CAA0C;gBAC1C,EAAE;gBACF,kDAAkD;gBAClD,2DAA2D;gBAC3D,oBAAoB;gBACpB,EAAE;gBACF,IAAI,EAAE,UAAU,OAAO,EAAE,SAAS;oBAC9B,IAAM,CAAC,GAAG,WAAW,CAAC,WAAW,EAAE,CAAC;oBACpC,IAAI,SAAS,GAAG,KAAK,CAAC;oBACtB,IAAI,OAAO,CAAC;oBACZ,IAAM,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;oBAC5B,IAAI,QAAQ,CAAC;oBACb,IAAI,IAAI,CAAC;oBACT,IAAI,SAAS,CAAC;oBACd,IAAI,WAAW,CAAC;oBAChB,IAAI,QAAQ,GAAG,KAAK,CAAC;oBAErB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,EAAE;wBAAE,OAAO;qBAAE;oBAEvC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,gDAAgD;oBAEpE,QAAQ,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;oBAE3B,IAAI,QAAQ,EAAE;wBACV,WAAW,GAAG,WAAW,CAAC,CAAC,CAAC;wBAC5B,IAAI,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;4BACxB,QAAQ,GAAG,WAAW,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;4BACxC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;4BAC5B,UAAU,CAAC,GAAG,CAAC,CAAC;4BAChB,SAAS,GAAG,IAAI,CAAC;4BACjB,IAAI,QAAQ,EAAE;gCACV,IAAI,CAAC,gFAAgF,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC;6BACrH;yBACJ;wBAED,IAAI,SAAS,KAAK,KAAK,EAAE;4BACrB,OAAO,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;yBAChC;wBACD,IAAI,SAAS,KAAK,IAAI,IAAI,CAAC,OAAO,EAAE;4BAChC,WAAW,CAAC,OAAO,EAAE,CAAC;4BACtB,OAAO;yBACV;wBAED,IAAI,OAAO,IAAI,CAAC,OAAO,IAAI,CAAC,SAAS,EAAE;4BACnC,yCAAyC;4BACzC,WAAW,CAAC,OAAO,EAAE,CAAC;4BACtB,OAAO;yBACV;wBAED,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,SAAS,EAAE,EAAE;4BACjC,SAAS,GAAG,IAAI,CAAC;yBACpB;wBAED,IAAI,OAAO,IAAI,OAAO,CAAC,GAAG,EAAE,EAAE;4BAC1B,WAAW,CAAC,MAAM,EAAE,CAAC;4BACrB,IAAM,KAAK,GAAG,IAAG,CAAC,cAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,GAAG,YAAY,EAAE,QAAQ,EAAE,CAAC,OAAO,IAAI,SAAS,CAAC,CAAC;4BAC1G,IAAI,OAAO,EAAE;gCACT,OAAO,IAAI,cAAI,CAAC,cAAc,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;6BAClD;iCACI;gCACD,IAAI,CAAC,SAAS,EAAE;oCACZ,IAAI,CAAC,mDAAmD,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC;iCACxF;gCACD,OAAO,KAAK,CAAC;6BAChB;yBACJ;qBACJ;oBAED,WAAW,CAAC,OAAO,EAAE,CAAC;gBAC1B,CAAC;gBACD;;;mBAGG;gBACH,QAAQ,EAAE;oBACN,IAAI,QAAQ,CAAC;oBACb,IAAI,CAAC,CAAC;oBACN,IAAI,CAAC,CAAC;oBACN,IAAI,IAAI,CAAC;oBACT,IAAI,SAAS,CAAC;oBACd,IAAM,EAAE,GAAG,uDAAuD,CAAC;oBACnE,OAAO,IAAI,EAAE;wBACT,SAAS,GAAG,WAAW,CAAC,CAAC,CAAC;wBAC1B,CAAC,GAAG,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;wBAExB,IAAI,CAAC,CAAC,EAAE;4BACJ,MAAM;yBACT;wBACD,IAAI,GAAG,IAAG,CAAC,cAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,SAAS,GAAG,YAAY,EAAE,QAAQ,CAAC,CAAC;wBAC1E,IAAI,QAAQ,EAAE;4BACV,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;yBACvB;6BAAM;4BACH,QAAQ,GAAG,CAAE,IAAI,CAAE,CAAC;yBACvB;wBACD,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;qBAC9B;oBACD,OAAO,QAAQ,CAAC;gBACpB,CAAC;gBACD,IAAI,EAAE,UAAU,MAAM;oBAClB,IAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;oBAClC,IAAM,QAAQ,GAAG,EAAE,IAAI,EAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;oBAChD,IAAI,WAAW,GAAG,EAAE,CAAC;oBACrB,IAAM,aAAa,GAAG,EAAE,CAAC;oBACzB,IAAM,SAAS,GAAG,EAAE,CAAC;oBACrB,IAAI,oBAAoB,CAAC;oBACzB,IAAI,uBAAuB,CAAC;oBAC5B,IAAI,IAAI,CAAC;oBACT,IAAI,QAAQ,CAAC;oBACb,IAAI,KAAK,CAAC;oBACV,IAAI,GAAG,CAAC;oBACR,IAAI,MAAM,CAAC;oBACX,IAAI,MAAM,GAAG,IAAI,CAAC;oBAElB,WAAW,CAAC,IAAI,EAAE,CAAC;oBAEnB,OAAO,IAAI,EAAE;wBACT,IAAI,MAAM,EAAE;4BACR,GAAG,GAAG,OAAO,CAAC,eAAe,EAAE,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;yBAC3D;6BAAM;4BACH,WAAW,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;4BACpC,IAAI,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;gCACzB,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC;gCACzB,IAAI,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,oBAAoB,EAAE;oCACjD,oBAAoB,GAAG,IAAI,CAAC;iCAC/B;gCACD,CAAC,oBAAoB,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC;qCAC7C,IAAI,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;gCAC9B,MAAM;6BACT;4BACD,GAAG,GAAG,QAAQ,CAAC,QAAQ,EAAE,IAAI,QAAQ,CAAC,QAAQ,EAAE,IAAI,QAAQ,CAAC,OAAO,EAAE,IAAI,QAAQ,CAAC,OAAO,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;yBACnH;wBAED,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE;4BACjB,MAAM;yBACT;wBAED,QAAQ,GAAG,IAAI,CAAC;wBAChB,IAAI,GAAG,CAAC,iBAAiB,EAAE;4BACvB,GAAG,CAAC,iBAAiB,EAAE,CAAC;yBAC3B;wBACD,KAAK,GAAG,GAAG,CAAC;wBACZ,IAAI,GAAG,GAAG,IAAI,CAAC;wBAEf,IAAI,MAAM,EAAE;4BACR,WAAW;4BACX,IAAI,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE;gCACpC,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;6BACtB;yBACJ;6BAAM;4BACH,GAAG,GAAG,GAAG,CAAC;yBACb;wBAED,IAAI,GAAG,IAAI,CAAC,GAAG,YAAY,cAAI,CAAC,QAAQ,IAAI,GAAG,YAAY,cAAI,CAAC,QAAQ,CAAC,EAAE;4BACvE,IAAI,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;gCACxB,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;oCACxB,IAAI,oBAAoB,EAAE;wCACtB,KAAK,CAAC,uCAAuC,CAAC,CAAC;qCAClD;oCACD,uBAAuB,GAAG,IAAI,CAAC;iCAClC;gCAED,KAAK,GAAG,OAAO,CAAC,eAAe,EAAE,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;gCAE1D,IAAI,CAAC,KAAK,EAAE;oCACR,IAAI,MAAM,EAAE;wCACR,KAAK,CAAC,+CAA+C,CAAC,CAAC;qCAC1D;yCAAM;wCACH,WAAW,CAAC,OAAO,EAAE,CAAC;wCACtB,QAAQ,CAAC,IAAI,GAAG,EAAE,CAAC;wCACnB,OAAO,QAAQ,CAAC;qCACnB;iCACJ;gCACD,QAAQ,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;6BAChC;iCAAM,IAAI,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;gCAChC,IAAI,CAAC,MAAM,EAAE;oCACT,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC;oCACzB,IAAI,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,oBAAoB,EAAE;wCACjD,oBAAoB,GAAG,IAAI,CAAC;qCAC/B;oCACD,CAAC,oBAAoB,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC;yCAC7C,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;oCAC9C,MAAM;iCACT;qCAAM;oCACH,MAAM,GAAG,IAAI,CAAC;iCACjB;6BACJ;iCAAM,IAAI,CAAC,MAAM,EAAE;gCAChB,IAAI,GAAG,QAAQ,GAAG,GAAG,CAAC,IAAI,CAAC;gCAC3B,KAAK,GAAG,IAAI,CAAC;6BAChB;yBACJ;wBAED,IAAI,KAAK,EAAE;4BACP,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;yBAC3B;wBAED,SAAS,CAAC,IAAI,CAAC,EAAE,IAAI,EAAC,QAAQ,EAAE,KAAK,OAAA,EAAE,MAAM,QAAA,EAAE,CAAC,CAAC;wBAEjD,IAAI,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;4BACxB,MAAM,GAAG,IAAI,CAAC;4BACd,SAAS;yBACZ;wBACD,MAAM,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC;wBAExC,IAAI,MAAM,IAAI,oBAAoB,EAAE;4BAEhC,IAAI,uBAAuB,EAAE;gCACzB,KAAK,CAAC,uCAAuC,CAAC,CAAC;6BAClD;4BAED,oBAAoB,GAAG,IAAI,CAAC;4BAE5B,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;gCACxB,KAAK,GAAG,IAAG,CAAC,cAAI,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,CAAC;6BACxC;4BACD,aAAa,CAAC,IAAI,CAAC,EAAE,IAAI,MAAA,EAAE,KAAK,OAAA,EAAE,MAAM,QAAA,EAAE,CAAC,CAAC;4BAE5C,IAAI,GAAG,IAAI,CAAC;4BACZ,WAAW,GAAG,EAAE,CAAC;4BACjB,uBAAuB,GAAG,KAAK,CAAC;yBACnC;qBACJ;oBAED,WAAW,CAAC,MAAM,EAAE,CAAC;oBACrB,QAAQ,CAAC,IAAI,GAAG,oBAAoB,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS,CAAC;oBACjE,OAAO,QAAQ,CAAC;gBACpB,CAAC;gBACD,EAAE;gBACF,gDAAgD;gBAChD,EAAE;gBACF,wCAAwC;gBACxC,aAAa;gBACb,QAAQ;gBACR,EAAE;gBACF,0DAA0D;gBAC1D,4DAA4D;gBAC5D,gDAAgD;gBAChD,EAAE;gBACF,4DAA4D;gBAC5D,wDAAwD;gBACxD,2DAA2D;gBAC3D,yDAAyD;gBACzD,EAAE;gBACF,8DAA8D;gBAC9D,qBAAqB;gBACrB,EAAE;gBACF,UAAU,EAAE;oBACR,IAAI,IAAI,CAAC;oBACT,IAAI,MAAM,GAAG,EAAE,CAAC;oBAChB,IAAI,KAAK,CAAC;oBACV,IAAI,OAAO,CAAC;oBACZ,IAAI,IAAI,CAAC;oBACT,IAAI,QAAQ,GAAG,KAAK,CAAC;oBACrB,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,GAAG,IAAI,WAAW,CAAC,WAAW,EAAE,KAAK,GAAG,CAAC;wBACxE,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;wBAC9B,OAAO;qBACV;oBAED,WAAW,CAAC,IAAI,EAAE,CAAC;oBAEnB,KAAK,GAAG,WAAW,CAAC,GAAG,CAAC,8DAA8D,CAAC,CAAC;oBACxF,IAAI,KAAK,EAAE;wBACP,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;wBAEhB,IAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACjC,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;wBACtB,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;wBAE5B,sBAAsB;wBACtB,wCAAwC;wBACxC,OAAO;wBACP,gCAAgC;wBAChC,oCAAoC;wBACpC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;4BACzB,WAAW,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC;4BAC7C,OAAO;yBACV;wBAED,WAAW,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;wBAEpC,IAAI,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,QAAQ;4BACpC,IAAI,GAAG,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,oBAAoB,CAAC,CAAC;yBAC3D;wBAED,OAAO,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;wBAE1B,IAAI,OAAO,EAAE;4BACT,WAAW,CAAC,MAAM,EAAE,CAAC;4BACrB,OAAO,IAAG,CAAC,cAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;yBAC5E;6BAAM;4BACH,WAAW,CAAC,OAAO,EAAE,CAAC;yBACzB;qBACJ;yBAAM;wBACH,WAAW,CAAC,OAAO,EAAE,CAAC;qBACzB;gBACL,CAAC;gBAED,WAAW,EAAE;oBACT,IAAI,IAAI,CAAC;oBACT,IAAM,OAAO,GAAG,EAAE,CAAC;oBAEnB,IAAI,WAAW,CAAC,WAAW,EAAE,KAAK,GAAG,EAAE;wBACnC,OAAO;qBACV;oBAED,OAAO,IAAI,EAAE;wBACT,WAAW,CAAC,IAAI,EAAE,CAAC;wBACnB,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;wBAC1B,IAAI,CAAC,IAAI,IAAI,IAAI,KAAK,EAAE,EAAE;4BACtB,WAAW,CAAC,OAAO,EAAE,CAAC;4BACtB,MAAM;yBACT;wBACD,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBACnB,WAAW,CAAC,MAAM,EAAE,CAAC;qBACxB;oBACD,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;wBACpB,OAAO,OAAO,CAAC;qBAClB;gBACL,CAAC;gBAED,WAAW,EAAE;oBACT,WAAW,CAAC,IAAI,EAAE,CAAC;oBAEnB,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;wBACzB,WAAW,CAAC,OAAO,EAAE,CAAC;wBACtB,OAAO;qBACV;oBAED,IAAM,IAAI,GAAG,WAAW,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;oBAE7D,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;wBACzB,WAAW,CAAC,OAAO,EAAE,CAAC;wBACtB,OAAO;qBACV;oBAED,IAAI,IAAI,IAAI,IAAI,KAAK,EAAE,EAAE;wBACrB,WAAW,CAAC,MAAM,EAAE,CAAC;wBACrB,OAAO,IAAI,CAAC;qBACf;oBAED,WAAW,CAAC,OAAO,EAAE,CAAC;gBAC1B,CAAC;aACJ;YACD,EAAE;YACF,8CAA8C;YAC9C,0CAA0C;YAC1C,EAAE;YACF,MAAM,EAAE;gBACJ,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;gBAE/B,OAAO,IAAI,CAAC,OAAO,EAAE,IAAI,QAAQ,CAAC,OAAO,EAAE,IAAI,QAAQ,CAAC,QAAQ,EAAE,IAAI,QAAQ,CAAC,GAAG,EAAE;oBAChF,QAAQ,CAAC,QAAQ,EAAE,IAAI,QAAQ,CAAC,IAAI,EAAE,IAAI,QAAQ,CAAC,OAAO,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;oBACrF,QAAQ,CAAC,UAAU,EAAE,CAAC;YAC9B,CAAC;YAED,EAAE;YACF,wEAAwE;YACxE,gFAAgF;YAChF,kCAAkC;YAClC,EAAE;YACF,GAAG,EAAE;gBACD,OAAO,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC3D,CAAC;YAED,EAAE;YACF,sBAAsB;YACtB,EAAE;YACF,wBAAwB;YACxB,EAAE;YACF,OAAO,EAAE;gBACL,IAAI,KAAK,CAAC;gBAEV,uEAAuE;gBACvE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE;oBAAE,OAAO;iBAAE;gBAC/C,KAAK,GAAG,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;gBAChC,IAAI,CAAC,KAAK,EAAE;oBACR,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,EAAE,uBAAuB,CAAC,CAAC;oBACnE,KAAK,GAAG,YAAK,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,MAAG,CAAC;iBACvC;gBACD,UAAU,CAAC,GAAG,CAAC,CAAC;gBAChB,OAAO,IAAI,cAAI,CAAC,MAAM,CAAC,EAAE,EAAE,wBAAiB,KAAK,MAAG,CAAC,CAAC;YAC1D,CAAC;YAED;;;;;;;;;;;eAWG;YACH,OAAO,EAAE;gBACL,IAAI,CAAC,CAAC;gBACN,IAAI,CAAC,CAAC;gBACN,IAAI,CAAC,CAAC;gBACN,IAAM,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;gBAE5B,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;gBAEtB,4FAA4F;gBAC5F,CAAC,GAAG,WAAW,CAAC,GAAG,CAAC,oBAAoB,CAAC;oBACrC,4CAA4C;oBAC5C,WAAW,CAAC,GAAG,CAAC,4EAA4E,CAAC;oBAC7F,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE;oBACpE,WAAW,CAAC,GAAG,CAAC,eAAe,CAAC,IAAK,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC;oBACnE,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC;gBAElC,IAAI,CAAC,CAAC,EAAE;oBACJ,WAAW,CAAC,IAAI,EAAE,CAAC;oBACnB,IAAI,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;wBACxB,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;4BAC5B,IAAI,SAAS,GAAG,EAAE,CAAC;4BACnB,OAAO,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;gCAC3B,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gCAClB,SAAS,CAAC,IAAI,CAAC,IAAI,mBAAS,CAAC,GAAG,CAAC,CAAC,CAAC;gCACnC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;6BAC5B;4BACD,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;4BAElB,IAAI,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;gCACxB,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;oCACtB,CAAC,GAAG,IAAI,CAAC,cAAI,CAAC,KAAK,CAAC,CAAC,IAAI,kBAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;iCACjD;qCAAM;oCACH,CAAC,GAAG,IAAG,CAAC,cAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;iCAC1B;gCACD,WAAW,CAAC,MAAM,EAAE,CAAC;6BACxB;iCAAM;gCACH,WAAW,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC;6BAChD;yBACJ;6BAAM;4BACH,WAAW,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC;yBAChD;qBACJ;yBAAM;wBACH,WAAW,CAAC,MAAM,EAAE,CAAC;qBACxB;iBACJ;gBAED,IAAI,CAAC,EAAE;oBAAE,OAAO,IAAG,CAAC,cAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,YAAY,cAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,YAAY,EAAE,QAAQ,CAAC,CAAC;iBAAE;YAC1G,CAAC;YAED,EAAE;YACF,wDAAwD;YACxD,EAAE;YACF,+DAA+D;YAC/D,gEAAgE;YAChE,kEAAkE;YAClE,iEAAiE;YACjE,wCAAwC;YACxC,EAAE;YACF,UAAU,EAAE;gBACR,IAAI,CAAC,GAAG,WAAW,CAAC,WAAW,EAAE,CAAC;gBAElC,IAAI,CAAC,KAAK,GAAG,EAAE;oBACX,WAAW,CAAC,IAAI,EAAE,CAAC;oBACnB,IAAM,iBAAiB,GAAG,WAAW,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;oBAC1D,IAAI,iBAAiB,EAAE;wBACnB,WAAW,CAAC,MAAM,EAAE,CAAC;wBACrB,OAAO,IAAG,CAAC,cAAI,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,CAAC;qBAClD;oBACD,WAAW,CAAC,OAAO,EAAE,CAAC;iBACzB;gBAED,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,EAAE;oBAC/D,WAAW,CAAC,CAAC,EAAE,CAAC;oBAChB,IAAI,CAAC,KAAK,GAAG,IAAI,WAAW,CAAC,WAAW,EAAE,KAAK,GAAG,EAAE;wBAChD,CAAC,GAAG,IAAI,CAAC;wBACT,WAAW,CAAC,CAAC,EAAE,CAAC;qBACnB;oBACD,OAAO,WAAW,CAAC,YAAY,EAAE,EAAE;wBAAE,WAAW,CAAC,CAAC,EAAE,CAAC;qBAAE;oBACvD,OAAO,IAAG,CAAC,cAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;iBAClC;qBAAM,IAAI,WAAW,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE;oBACrC,OAAO,IAAG,CAAC,cAAI,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC;iBACpC;qBAAM;oBACH,OAAO,IAAG,CAAC,cAAI,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC;iBACrC;YACL,CAAC;YACD,EAAE;YACF,iBAAiB;YACjB,4DAA4D;YAC5D,EAAE;YACF,wBAAwB;YACxB,iBAAiB;YACjB,EAAE;YACF,6DAA6D;YAC7D,EAAE;YACF,QAAQ,EAAE,UAAU,MAAM;gBACtB,IAAM,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;gBAC5B,IAAI,QAAQ,CAAC;gBACb,IAAI,UAAU,CAAC;gBACf,IAAI,CAAC,CAAC;gBACN,IAAI,CAAC,CAAC;gBACN,IAAI,UAAU,CAAC;gBACf,IAAI,IAAI,CAAC;gBACT,IAAI,SAAS,CAAC;gBACd,MAAM,GAAG,MAAM,KAAK,KAAK,CAAC;gBAC1B,OAAO,CAAC,MAAM,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE;oBACtH,IAAI,IAAI,EAAE;wBACN,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,oBAAoB,CAAC,CAAC;qBAC7D;yBAAM,IAAI,SAAS,EAAE;wBAClB,KAAK,CAAC,mDAAmD,CAAC,CAAC;qBAC9D;yBAAM,IAAI,UAAU,EAAE;wBACnB,IAAI,UAAU,EAAE;4BACZ,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;yBAC9C;6BAAM;4BACH,UAAU,GAAG,UAAU,CAAC;yBAC3B;qBACJ;yBAAM;wBACH,IAAI,UAAU,EAAE;4BAAE,KAAK,CAAC,gDAAgD,CAAC,CAAC;yBAAE;wBAC5E,CAAC,GAAG,WAAW,CAAC,WAAW,EAAE,CAAC;wBAC9B,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAC;4BACjB,CAAC,CAAC,OAAO,CAAC,UAAA,GAAG,IAAI,OAAA,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,EAAlB,CAAkB,CAAC,CAAC;yBACxC;wBAAC,IAAI,QAAQ,EAAE;4BACZ,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;yBACpB;6BAAM;4BACH,QAAQ,GAAG,CAAE,CAAC,CAAE,CAAC;yBACpB;wBACD,CAAC,GAAG,IAAI,CAAC;qBACZ;oBACD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,EAAE;wBAC/D,MAAM;qBACT;iBACJ;gBAED,IAAI,QAAQ,EAAE;oBAAE,OAAO,IAAG,CAAC,cAAI,CAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,KAAK,GAAG,YAAY,EAAE,QAAQ,CAAC,CAAC;iBAAE;gBAC7G,IAAI,UAAU,EAAE;oBAAE,KAAK,CAAC,wEAAwE,CAAC,CAAC;iBAAE;YACxG,CAAC;YACD,SAAS,EAAE;gBACP,IAAI,CAAC,CAAC;gBACN,IAAI,SAAS,CAAC;gBACd,OAAO,IAAI,EAAE;oBACT,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACpB,IAAI,CAAC,CAAC,EAAE;wBACJ,MAAM;qBACT;oBACD,IAAI,SAAS,EAAE;wBACX,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;qBACrB;yBAAM;wBACH,SAAS,GAAG,CAAE,CAAC,CAAE,CAAC;qBACrB;oBACD,WAAW,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;oBACpC,IAAI,CAAC,CAAC,SAAS,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;wBACrC,KAAK,CAAC,yDAAyD,CAAC,CAAC;qBACpE;oBACD,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;wBAAE,MAAM;qBAAE;oBACvC,IAAI,CAAC,CAAC,SAAS,EAAE;wBACb,KAAK,CAAC,yDAAyD,CAAC,CAAC;qBACpE;oBACD,WAAW,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;iBACvC;gBACD,OAAO,SAAS,CAAC;YACrB,CAAC;YACD,SAAS,EAAE;gBACP,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;oBAAE,OAAO;iBAAE;gBAExC,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;gBAC/B,IAAI,GAAG,CAAC;gBACR,IAAI,GAAG,CAAC;gBACR,IAAI,EAAE,CAAC;gBACP,EAAE;gBACF,wBAAwB;gBACxB,+BAA+B;gBAC/B,EAAE;gBACF,IAAI,GAAG,CAAC;gBAER,IAAI,CAAC,CAAC,GAAG,GAAG,QAAQ,CAAC,aAAa,EAAE,CAAC,EAAE;oBACnC,GAAG,GAAG,MAAM,CAAC,+CAA+C,CAAC,CAAC;iBACjE;gBAED,EAAE,GAAG,WAAW,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;gBACnC,IAAI,EAAE,EAAE;oBACJ,GAAG,GAAG,QAAQ,CAAC,MAAM,EAAE,IAAI,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,QAAQ,CAAC,aAAa,EAAE,CAAC;oBACjH,IAAI,GAAG,EAAE;wBACL,GAAG,GAAG,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;qBACpC;iBACJ;gBAED,UAAU,CAAC,GAAG,CAAC,CAAC;gBAEhB,OAAO,IAAG,CAAC,cAAI,CAAC,SAAS,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;YAClD,CAAC;YAED,EAAE;YACF,gEAAgE;YAChE,6DAA6D;YAC7D,EAAE;YACF,KAAK,EAAE;gBACH,IAAI,OAAO,CAAC;gBACZ,IAAI,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC,IAAI,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;oBAChF,OAAO,OAAO,CAAC;iBAClB;YACL,CAAC;YAED,YAAY,EAAE;gBACV,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;gBAEzB,IAAI,KAAK,EAAE;oBACP,KAAK,GAAG,IAAI,cAAI,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;iBACzC;gBACD,OAAO,KAAK,CAAC;YACjB,CAAC;YAED,eAAe,EAAE;gBACb,IAAI,OAAO,CAAC;gBACZ,IAAI,MAAM,CAAC;gBACX,IAAI,QAAQ,CAAC;gBAEb,WAAW,CAAC,IAAI,EAAE,CAAC;gBACnB,IAAI,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;oBAC5B;;;;;uBAKG;oBACH,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBACjC,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;oBACtB,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;oBAC5B,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;wBACzB,WAAW,CAAC,OAAO,EAAE,CAAC;wBACtB,OAAO;qBACV;iBACJ;gBACD,IAAM,YAAY,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;gBACzC,IAAI,YAAY,EAAE;oBACd,WAAW,CAAC,MAAM,EAAE,CAAC;oBACrB,IAAI,MAAM,EAAE;wBACR,OAAO,IAAI,cAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;qBAChF;oBACD,OAAO,IAAI,cAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;iBACjD;gBACD,WAAW,CAAC,OAAO,EAAE,CAAC;YAC1B,CAAC;YAED,EAAE;YACF,8BAA8B;YAC9B,EAAE;YACF,OAAO,EAAE;gBACL,IAAI,SAAS,CAAC;gBACd,IAAI,KAAK,CAAC;gBACV,IAAI,SAAS,CAAC;gBAEd,WAAW,CAAC,IAAI,EAAE,CAAC;gBAEnB,IAAI,OAAO,CAAC,eAAe,EAAE;oBACzB,SAAS,GAAG,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;iBAC3C;gBAED,SAAS,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;gBAE7B,IAAI,SAAS,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE;oBACrC,WAAW,CAAC,MAAM,EAAE,CAAC;oBACrB,IAAM,OAAO,GAAG,IAAG,CAAC,cAAI,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,KAAK,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;oBAC3E,IAAI,OAAO,CAAC,eAAe,EAAE;wBACzB,OAAO,CAAC,SAAS,GAAG,SAAS,CAAC;qBACjC;oBACD,OAAO,OAAO,CAAC;iBAClB;qBAAM;oBACH,WAAW,CAAC,OAAO,EAAE,CAAC;iBACzB;YACL,CAAC;YACD,WAAW,EAAE;gBACT,IAAI,IAAI,CAAC;gBACT,IAAI,KAAK,CAAC;gBACV,IAAM,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;gBAC5B,IAAI,KAAK,CAAC;gBACV,IAAM,CAAC,GAAG,WAAW,CAAC,WAAW,EAAE,CAAC;gBACpC,IAAI,SAAS,CAAC;gBACd,IAAI,KAAK,CAAC;gBACV,IAAI,UAAU,CAAC;gBAEf,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,EAAE;oBAAE,OAAO;iBAAE;gBAEjE,WAAW,CAAC,IAAI,EAAE,CAAC;gBAEnB,IAAI,GAAG,IAAI,CAAC,QAAQ,EAAE,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBAC9C,IAAI,IAAI,EAAE;oBACN,UAAU,GAAG,OAAO,IAAI,KAAK,QAAQ,CAAC;oBAEtC,IAAI,UAAU,EAAE;wBACZ,KAAK,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;wBAC/B,IAAI,KAAK,EAAE;4BACP,KAAK,GAAG,IAAI,CAAC;yBAChB;qBACJ;oBAED,WAAW,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;oBACpC,IAAI,CAAC,KAAK,EAAE;wBACR,yEAAyE;wBACzE,kEAAkE;wBAClE,qDAAqD;wBACrD,KAAK,GAAG,CAAC,UAAU,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC;wBAE3D,gDAAgD;wBAChD,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,IAAI,EAAE;4BACrD,IAAI,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;gCACxB,KAAK,GAAG,IAAI,mBAAS,CAAC,EAAE,CAAC,CAAC;6BAC7B;iCAAM;gCACH,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;6BAC9C;yBACJ;wBACD,mCAAmC;wBACnC,qEAAqE;6BAChE;4BACD,KAAK,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;yBACjC;wBACD,IAAI,KAAK,EAAE;4BACP,WAAW,CAAC,MAAM,EAAE,CAAC;4BACrB,yEAAyE;4BACzE,OAAO,IAAG,CAAC,cAAI,CAAC,WAAW,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,GAAG,YAAY,EAAE,QAAQ,CAAC,CAAC;yBAC3F;wBAED,IAAI,CAAC,KAAK,EAAE;4BACR,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;yBACxB;wBAED,IAAI,KAAK,EAAE;4BACP,SAAS,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;yBAChC;6BAAM,IAAI,UAAU,EAAE;4BACnB;;;;;+BAKG;4BACH,KAAK,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;yBAClC;qBACJ;oBAED,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,KAAK,CAAC,EAAE;wBAChC,WAAW,CAAC,MAAM,EAAE,CAAC;wBACrB,OAAO,IAAG,CAAC,cAAI,CAAC,WAAW,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,GAAG,YAAY,EAAE,QAAQ,CAAC,CAAC;qBAC/F;yBACI;wBACD,WAAW,CAAC,OAAO,EAAE,CAAC;qBACzB;iBACJ;qBAAM;oBACH,WAAW,CAAC,OAAO,EAAE,CAAC;iBACzB;YACL,CAAC;YACD,cAAc,EAAE;gBACZ,IAAM,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;gBAC5B,IAAM,KAAK,GAAG,WAAW,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;gBACzD,IAAI,KAAK,EAAE;oBACP,OAAO,IAAG,CAAC,cAAI,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,GAAG,YAAY,CAAC,CAAC;iBAC9D;YACL,CAAC;YACD;;;;;;;;;;eAUG;YACH,eAAe,EAAE,UAAU,WAAW;gBAClC,IAAI,CAAC,CAAC;gBACN,IAAI,CAAC,CAAC;gBACN,IAAI,IAAI,CAAC;gBACT,IAAI,KAAK,CAAC;gBACV,IAAM,GAAG,GAAG,WAAW,IAAI,GAAG,CAAC;gBAC/B,IAAM,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;gBAC5B,IAAM,MAAM,GAAG,EAAE,CAAC;gBAElB,SAAS,eAAe;oBACpB,IAAM,IAAI,GAAG,WAAW,CAAC,WAAW,EAAE,CAAC;oBACvC,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;wBACzB,OAAO,IAAI,KAAK,GAAG,CAAC;qBACvB;yBAAM;wBACH,OAAO,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;qBACzB;gBACL,CAAC;gBACD,IAAI,eAAe,EAAE,EAAE;oBACnB,OAAO;iBACV;gBACD,KAAK,GAAG,EAAE,CAAC;gBACX,GAAG;oBACC,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;oBACnB,IAAI,CAAC,EAAE;wBACH,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;wBACd,SAAS;qBACZ;oBACD,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;oBAClB,IAAI,CAAC,EAAE;wBACH,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;qBACjB;oBACD,IAAI,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;wBACvB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAI,CAAC,SAAS,CAAC,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;wBACrD,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;qBAC1B;iBACJ,QAAQ,CAAC,EAAE;gBAEZ,IAAI,GAAG,eAAe,EAAE,CAAC;gBAEzB,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;oBAClB,KAAK,GAAG,IAAG,CAAC,cAAI,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,CAAC;oBACpC,IAAI,IAAI,EAAE;wBACN,OAAO,KAAK,CAAC;qBAChB;yBACI;wBACD,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;qBACtB;oBACD,mDAAmD;oBACnD,IAAI,WAAW,CAAC,QAAQ,EAAE,KAAK,GAAG,EAAE;wBAChC,MAAM,CAAC,IAAI,CAAC,IAAI,cAAI,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;qBAC/C;iBACJ;gBACD,WAAW,CAAC,IAAI,EAAE,CAAC;gBAEnB,KAAK,GAAG,WAAW,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;gBAErC,IAAI,KAAK,EAAE;oBACP,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;wBAC3B,KAAK,CAAC,oBAAa,KAAK,MAAG,EAAE,OAAO,CAAC,CAAC;qBACzC;oBACD,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;wBACxC,WAAW,CAAC,MAAM,EAAE,CAAC;wBACrB,OAAO,IAAI,cAAI,CAAC,SAAS,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;qBACxC;oBACD,qBAAqB;oBACrB,IAAI,IAAI,SAAA,CAAC;oBACT,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;wBAC/B,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;wBAChB,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;4BACrB,8CAA8C;4BAC9C,MAAM,CAAC,IAAI,CAAC,IAAI,cAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC;yBACzE;6BACI;4BACD,IAAI,CAAC,KAAK,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;gCACxB,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;6BACtB;4BACD,uEAAuE;4BACvE,IAAM,KAAK,GAAG,IAAI,cAAI,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;4BACjE,IAAM,aAAa,GAAG,YAAY,CAAC;4BACnC,IAAM,SAAS,GAAG,aAAa,CAAC;4BAChC,IAAI,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gCAC1B,IAAI,CAAC,6FAA6F,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC;6BAC5H;4BACD,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gCACtB,IAAI,CAAC,uGAAuG,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC;6BACtI;4BACD,KAAK,CAAC,aAAa,GAAG,wBAAwB,CAAC;4BAC/C,KAAK,CAAC,SAAS,GAAG,0BAA0B,CAAC;4BAC7C,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;yBACtB;qBACJ;oBACD,WAAW,CAAC,MAAM,EAAE,CAAC;oBACrB,OAAO,IAAI,cAAI,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;iBAC5C;gBACD,WAAW,CAAC,OAAO,EAAE,CAAC;YAC1B,CAAC;YAED,EAAE;YACF,oBAAoB;YACpB,EAAE;YACF,qBAAqB;YACrB,EAAE;YACF,+DAA+D;YAC/D,8DAA8D;YAC9D,4DAA4D;YAC5D,+DAA+D;YAC/D,EAAE;YACF,QAAQ,EAAE;gBACN,IAAI,IAAI,CAAC;gBACT,IAAI,QAAQ,CAAC;gBACb,IAAM,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;gBAE5B,IAAM,GAAG,GAAG,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;gBAE3C,IAAI,GAAG,EAAE;oBACL,IAAM,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;oBAE1D,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,EAAE;wBACxD,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;wBAElC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;4BACzB,WAAW,CAAC,CAAC,GAAG,KAAK,CAAC;4BACtB,KAAK,CAAC,6DAA6D,CAAC,CAAC;yBACxE;wBACD,QAAQ,GAAG,QAAQ,IAAI,IAAG,CAAC,cAAI,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,CAAC;wBACjD,OAAO,IAAG,CAAC,cAAI,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,GAAG,YAAY,EAAE,QAAQ,CAAC,CAAC;qBACpF;yBACI;wBACD,WAAW,CAAC,CAAC,GAAG,KAAK,CAAC;wBACtB,KAAK,CAAC,4BAA4B,CAAC,CAAC;qBACvC;iBACJ;YACL,CAAC;YAED,aAAa,EAAE;gBACX,IAAI,CAAC,CAAC;gBACN,IAAM,OAAO,GAAG,EAAE,CAAC;gBACnB,IAAI,UAAU,CAAC;gBACf,IAAI,KAAK,CAAC;gBAEV,wCAAwC;gBACxC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;oBAAE,OAAO,IAAI,CAAC;iBAAE;gBAC7C,GAAG;oBACC,CAAC,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;oBACxB,IAAI,CAAC,EAAE;wBACH,UAAU,GAAG,CAAC,CAAC;wBACf,KAAK,GAAG,IAAI,CAAC;wBACb,QAAQ,UAAU,EAAE;4BAChB,KAAK,KAAK;gCACN,UAAU,GAAG,MAAM,CAAC;gCACpB,KAAK,GAAG,KAAK,CAAC;gCACd,MAAM;4BACV,KAAK,MAAM;gCACP,UAAU,GAAG,UAAU,CAAC;gCACxB,KAAK,GAAG,KAAK,CAAC;gCACd,MAAM;yBACb;wBACD,OAAO,CAAC,UAAU,CAAC,GAAG,KAAK,CAAC;wBAC5B,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;4BAAE,MAAM;yBAAE;qBAC1C;iBACJ,QAAQ,CAAC,EAAE;gBACZ,UAAU,CAAC,GAAG,CAAC,CAAC;gBAChB,OAAO,OAAO,CAAC;YACnB,CAAC;YAED,YAAY,EAAE;gBACV,IAAM,GAAG,GAAG,WAAW,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAC;gBACnF,IAAI,GAAG,EAAE;oBACL,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;iBACjB;YACL,CAAC;YAED,YAAY,EAAE,UAAU,aAAa;gBACjC,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;gBAC/B,IAAM,KAAK,GAAG,EAAE,CAAC;gBACjB,IAAI,CAAC,CAAC;gBACN,IAAI,CAAC,CAAC;gBACN,IAAI,MAAM,CAAC;gBACX,WAAW,CAAC,IAAI,EAAE,CAAC;gBACnB,GAAG;oBACC,CAAC,GAAG,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,QAAQ,CAAC,OAAO,EAAE,IAAI,QAAQ,CAAC,QAAQ,EAAE,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAA;oBAChH,IAAI,CAAC,EAAE;wBACH,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;qBACjB;yBAAM,IAAI,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;wBAC/B,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;wBACpB,WAAW,CAAC,IAAI,EAAE,CAAC;wBACnB,IAAI,CAAC,CAAC,IAAI,aAAa,CAAC,aAAa,IAAI,WAAW,CAAC,GAAG,CAAC,oCAAoC,CAAC,EAAE;4BAC5F,WAAW,CAAC,OAAO,EAAE,CAAC;4BACtB,CAAC,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;4BAErB,WAAW,CAAC,IAAI,EAAE,CAAC;4BACnB,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC;4BAC9C,IAAI,CAAC,MAAM,EAAE;gCACT,WAAW,CAAC,OAAO,EAAE,CAAC;6BACzB;yBACJ;6BAAM;4BACH,WAAW,CAAC,OAAO,EAAE,CAAC;4BACtB,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;yBACpB;wBACD,IAAI,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;4BACxB,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE;gCACT,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,cAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;gCACrJ,CAAC,GAAG,CAAC,CAAC;6BACT;iCAAM,IAAI,CAAC,IAAI,CAAC,EAAE;gCACf,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,cAAI,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC,GAAG,YAAY,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;6BACxH;iCAAM,IAAI,CAAC,EAAE;gCACV,KAAK,CAAC,IAAI,CAAC,IAAG,CAAC,cAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;6BAClC;iCAAM;gCACH,KAAK,CAAC,uCAAuC,CAAC,CAAC;6BAClD;yBACJ;6BAAM;4BACH,KAAK,CAAC,uBAAuB,EAAE,OAAO,CAAC,CAAC;yBAC3C;qBACJ;iBACJ,QAAQ,CAAC,EAAE;gBAEZ,WAAW,CAAC,MAAM,EAAE,CAAC;gBACrB,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;oBAClB,OAAO,IAAG,CAAC,cAAI,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,CAAC;iBACtC;YACL,CAAC;YAED,aAAa,EAAE,UAAU,aAAa;gBAClC,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;gBAC/B,IAAM,QAAQ,GAAG,EAAE,CAAC;gBACpB,IAAI,CAAC,CAAC;gBACN,GAAG;oBACC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;oBACrC,IAAI,CAAC,EAAE;wBACH,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;wBACjB,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;4BAAE,MAAM;yBAAE;qBAC1C;yBAAM;wBACH,CAAC,GAAG,QAAQ,CAAC,QAAQ,EAAE,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC;wBAClD,IAAI,CAAC,EAAE;4BACH,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;4BACjB,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;gCAAE,MAAM;6BAAE;yBAC1C;qBACJ;iBACJ,QAAQ,CAAC,EAAE;gBAEZ,OAAO,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC;YACjD,CAAC;YAED,2BAA2B,EAAE,UAAU,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE,aAAa;gBAC5E,IAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;gBAEnD,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;gBAE3B,IAAI,CAAC,KAAK,EAAE;oBACR,KAAK,CAAC,+DAA+D,CAAC,CAAC;iBAC1E;gBAED,WAAW,CAAC,MAAM,EAAE,CAAC;gBAErB,IAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,GAAG,YAAY,EAAE,QAAQ,CAAC,CAAC;gBAC/E,IAAI,OAAO,CAAC,eAAe,EAAE;oBACzB,MAAM,CAAC,SAAS,GAAG,SAAS,CAAC;iBAChC;gBAED,OAAO,MAAM,CAAC;YAClB,CAAC;YAED,cAAc,EAAE;gBACZ,IAAI,SAAS,CAAC;gBACd,IAAM,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;gBAE5B,IAAI,OAAO,CAAC,eAAe,EAAE;oBACzB,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;iBACnC;gBACD,WAAW,CAAC,IAAI,EAAE,CAAC;gBAEnB,IAAI,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;oBAC5B,IAAI,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;wBAC5B,OAAO,IAAI,CAAC,2BAA2B,CAAC,cAAI,CAAC,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,kCAAkB,CAAC,CAAC;qBAC7F;oBAED,IAAI,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE;wBAChC,OAAO,IAAI,CAAC,2BAA2B,CAAC,cAAI,CAAC,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE,sCAAsB,CAAC,CAAC;qBACrG;iBACJ;gBAED,WAAW,CAAC,OAAO,EAAE,CAAC;YAC1B,CAAC;YAED,EAAE;YAEF,2DAA2D;YAC3D,EAAE;YACF,4BAA4B;YAC5B,EAAE;YACF,MAAM,EAAE;gBACJ,IAAI,IAAI,CAAC;gBACT,IAAI,IAAI,CAAC;gBACT,IAAI,OAAO,CAAC;gBACZ,IAAM,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;gBAC5B,IAAM,GAAG,GAAK,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;gBAE7C,IAAI,GAAG,EAAE;oBACL,IAAI,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;oBAEzB,IAAI,IAAI,EAAE;wBACN,OAAO,GAAG;4BACN,UAAU,EAAE,IAAI;4BAChB,QAAQ,EAAE,IAAI;yBACjB,CAAC;qBACL;yBACI;wBACD,OAAO,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;qBAChC;oBAED,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,EAAE;wBAExD,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;4BACzB,WAAW,CAAC,CAAC,GAAG,KAAK,CAAC;4BACtB,KAAK,CAAC,+BAA+B,CAAC,CAAC;yBAC1C;wBACD,OAAO,IAAG,CAAC,cAAI,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,GAAG,YAAY,EAAE,QAAQ,CAAC,CAAC;qBAChF;yBACI;wBACD,WAAW,CAAC,CAAC,GAAG,KAAK,CAAC;wBACtB,KAAK,CAAC,6BAA6B,CAAC,CAAC;qBACxC;iBACJ;YACL,CAAC;YAED,UAAU,EAAE;gBACR,wCAAwC;gBACxC,WAAW,CAAC,IAAI,EAAE,CAAC;gBACnB,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;oBACzB,WAAW,CAAC,OAAO,EAAE,CAAC;oBACtB,OAAO,IAAI,CAAC;iBACf;gBACD,IAAM,IAAI,GAAG,WAAW,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;gBAClD,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE;oBACT,WAAW,CAAC,MAAM,EAAE,CAAC;oBACrB,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;iBACzB;qBACI;oBACD,WAAW,CAAC,OAAO,EAAE,CAAC;oBACtB,OAAO,IAAI,CAAC;iBACf;YACL,CAAC;YAED,EAAE;YACF,eAAe;YACf,EAAE;YACF,wBAAwB;YACxB,EAAE;YACF,MAAM,EAAE;gBACJ,IAAM,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;gBAC5B,IAAI,IAAI,CAAC;gBACT,IAAI,KAAK,CAAC;gBACV,IAAI,KAAK,CAAC;gBACV,IAAI,qBAAqB,CAAC;gBAC1B,IAAI,aAAa,CAAC;gBAClB,IAAI,aAAa,CAAC;gBAClB,IAAI,UAAU,CAAC;gBACf,IAAI,QAAQ,GAAG,IAAI,CAAC;gBACpB,IAAI,QAAQ,GAAG,IAAI,CAAC;gBACpB,IAAI,aAAa,GAAG,KAAK,CAAC;gBAE1B,IAAI,WAAW,CAAC,WAAW,EAAE,KAAK,GAAG,EAAE;oBAAE,OAAO;iBAAE;gBAElD,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACnE,IAAI,KAAK,EAAE;oBACP,OAAO,KAAK,CAAC;iBAChB;gBAED,WAAW,CAAC,IAAI,EAAE,CAAC;gBAEnB,IAAI,GAAG,WAAW,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;gBAEpC,IAAI,CAAC,IAAI,EAAE;oBAAE,OAAO;iBAAE;gBAEtB,qBAAqB,GAAG,IAAI,CAAC;gBAC7B,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE;oBACnD,qBAAqB,GAAG,WAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAE,CAAC;iBACtE;gBAED,QAAQ,qBAAqB,EAAE;oBAC3B,KAAK,UAAU;wBACX,aAAa,GAAG,IAAI,CAAC;wBACrB,QAAQ,GAAG,KAAK,CAAC;wBACjB,MAAM;oBACV,KAAK,YAAY;wBACb,aAAa,GAAG,IAAI,CAAC;wBACrB,QAAQ,GAAG,KAAK,CAAC;wBACjB,MAAM;oBACV,KAAK,YAAY,CAAC;oBAClB,KAAK,gBAAgB;wBACjB,aAAa,GAAG,IAAI,CAAC;wBACrB,MAAM;oBACV,KAAK,WAAW,CAAC;oBACjB,KAAK,WAAW;wBACZ,UAAU,GAAG,IAAI,CAAC;wBAClB,QAAQ,GAAG,KAAK,CAAC;wBACjB,MAAM;oBACV,KAAK,iBAAiB;wBAClB,QAAQ,GAAG,KAAK,CAAC;wBACjB,MAAM;oBACV,KAAK,QAAQ;wBACT,QAAQ,GAAG,KAAK,CAAC;wBACjB,MAAM;oBACV;wBACI,UAAU,GAAG,IAAI,CAAC;wBAClB,MAAM;iBACb;gBAED,WAAW,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;gBAEpC,IAAI,aAAa,EAAE;oBACf,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;oBACtB,IAAI,CAAC,KAAK,EAAE;wBACR,KAAK,CAAC,mBAAY,IAAI,gBAAa,CAAC,CAAC;qBACxC;iBACJ;qBAAM,IAAI,aAAa,EAAE;oBACtB,KAAK,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;oBAC1B,IAAI,CAAC,KAAK,EAAE;wBACR,KAAK,CAAC,mBAAY,IAAI,gBAAa,CAAC,CAAC;qBACxC;iBACJ;qBAAM,IAAI,UAAU,EAAE;oBACnB,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;oBACtC,QAAQ,GAAG,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,GAAG,CAAC,CAAC;oBAC/C,IAAI,CAAC,KAAK,EAAE;wBACR,IAAI,CAAC,QAAQ,IAAI,WAAW,CAAC,WAAW,EAAE,KAAK,GAAG,EAAE;4BAChD,KAAK,CAAC,UAAG,IAAI,gDAA6C,CAAC,CAAC;yBAC/D;qBACJ;yBACI,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;wBACnB,KAAK,GAAG,IAAI,CAAC;qBAChB;iBACJ;gBAED,IAAI,QAAQ,EAAE;oBACV,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;oBAE5B,WAAW,CAAC,IAAI,EAAE,CAAC;oBAEnB,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ,EAAE;wBACrB,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;wBACtB,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;qBAC/B;oBAED,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ,EAAE;wBACrB,WAAW,CAAC,OAAO,EAAE,CAAC;wBAEtB,IAAI,CAAC,GAAG,EAAE,CAAC;wBACX,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;wBAEtB,OAAO,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;4BAC3B,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;4BACd,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;yBACzB;wBAED,IAAI,KAAK,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;4BACvB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;4BACd,KAAK,GAAG,CAAC,CAAC;4BACV,aAAa,GAAG,IAAI,CAAC;yBACxB;6BAAM;4BACH,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;yBAC/B;qBACJ;yBAAM;wBACH,WAAW,CAAC,MAAM,EAAE,CAAC;qBACxB;iBACJ;gBAED,IAAI,KAAK,IAAI,aAAa,IAAI,CAAC,CAAC,QAAQ,IAAI,KAAK,IAAI,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE;oBAC1E,WAAW,CAAC,MAAM,EAAE,CAAC;oBACrB,OAAO,IAAG,CAAC,cAAI,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,GAAG,YAAY,EAAE,QAAQ,EACtE,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EACpD,QAAQ,CACX,CAAC;iBACL;gBAED,WAAW,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;YAC1D,CAAC;YAED,EAAE;YACF,mDAAmD;YACnD,EAAE;YACF,gDAAgD;YAChD,EAAE;YACF,0DAA0D;YAC1D,sBAAsB;YACtB,EAAE;YACF,KAAK,EAAE;gBACH,IAAI,CAAC,CAAC;gBACN,IAAM,WAAW,GAAG,EAAE,CAAC;gBACvB,IAAM,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;gBAE5B,GAAG;oBACC,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;oBACtB,IAAI,CAAC,EAAE;wBACH,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;wBACpB,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;4BAAE,MAAM;yBAAE;qBAC1C;iBACJ,QAAQ,CAAC,EAAE;gBAEZ,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;oBACxB,OAAO,IAAG,CAAC,cAAI,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,KAAK,GAAG,YAAY,CAAC,CAAC;iBAC7D;YACL,CAAC;YACD,SAAS,EAAE;gBACP,IAAI,WAAW,CAAC,WAAW,EAAE,KAAK,GAAG,EAAE;oBACnC,OAAO,WAAW,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;iBAC3C;YACL,CAAC;YACD,GAAG,EAAE;gBACD,IAAI,CAAC,CAAC;gBACN,IAAI,CAAC,CAAC;gBAEN,WAAW,CAAC,IAAI,EAAE,CAAC;gBACnB,IAAI,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;oBACxB,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACpB,IAAI,CAAC,IAAI,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;wBAC7B,WAAW,CAAC,MAAM,EAAE,CAAC;wBACrB,CAAC,GAAG,IAAG,CAAC,cAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC9B,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC;wBAChB,OAAO,CAAC,CAAC;qBACZ;oBACD,WAAW,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;oBACtC,OAAO;iBACV;gBACD,WAAW,CAAC,OAAO,EAAE,CAAC;YAC1B,CAAC;YACD,YAAY,EAAE;gBACV,WAAW,CAAC,IAAI,EAAE,CAAC;gBAEnB,4BAA4B;gBAC5B,IAAM,KAAK,GAAG,WAAW,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;gBAC/C,IAAI,KAAK,EAAE;oBACP,OAAO,IAAI,cAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;iBACrC;gBAED,WAAW,CAAC,OAAO,EAAE,CAAC;YAC1B,CAAC;YACD,cAAc,EAAE;gBACZ,IAAI,CAAC,CAAC;gBACN,IAAI,CAAC,CAAC;gBACN,IAAI,EAAE,CAAC;gBACP,IAAI,SAAS,CAAC;gBACd,IAAI,QAAQ,CAAC;gBACb,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;gBACnB,IAAI,CAAC,EAAE;oBACH,QAAQ,GAAG,WAAW,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;oBACxC,OAAO,IAAI,EAAE;wBACT,IAAI,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;4BAC7B,MAAM;yBACT;wBAED,WAAW,CAAC,IAAI,EAAE,CAAC;wBAEnB,EAAE,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;wBACtD,IAAI,CAAC,EAAE,EAAE;4BACL,IAAI,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;4BAC1B,EAAE,GAAG,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;4BAC5B,IAAI,EAAE,EAAE;gCACJ,IAAI,CAAC,2BAA2B,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC;6BAC1D;yBACJ;wBAED,IAAI,CAAC,EAAE,EAAE;4BAAE,WAAW,CAAC,MAAM,EAAE,CAAC;4BAAC,MAAM;yBAAE;wBAEzC,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;wBAEnB,IAAI,CAAC,CAAC,EAAE;4BAAE,WAAW,CAAC,OAAO,EAAE,CAAC;4BAAC,MAAM;yBAAE;wBACzC,WAAW,CAAC,MAAM,EAAE,CAAC;wBAErB,CAAC,CAAC,UAAU,GAAG,IAAI,CAAC;wBACpB,CAAC,CAAC,UAAU,GAAG,IAAI,CAAC;wBACpB,SAAS,GAAG,IAAG,CAAC,cAAI,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,SAAS,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;wBACnE,QAAQ,GAAG,WAAW,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;qBAC3C;oBACD,OAAO,SAAS,IAAI,CAAC,CAAC;iBACzB;YACL,CAAC;YACD,QAAQ,EAAE;gBACN,IAAI,CAAC,CAAC;gBACN,IAAI,CAAC,CAAC;gBACN,IAAI,EAAE,CAAC;gBACP,IAAI,SAAS,CAAC;gBACd,IAAI,QAAQ,CAAC;gBACb,CAAC,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;gBAC1B,IAAI,CAAC,EAAE;oBACH,QAAQ,GAAG,WAAW,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;oBACxC,OAAO,IAAI,EAAE;wBACT,EAAE,GAAG,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,QAAQ,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;wBACtG,IAAI,CAAC,EAAE,EAAE;4BACL,MAAM;yBACT;wBACD,CAAC,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;wBAC1B,IAAI,CAAC,CAAC,EAAE;4BACJ,MAAM;yBACT;wBAED,CAAC,CAAC,UAAU,GAAG,IAAI,CAAC;wBACpB,CAAC,CAAC,UAAU,GAAG,IAAI,CAAC;wBACpB,SAAS,GAAG,IAAG,CAAC,cAAI,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,SAAS,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;wBACnE,QAAQ,GAAG,WAAW,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;qBAC3C;oBACD,OAAO,SAAS,IAAI,CAAC,CAAC;iBACzB;YACL,CAAC;YACD,UAAU,EAAE;gBACR,IAAI,CAAC,CAAC;gBACN,IAAI,CAAC,CAAC;gBACN,IAAM,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;gBAC5B,IAAI,SAAS,CAAC;gBAEd,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBACzB,IAAI,CAAC,EAAE;oBACH,OAAO,IAAI,EAAE;wBACT,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;4BAClE,MAAM;yBACT;wBACD,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;wBACzB,IAAI,CAAC,CAAC,EAAE;4BACJ,MAAM;yBACT;wBACD,SAAS,GAAG,IAAG,CAAC,cAAI,CAAC,SAAS,CAAC,CAAC,IAAI,EAAE,SAAS,IAAI,CAAC,EAAE,CAAC,EAAE,KAAK,GAAG,YAAY,CAAC,CAAC;qBAClF;oBACD,OAAO,SAAS,IAAI,CAAC,CAAC;iBACzB;YACL,CAAC;YACD,SAAS,EAAE,UAAU,WAAW;gBAC5B,IAAI,MAAM,CAAC;gBACX,IAAI,OAAO,CAAC;gBACZ,IAAI,IAAI,CAAC;gBACT,SAAS,EAAE;oBACP,OAAO,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAClC,CAAC;gBAED,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;gBACxC,IAAI,CAAC,MAAM,EAAE;oBACT,OAAQ;iBACX;gBACD,OAAO,GAAG,EAAE,EAAE,CAAC;gBACf,IAAI,OAAO,EAAE;oBACT,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;oBACnC,IAAI,IAAI,EAAE;wBACN,MAAM,GAAG,IAAG,CAAC,cAAI,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;qBACvD;yBAAM;wBACH,OAAQ;qBACX;iBACJ;gBACD,OAAO,MAAM,CAAC;YAClB,CAAC;YACD,YAAY,EAAE,UAAU,WAAW;gBAC/B,IAAI,MAAM,CAAC;gBACX,IAAI,OAAO,CAAC;gBACZ,IAAI,IAAI,CAAC;gBACT,IAAM,IAAI,GAAG,IAAI,CAAC;gBAClB,SAAS,eAAe;oBACpB,IAAM,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;oBAC1F,IAAI,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE;wBACvB,OAAO,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;qBAC5C;oBACD,OAAO,IAAI,CAAC;gBAChB,CAAC;gBACD,SAAS,GAAG;oBACR,OAAO,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACnC,CAAC;gBAED,MAAM,GAAG,eAAe,EAAE,CAAC;gBAC3B,IAAI,CAAC,MAAM,EAAE;oBACT,OAAQ;iBACX;gBACD,OAAO,GAAG,GAAG,EAAE,CAAC;gBAChB,IAAI,OAAO,EAAE;oBACT,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;oBACtC,IAAI,IAAI,EAAE;wBACN,MAAM,GAAG,IAAG,CAAC,cAAI,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;qBACvD;yBAAM;wBACH,OAAQ;qBACX;iBACJ;gBACD,OAAO,MAAM,CAAC;YAClB,CAAC;YACD,gBAAgB,EAAE,UAAU,WAAW;gBACnC,IAAI,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;oBACzB,IAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;oBACtD,IAAI,MAAM,EAAE;wBACR,MAAM,CAAC,MAAM,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC;qBAClC;oBACD,OAAO,MAAM,CAAC;iBACjB;YACL,CAAC;YACD,oBAAoB,EAAE,UAAU,WAAW;gBACvC,SAAS,iCAAiC,CAAC,EAAE;oBACzC,IAAI,IAAI,CAAC;oBACT,WAAW,CAAC,IAAI,EAAE,CAAC;oBACnB,IAAI,GAAG,EAAE,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;oBACjC,IAAI,CAAC,IAAI,EAAE;wBACP,WAAW,CAAC,OAAO,EAAE,CAAC;wBACtB,OAAQ;qBACX;oBACD,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;wBACzB,WAAW,CAAC,OAAO,EAAE,CAAC;wBACtB,OAAQ;qBACX;oBACD,WAAW,CAAC,MAAM,EAAE,CAAC;oBACrB,OAAO,IAAI,CAAC;gBAChB,CAAC;gBAED,IAAI,IAAI,CAAC;gBACT,WAAW,CAAC,IAAI,EAAE,CAAC;gBACnB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;oBACxB,WAAW,CAAC,OAAO,EAAE,CAAC;oBACtB,OAAQ;iBACX;gBACD,IAAI,GAAG,iCAAiC,CAAC,IAAI,CAAC,CAAC;gBAC/C,IAAI,IAAI,EAAE;oBACN,WAAW,CAAC,MAAM,EAAE,CAAC;oBACrB,OAAO,IAAI,CAAC;iBACf;gBAED,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;gBACzC,IAAI,CAAC,IAAI,EAAE;oBACP,WAAW,CAAC,OAAO,EAAE,CAAC;oBACtB,OAAQ;iBACX;gBACD,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;oBACzB,WAAW,CAAC,OAAO,CAAC,4BAAqB,WAAW,CAAC,WAAW,EAAE,MAAG,CAAC,CAAC;oBACvE,OAAQ;iBACX;gBACD,WAAW,CAAC,MAAM,EAAE,CAAC;gBACrB,OAAO,IAAI,CAAC;YAChB,CAAC;YACD,eAAe,EAAE,UAAU,WAAW,EAAE,aAAa;gBACjD,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;gBAC/B,IAAM,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;gBAC5B,IAAI,CAAC,CAAC;gBACN,IAAI,CAAC,CAAC;gBACN,IAAI,CAAC,CAAC;gBACN,IAAI,EAAE,CAAC;gBAEP,IAAM,IAAI,GAAG,CAAC;oBACV,OAAO,IAAI,CAAC,QAAQ,EAAE,IAAI,QAAQ,CAAC,OAAO,EAAE,IAAI,QAAQ,CAAC,MAAM,EAAE,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC;gBAChG,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gBAEb,IAAI,aAAa,EAAE;oBACf,CAAC,GAAG,aAAa,CAAC;iBACrB;qBAAM;oBACH,CAAC,GAAG,IAAI,EAAE,CAAC;iBACd;gBAED,IAAI,CAAC,EAAE;oBACH,IAAI,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;wBACxB,IAAI,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;4BACxB,EAAE,GAAG,IAAI,CAAC;yBACb;6BAAM;4BACH,EAAE,GAAG,GAAG,CAAC;yBACZ;qBACJ;yBACD,IAAI,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;wBACxB,IAAI,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;4BACxB,EAAE,GAAG,IAAI,CAAC;yBACb;6BAAM;4BACH,EAAE,GAAG,GAAG,CAAC;yBACZ;qBACJ;yBACD,IAAI,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;wBACxB,IAAI,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;4BACxB,EAAE,GAAG,IAAI,CAAC;yBACb;6BAAM,IAAI,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;4BAC/B,EAAE,GAAG,IAAI,CAAC;yBACb;6BAAM;4BACH,EAAE,GAAG,GAAG,CAAC;yBACZ;qBACJ;oBACD,IAAI,EAAE,EAAE;wBACJ,CAAC,GAAG,IAAI,EAAE,CAAC;wBACX,IAAI,CAAC,EAAE;4BACH,CAAC,GAAG,IAAG,CAAC,cAAI,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,GAAG,YAAY,EAAE,KAAK,CAAC,CAAC;yBAClE;6BAAM;4BACH,KAAK,CAAC,qBAAqB,CAAC,CAAC;yBAChC;qBACJ;yBAAM,IAAI,CAAC,aAAa,EAAE;wBACvB,CAAC,GAAG,IAAG,CAAC,cAAI,CAAC,SAAS,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,IAAG,CAAC,cAAI,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,EAAE,KAAK,GAAG,YAAY,EAAE,KAAK,CAAC,CAAC;qBAC3F;oBACD,OAAO,CAAC,CAAC;iBACZ;YACL,CAAC;YAED,EAAE;YACF,2DAA2D;YAC3D,iCAAiC;YACjC,EAAE;YACF,OAAO,EAAE;gBACL,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;gBAC/B,IAAI,MAAM,CAAC;gBAEX,IAAI,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;oBAC7B,MAAM,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;iBACnC;gBAED,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,QAAQ,CAAC,SAAS,EAAE;oBAClC,QAAQ,CAAC,KAAK,EAAE,IAAI,QAAQ,CAAC,QAAQ,EAAE;oBACvC,QAAQ,CAAC,QAAQ,EAAE,IAAI,QAAQ,CAAC,IAAI,EAAE;oBACtC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,YAAY,EAAE;oBAChD,IAAI,CAAC,YAAY,EAAE,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC;gBAEtD,IAAI,MAAM,EAAE;oBACR,CAAC,CAAC,UAAU,GAAG,IAAI,CAAC;oBACpB,CAAC,GAAG,IAAG,CAAC,cAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;iBAC7B;gBAED,OAAO,CAAC,CAAC;YACb,CAAC;YAED,EAAE;YACF,wDAAwD;YACxD,qCAAqC;YACrC,EAAE;YACF,sBAAsB;YACtB,eAAe;YACf,EAAE;YACF,UAAU,EAAE;gBACR,IAAM,QAAQ,GAAG,EAAE,CAAC;gBACpB,IAAI,CAAC,CAAC;gBACN,IAAI,KAAK,CAAC;gBACV,IAAM,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;gBAE5B,GAAG;oBACC,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;oBACnB,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,EAAE;wBACvB,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;wBACjB,SAAS;qBACZ;oBACD,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;oBAErC,IAAI,CAAC,YAAY,cAAI,CAAC,OAAO,EAAE;wBAC3B,CAAC,GAAG,IAAI,CAAC;qBACZ;oBAED,IAAI,CAAC,EAAE;wBACH,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;wBACjB,0FAA0F;wBAC1F,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;4BAC9B,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;4BAC/B,IAAI,KAAK,EAAE;gCACP,QAAQ,CAAC,IAAI,CAAC,IAAG,CAAC,cAAI,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,KAAK,GAAG,YAAY,CAAC,CAAC,CAAC;6BACnE;yBACJ;qBACJ;iBACJ,QAAQ,CAAC,EAAE;gBACZ,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;oBACrB,OAAO,IAAG,CAAC,cAAI,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,CAAC;iBACzC;YACL,CAAC;YACD,QAAQ,EAAE;gBACN,IAAM,IAAI,GAAG,WAAW,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;gBAC3D,IAAI,IAAI,EAAE;oBACN,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC;iBAClB;YACL,CAAC;YACD,YAAY,EAAE;gBACV,IAAI,IAAI,GAAG,EAAE,CAAC;gBACd,IAAM,KAAK,GAAG,EAAE,CAAC;gBACjB,IAAI,CAAC,CAAC;gBACN,IAAI,CAAC,CAAC;gBAEN,WAAW,CAAC,IAAI,EAAE,CAAC;gBAEnB,IAAM,cAAc,GAAG,WAAW,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;gBAChE,IAAI,cAAc,EAAE;oBAChB,IAAI,GAAG,CAAC,IAAG,CAAC,cAAI,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC9C,WAAW,CAAC,MAAM,EAAE,CAAC;oBACrB,OAAO,IAAI,CAAC;iBACf;gBAED,SAAS,KAAK,CAAC,EAAE;oBACb,IAAM,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC;oBACxB,IAAM,KAAK,GAAG,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;oBAClC,IAAI,KAAK,EAAE;wBACP,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;wBACd,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;qBAC9B;gBACL,CAAC;gBAED,KAAK,CAAC,QAAQ,CAAC,CAAC;gBAChB,OAAO,IAAI,EAAE;oBACT,IAAI,CAAC,KAAK,CAAC,kCAAkC,CAAC,EAAE;wBAC5C,MAAM;qBACT;iBACJ;gBAED,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,oBAAoB,CAAC,EAAE;oBAClD,WAAW,CAAC,MAAM,EAAE,CAAC;oBAErB,yDAAyD;oBACzD,qDAAqD;oBACrD,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;wBAChB,IAAI,CAAC,KAAK,EAAE,CAAC;wBACb,KAAK,CAAC,KAAK,EAAE,CAAC;qBACjB;oBACD,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;wBAC9B,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;wBACZ,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;4BACpD,IAAG,CAAC,cAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;4BACtB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC;gCAClB,IAAG,CAAC,cAAI,CAAC,QAAQ,CAAC,CAAC,WAAI,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAE,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,YAAY,EAAE,QAAQ,CAAC,CAAC,CAAC;gCAC7E,IAAG,CAAC,cAAI,CAAC,QAAQ,CAAC,CAAC,WAAI,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAE,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,YAAY,EAAE,QAAQ,CAAC,CAAC,CAAC;qBACxF;oBACD,OAAO,IAAI,CAAC;iBACf;gBACD,WAAW,CAAC,OAAO,EAAE,CAAC;YAC1B,CAAC;SACJ;KACJ,CAAC;AACN,CAAC,CAAC;AACF,MAAM,CAAC,aAAa,GAAG,UAAA,IAAI;IACvB,IAAI,CAAC,GAAG,EAAE,CAAC;IAEX,KAAK,IAAM,MAAI,IAAI,IAAI,EAAE;QACrB,IAAI,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,MAAI,CAAC,EAAE;YACxC,IAAM,KAAK,GAAG,IAAI,CAAC,MAAI,CAAC,CAAC;YACzB,CAAC,IAAI,UAAG,CAAC,CAAC,MAAI,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,MAAI,eAAK,KAAK,SAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAE,CAAC;SAC5G;KACJ;IAED,OAAO,CAAC,CAAC;AACb,CAAC,CAAC;AAEF,kBAAe,MAAM,CAAC", "sourcesContent": ["import LessError from '../less-error';\nimport tree from '../tree';\nimport visitors from '../visitors';\nimport getParserInput from './parser-input';\nimport * as utils from '../utils';\nimport functionRegistry from '../functions/function-registry';\nimport { ContainerSyntaxOptions, MediaSyntaxOptions } from '../tree/atrule-syntax';\nimport logger from '../logger';\nimport Selector from '../tree/selector';\nimport Anonymous from '../tree/anonymous';\n\n//\n// less.js - parser\n//\n//    A relatively straight-forward predictive parser.\n//    There is no tokenization/lexing stage, the input is parsed\n//    in one sweep.\n//\n//    To make the parser fast enough to run in the browser, several\n//    optimization had to be made:\n//\n//    - Matching and slicing on a huge input is often cause of slowdowns.\n//      The solution is to chunkify the input into smaller strings.\n//      The chunks are stored in the `chunks` var,\n//      `j` holds the current chunk index, and `currentPos` holds\n//      the index of the current chunk in relation to `input`.\n//      This gives us an almost 4x speed-up.\n//\n//    - In many cases, we don't need to match individual tokens;\n//      for example, if a value doesn't hold any variables, operations\n//      or dynamic references, the parser can effectively 'skip' it,\n//      treating it as a literal.\n//      An example would be '1px solid #000' - which evaluates to itself,\n//      we don't need to know what the individual components are.\n//      The drawback, of course is that you don't get the benefits of\n//      syntax-checking on the CSS. This gives us a 50% speed-up in the parser,\n//      and a smaller speed-up in the code-gen.\n//\n//\n//    Token matching is done with the `$` function, which either takes\n//    a terminal string or regexp, or a non-terminal function to call.\n//    It also takes care of moving all the indices forwards.\n//\n\nconst Parser = function Parser(context, imports, fileInfo, currentIndex) {\n    currentIndex = currentIndex || 0;\n    let parsers;\n    const parserInput = getParserInput();\n\n    function error(msg, type) {\n        throw new LessError(\n            {\n                index: parserInput.i,\n                filename: fileInfo.filename,\n                type: type || 'Syntax',\n                message: msg\n            },\n            imports\n        );\n    }\n\n    /**\n     * \n     * @param {string} msg \n     * @param {number} index \n     * @param {string} type \n     */\n    function warn(msg, index, type) {\n        if (!context.quiet) {\n            logger.warn(\n                (new LessError(\n                    {\n                        index: index ?? parserInput.i,\n                        filename: fileInfo.filename,\n                        type: type ? `${type.toUpperCase()} WARNING` : 'WARNING',\n                        message: msg\n                    },\n                    imports\n                )).toString()\n            );\n        }\n    }\n\n    function expect(arg, msg) {\n        // some older browsers return typeof 'function' for RegExp\n        const result = (arg instanceof Function) ? arg.call(parsers) : parserInput.$re(arg);\n        if (result) {\n            return result;\n        }\n\n        error(msg || (typeof arg === 'string'\n            ? `expected '${arg}' got '${parserInput.currentChar()}'`\n            : 'unexpected token'));\n    }\n\n    // Specialization of expect()\n    function expectChar(arg, msg) {\n        if (parserInput.$char(arg)) {\n            return arg;\n        }\n        error(msg || `expected '${arg}' got '${parserInput.currentChar()}'`);\n    }\n\n    function getDebugInfo(index) {\n        const filename = fileInfo.filename;\n\n        return {\n            lineNumber: utils.getLocation(index, parserInput.getInput()).line + 1,\n            fileName: filename\n        };\n    }\n\n    /**\n     *  Used after initial parsing to create nodes on the fly\n     *\n     *  @param {String} str          - string to parse\n     *  @param {Array}  parseList    - array of parsers to run input through e.g. [\"value\", \"important\"]\n     *  @param {Number} currentIndex - start number to begin indexing\n     *  @param {Object} fileInfo     - fileInfo to attach to created nodes\n     */\n    function parseNode(str, parseList, callback) {\n        let result;\n        const returnNodes = [];\n        const parser = parserInput;\n\n        try {\n            parser.start(str, false, function fail(msg, index) {\n                callback({\n                    message: msg,\n                    index: index + currentIndex\n                });\n            });\n            for (let x = 0, p; (p = parseList[x]); x++) {\n                result = parsers[p]();\n                returnNodes.push(result || null);\n            }\n\n            const endInfo = parser.end();\n            if (endInfo.isFinished) {\n                callback(null, returnNodes);\n            }\n            else {\n                callback(true, null);\n            }\n        } catch (e) {\n            throw new LessError({\n                index: e.index + currentIndex,\n                message: e.message\n            }, imports, fileInfo.filename);\n        }\n    }\n\n    //\n    // The Parser\n    //\n    return {\n        parserInput,\n        imports,\n        fileInfo,\n        parseNode,\n        //\n        // Parse an input string into an abstract syntax tree,\n        // @param str A string containing 'less' markup\n        // @param callback call `callback` when done.\n        // @param [additionalData] An optional map which can contains vars - a map (key, value) of variables to apply\n        //\n        parse: function (str, callback, additionalData) {\n            let root;\n            let err = null;\n            let globalVars;\n            let modifyVars;\n            let ignored;\n            let preText = '';\n\n            // Optionally disable @plugin parsing\n            if (additionalData && additionalData.disablePluginRule) {\n                parsers.plugin = function() {\n                    var dir = parserInput.$re(/^@plugin?\\s+/);\n                    if (dir) {\n                        error('@plugin statements are not allowed when disablePluginRule is set to true');\n                    }\n                }\n            }\n\n            globalVars = (additionalData && additionalData.globalVars) ? `${Parser.serializeVars(additionalData.globalVars)}\\n` : '';\n            modifyVars = (additionalData && additionalData.modifyVars) ? `\\n${Parser.serializeVars(additionalData.modifyVars)}` : '';\n\n            if (context.pluginManager) {\n                const preProcessors = context.pluginManager.getPreProcessors();\n                for (let i = 0; i < preProcessors.length; i++) {\n                    str = preProcessors[i].process(str, { context, imports, fileInfo });\n                }\n            }\n\n            if (globalVars || (additionalData && additionalData.banner)) {\n                preText = ((additionalData && additionalData.banner) ? additionalData.banner : '') + globalVars;\n                ignored = imports.contentsIgnoredChars;\n                ignored[fileInfo.filename] = ignored[fileInfo.filename] || 0;\n                ignored[fileInfo.filename] += preText.length;\n            }\n\n            str = str.replace(/\\r\\n?/g, '\\n');\n            // Remove potential UTF Byte Order Mark\n            str = preText + str.replace(/^\\uFEFF/, '') + modifyVars;\n            imports.contents[fileInfo.filename] = str;\n\n            // Start with the primary rule.\n            // The whole syntax tree is held under a Ruleset node,\n            // with the `root` property set to true, so no `{}` are\n            // output. The callback is called when the input is parsed.\n            try {\n                parserInput.start(str, context.chunkInput, function fail(msg, index) {\n                    throw new LessError({\n                        index,\n                        type: 'Parse',\n                        message: msg,\n                        filename: fileInfo.filename\n                    }, imports);\n                });\n\n                tree.Node.prototype.parse = this;\n                root = new tree.Ruleset(null, this.parsers.primary());\n                tree.Node.prototype.rootNode = root;\n                root.root = true;\n                root.firstRoot = true;\n                root.functionRegistry = functionRegistry.inherit();\n\n            } catch (e) {\n                return callback(new LessError(e, imports, fileInfo.filename));\n            }\n\n            // If `i` is smaller than the `input.length - 1`,\n            // it means the parser wasn't able to parse the whole\n            // string, so we've got a parsing error.\n            //\n            // We try to extract a \\n delimited string,\n            // showing the line where the parse error occurred.\n            // We split it up into two parts (the part which parsed,\n            // and the part which didn't), so we can color them differently.\n            const endInfo = parserInput.end();\n            if (!endInfo.isFinished) {\n\n                let message = endInfo.furthestPossibleErrorMessage;\n\n                if (!message) {\n                    message = 'Unrecognised input';\n                    if (endInfo.furthestChar === '}') {\n                        message += '. Possibly missing opening \\'{\\'';\n                    } else if (endInfo.furthestChar === ')') {\n                        message += '. Possibly missing opening \\'(\\'';\n                    } else if (endInfo.furthestReachedEnd) {\n                        message += '. Possibly missing something';\n                    }\n                }\n\n                err = new LessError({\n                    type: 'Parse',\n                    message,\n                    index: endInfo.furthest,\n                    filename: fileInfo.filename\n                }, imports);\n            }\n\n            const finish = e => {\n                e = err || e || imports.error;\n\n                if (e) {\n                    if (!(e instanceof LessError)) {\n                        e = new LessError(e, imports, fileInfo.filename);\n                    }\n\n                    return callback(e);\n                }\n                else {\n                    return callback(null, root);\n                }\n            };\n\n            if (context.processImports !== false) {\n                new visitors.ImportVisitor(imports, finish)\n                    .run(root);\n            } else {\n                return finish();\n            }\n        },\n\n        //\n        // Here in, the parsing rules/functions\n        //\n        // The basic structure of the syntax tree generated is as follows:\n        //\n        //   Ruleset ->  Declaration -> Value -> Expression -> Entity\n        //\n        // Here's some Less code:\n        //\n        //    .class {\n        //      color: #fff;\n        //      border: 1px solid #000;\n        //      width: @w + 4px;\n        //      > .child {...}\n        //    }\n        //\n        // And here's what the parse tree might look like:\n        //\n        //     Ruleset (Selector '.class', [\n        //         Declaration (\"color\",  Value ([Expression [Color #fff]]))\n        //         Declaration (\"border\", Value ([Expression [Dimension 1px][Keyword \"solid\"][Color #000]]))\n        //         Declaration (\"width\",  Value ([Expression [Operation \" + \" [Variable \"@w\"][Dimension 4px]]]))\n        //         Ruleset (Selector [Element '>', '.child'], [...])\n        //     ])\n        //\n        //  In general, most rules will try to parse a token with the `$re()` function, and if the return\n        //  value is truly, will return a new node, of the relevant type. Sometimes, we need to check\n        //  first, before parsing, that's when we use `peek()`.\n        //\n        parsers: parsers = {\n            //\n            // The `primary` rule is the *entry* and *exit* point of the parser.\n            // The rules here can appear at any level of the parse tree.\n            //\n            // The recursive nature of the grammar is an interplay between the `block`\n            // rule, which represents `{ ... }`, the `ruleset` rule, and this `primary` rule,\n            // as represented by this simplified grammar:\n            //\n            //     primary  →  (ruleset | declaration)+\n            //     ruleset  →  selector+ block\n            //     block    →  '{' primary '}'\n            //\n            // Only at one point is the primary rule not called from the\n            // block rule: at the root level.\n            //\n            primary: function () {\n                const mixin = this.mixin;\n                let root = [];\n                let node;\n\n                while (true) {\n                    while (true) {\n                        node = this.comment();\n                        if (!node) { break; }\n                        root.push(node);\n                    }\n                    // always process comments before deciding if finished\n                    if (parserInput.finished) {\n                        break;\n                    }\n                    if (parserInput.peek('}')) {\n                        break;\n                    }\n\n                    node = this.extendRule();\n                    if (node) {\n                        root = root.concat(node);\n                        continue;\n                    }\n\n                    node = mixin.definition() || this.declaration() || mixin.call(false, false) ||\n                        this.ruleset() || this.variableCall() || this.entities.call() || this.atrule();\n                    if (node) {\n                        root.push(node);\n                    } else {\n                        let foundSemiColon = false;\n                        while (parserInput.$char(';')) {\n                            foundSemiColon = true;\n                        }\n                        if (!foundSemiColon) {\n                            break;\n                        }\n                    }\n                }\n\n                return root;\n            },\n\n            // comments are collected by the main parsing mechanism and then assigned to nodes\n            // where the current structure allows it\n            comment: function () {\n                if (parserInput.commentStore.length) {\n                    const comment = parserInput.commentStore.shift();\n                    return new(tree.Comment)(comment.text, comment.isLineComment, comment.index + currentIndex, fileInfo);\n                }\n            },\n\n            //\n            // Entities are tokens which can be found inside an Expression\n            //\n            entities: {\n                mixinLookup: function() {\n                    return parsers.mixin.call(true, true);\n                },\n                //\n                // A string, which supports escaping \" and '\n                //\n                //     \"milky way\" 'he\\'s the one!'\n                //\n                quoted: function (forceEscaped) {\n                    let str;\n                    const index = parserInput.i;\n                    let isEscaped = false;\n\n                    parserInput.save();\n                    if (parserInput.$char('~')) {\n                        isEscaped = true;\n                    } else if (forceEscaped) {\n                        parserInput.restore();\n                        return;\n                    }\n\n                    str = parserInput.$quoted();\n                    if (!str) {\n                        parserInput.restore();\n                        return;\n                    }\n                    parserInput.forget();\n\n                    return new(tree.Quoted)(str.charAt(0), str.substr(1, str.length - 2), isEscaped, index + currentIndex, fileInfo);\n                },\n\n                //\n                // A catch-all word, such as:\n                //\n                //     black border-collapse\n                //\n                keyword: function () {\n                    const k = parserInput.$char('%') || parserInput.$re(/^\\[?(?:[\\w-]|\\\\(?:[A-Fa-f0-9]{1,6} ?|[^A-Fa-f0-9]))+\\]?/);\n                    if (k) {\n                        return tree.Color.fromKeyword(k) || new(tree.Keyword)(k);\n                    }\n                },\n\n                //\n                // A function call\n                //\n                //     rgb(255, 0, 255)\n                //\n                // The arguments are parsed with the `entities.arguments` parser.\n                //\n                call: function () {\n                    let name;\n                    let args;\n                    let func;\n                    const index = parserInput.i;\n\n                    // http://jsperf.com/case-insensitive-regex-vs-strtolower-then-regex/18\n                    if (parserInput.peek(/^url\\(/i)) {\n                        return;\n                    }\n\n                    parserInput.save();\n\n                    name = parserInput.$re(/^([\\w-]+|%|~|progid:[\\w.]+)\\(/);\n                    if (!name) {\n                        parserInput.forget();\n                        return;\n                    }\n\n                    name = name[1];\n                    func = this.customFuncCall(name);\n                    if (func) {\n                        args = func.parse();\n                        if (args && func.stop) {\n                            parserInput.forget();\n                            return args;\n                        }\n                    }\n\n                    args = this.arguments(args);\n\n                    if (!parserInput.$char(')')) {\n                        parserInput.restore('Could not parse call arguments or missing \\')\\'');\n                        return;\n                    }\n\n                    parserInput.forget();\n\n                    return new(tree.Call)(name, args, index + currentIndex, fileInfo);\n                },\n\n                declarationCall: function () {\n                    let validCall;\n                    let args;\n                    const index = parserInput.i;\n\n                    parserInput.save();\n\n                    validCall = parserInput.$re(/^[\\w]+\\(/);\n                    if (!validCall) {\n                        parserInput.forget();\n                        return;\n                    }\n\n                    validCall = validCall.substring(0, validCall.length - 1);\n\n                    let rule = this.ruleProperty();\n                    let value;\n                  \n                    if (rule) {\n                        value = this.value();\n                    }\n                    \n                    if (rule && value) {\n                        args = [new (tree.Declaration)(rule, value, null, null, parserInput.i + currentIndex, fileInfo, true)];\n                    }\n\n                    if (!parserInput.$char(')')) {\n                        parserInput.restore('Could not parse call arguments or missing \\')\\'');\n                        return;\n                    }\n\n                    parserInput.forget();\n\n                    return new(tree.Call)(validCall, args, index + currentIndex, fileInfo);\n                },\n\n                //\n                // Parsing rules for functions with non-standard args, e.g.:\n                //\n                //     boolean(not(2 > 1))\n                //\n                //     This is a quick prototype, to be modified/improved when\n                //     more custom-parsed funcs come (e.g. `selector(...)`)\n                //\n\n                customFuncCall: function (name) {\n                    /* Ideally the table is to be moved out of here for faster perf.,\n                       but it's quite tricky since it relies on all these `parsers`\n                       and `expect` available only here */\n                    return {\n                        alpha:   f(parsers.ieAlpha, true),\n                        boolean: f(condition),\n                        'if':    f(condition)\n                    }[name.toLowerCase()];\n\n                    function f(parse, stop) {\n                        return {\n                            parse, // parsing function\n                            stop   // when true - stop after parse() and return its result,\n                            // otherwise continue for plain args\n                        };\n                    }\n\n                    function condition() {\n                        return [expect(parsers.condition, 'expected condition')];\n                    }\n                },\n\n                arguments: function (prevArgs) {\n                    let argsComma = prevArgs || [];\n                    const argsSemiColon = [];\n                    let isSemiColonSeparated;\n                    let value;\n\n                    parserInput.save();\n\n                    while (true) {\n                        if (prevArgs) {\n                            prevArgs = false;\n                        } else {\n                            value = parsers.detachedRuleset() || this.assignment() || parsers.expression();\n                            if (!value) {\n                                break;\n                            }\n\n                            if (value.value && value.value.length == 1) {\n                                value = value.value[0];\n                            }\n\n                            argsComma.push(value);\n                        }\n\n                        if (parserInput.$char(',')) {\n                            continue;\n                        }\n\n                        if (parserInput.$char(';') || isSemiColonSeparated) {\n                            isSemiColonSeparated = true;\n                            value = (argsComma.length < 1) ? argsComma[0]\n                                : new tree.Value(argsComma);\n                            argsSemiColon.push(value);\n                            argsComma = [];\n                        }\n                    }\n\n                    parserInput.forget();\n                    return isSemiColonSeparated ? argsSemiColon : argsComma;\n                },\n                literal: function () {\n                    return this.dimension() ||\n                           this.color() ||\n                           this.quoted() ||\n                           this.unicodeDescriptor();\n                },\n\n                // Assignments are argument entities for calls.\n                // They are present in ie filter properties as shown below.\n                //\n                //     filter: progid:DXImageTransform.Microsoft.Alpha( *opacity=50* )\n                //\n\n                assignment: function () {\n                    let key;\n                    let value;\n                    parserInput.save();\n                    key = parserInput.$re(/^\\w+(?=\\s?=)/i);\n                    if (!key) {\n                        parserInput.restore();\n                        return;\n                    }\n                    if (!parserInput.$char('=')) {\n                        parserInput.restore();\n                        return;\n                    }\n                    value = parsers.entity();\n                    if (value) {\n                        parserInput.forget();\n                        return new(tree.Assignment)(key, value);\n                    } else {\n                        parserInput.restore();\n                    }\n                },\n\n                //\n                // Parse url() tokens\n                //\n                // We use a specific rule for urls, because they don't really behave like\n                // standard function calls. The difference is that the argument doesn't have\n                // to be enclosed within a string, so it can't be parsed as an Expression.\n                //\n                url: function () {\n                    let value;\n                    const index = parserInput.i;\n\n                    parserInput.autoCommentAbsorb = false;\n\n                    if (!parserInput.$str('url(')) {\n                        parserInput.autoCommentAbsorb = true;\n                        return;\n                    }\n\n                    value = this.quoted() || this.variable() || this.property() ||\n                            parserInput.$re(/^(?:(?:\\\\[()'\"])|[^()'\"])+/) || '';\n\n                    parserInput.autoCommentAbsorb = true;\n\n                    expectChar(')');\n\n                    return new(tree.URL)((value.value !== undefined ||\n                        value instanceof tree.Variable ||\n                        value instanceof tree.Property) ?\n                        value : new(tree.Anonymous)(value, index), index + currentIndex, fileInfo);\n                },\n\n                //\n                // A Variable entity, such as `@fink`, in\n                //\n                //     width: @fink + 2px\n                //\n                // We use a different parser for variable definitions,\n                // see `parsers.variable`.\n                //\n                variable: function () {\n                    let ch;\n                    let name;\n                    const index = parserInput.i;\n\n                    parserInput.save();\n                    if (parserInput.currentChar() === '@' && (name = parserInput.$re(/^@@?[\\w-]+/))) {\n                        ch = parserInput.currentChar();\n                        if (ch === '(' || ch === '[' && !parserInput.prevChar().match(/^\\s/)) {\n                            // this may be a VariableCall lookup\n                            const result = parsers.variableCall(name);\n                            if (result) {\n                                parserInput.forget();\n                                return result;\n                            }\n                        }\n                        parserInput.forget();\n                        return new(tree.Variable)(name, index + currentIndex, fileInfo);\n                    }\n                    parserInput.restore();\n                },\n\n                // A variable entity using the protective {} e.g. @{var}\n                variableCurly: function () {\n                    let curly;\n                    const index = parserInput.i;\n\n                    if (parserInput.currentChar() === '@' && (curly = parserInput.$re(/^@\\{([\\w-]+)\\}/))) {\n                        return new(tree.Variable)(`@${curly[1]}`, index + currentIndex, fileInfo);\n                    }\n                },\n                //\n                // A Property accessor, such as `$color`, in\n                //\n                //     background-color: $color\n                //\n                property: function () {\n                    let name;\n                    const index = parserInput.i;\n\n                    if (parserInput.currentChar() === '$' && (name = parserInput.$re(/^\\$[\\w-]+/))) {\n                        return new(tree.Property)(name, index + currentIndex, fileInfo);\n                    }\n                },\n\n                // A property entity useing the protective {} e.g. ${prop}\n                propertyCurly: function () {\n                    let curly;\n                    const index = parserInput.i;\n\n                    if (parserInput.currentChar() === '$' && (curly = parserInput.$re(/^\\$\\{([\\w-]+)\\}/))) {\n                        return new(tree.Property)(`$${curly[1]}`, index + currentIndex, fileInfo);\n                    }\n                },\n                //\n                // A Hexadecimal color\n                //\n                //     #4F3C2F\n                //\n                // `rgb` and `hsl` colors are parsed through the `entities.call` parser.\n                //\n                color: function () {\n                    let rgb;\n                    parserInput.save();\n\n                    if (parserInput.currentChar() === '#' && (rgb = parserInput.$re(/^#([A-Fa-f0-9]{8}|[A-Fa-f0-9]{6}|[A-Fa-f0-9]{3,4})([\\w.#[])?/))) {\n                        if (!rgb[2]) {\n                            parserInput.forget();\n                            return new(tree.Color)(rgb[1], undefined, rgb[0]);\n                        }\n                    }\n                    parserInput.restore();\n                },\n\n                colorKeyword: function () {\n                    parserInput.save();\n                    const autoCommentAbsorb = parserInput.autoCommentAbsorb;\n                    parserInput.autoCommentAbsorb = false;\n                    const k = parserInput.$re(/^[_A-Za-z-][_A-Za-z0-9-]+/);\n                    parserInput.autoCommentAbsorb = autoCommentAbsorb;\n                    if (!k) {\n                        parserInput.forget();\n                        return;\n                    }\n                    parserInput.restore();\n                    const color = tree.Color.fromKeyword(k);\n                    if (color) {\n                        parserInput.$str(k);\n                        return color;\n                    }\n                },\n\n                //\n                // A Dimension, that is, a number and a unit\n                //\n                //     0.5em 95%\n                //\n                dimension: function () {\n                    if (parserInput.peekNotNumeric()) {\n                        return;\n                    }\n\n                    const value = parserInput.$re(/^([+-]?\\d*\\.?\\d+)(%|[a-z_]+)?/i);\n                    if (value) {\n                        return new(tree.Dimension)(value[1], value[2]);\n                    }\n                },\n\n                //\n                // A unicode descriptor, as is used in unicode-range\n                //\n                // U+0??  or U+00A1-00A9\n                //\n                unicodeDescriptor: function () {\n                    let ud;\n\n                    ud = parserInput.$re(/^U\\+[0-9a-fA-F?]+(-[0-9a-fA-F?]+)?/);\n                    if (ud) {\n                        return new(tree.UnicodeDescriptor)(ud[0]);\n                    }\n                },\n\n                //\n                // JavaScript code to be evaluated\n                //\n                //     `window.location.href`\n                //\n                javascript: function () {\n                    let js;\n                    const index = parserInput.i;\n\n                    parserInput.save();\n\n                    const escape = parserInput.$char('~');\n                    const jsQuote = parserInput.$char('`');\n\n                    if (!jsQuote) {\n                        parserInput.restore();\n                        return;\n                    }\n\n                    js = parserInput.$re(/^[^`]*`/);\n                    if (js) {\n                        parserInput.forget();\n                        return new(tree.JavaScript)(js.substr(0, js.length - 1), Boolean(escape), index + currentIndex, fileInfo);\n                    }\n                    parserInput.restore('invalid javascript definition');\n                }\n            },\n\n            //\n            // The variable part of a variable definition. Used in the `rule` parser\n            //\n            //     @fink:\n            //\n            variable: function () {\n                let name;\n\n                if (parserInput.currentChar() === '@' && (name = parserInput.$re(/^(@[\\w-]+)\\s*:/))) { return name[1]; }\n            },\n\n            //\n            // Call a variable value to retrieve a detached ruleset\n            // or a value from a detached ruleset's rules.\n            //\n            //     @fink();\n            //     @fink;\n            //     color: @fink[@color];\n            //\n            variableCall: function (parsedName) {\n                let lookups;\n                const i = parserInput.i;\n                const inValue = !!parsedName;\n                let name = parsedName;\n\n                parserInput.save();\n\n                if (name || (parserInput.currentChar() === '@'\n                    && (name = parserInput.$re(/^(@[\\w-]+)(\\(\\s*\\))?/)))) {\n\n                    lookups = this.mixin.ruleLookups();\n\n                    if (!lookups && ((inValue && parserInput.$str('()') !== '()') || (name[2] !== '()'))) {\n                        parserInput.restore('Missing \\'[...]\\' lookup in variable call');\n                        return;\n                    }\n\n                    if (!inValue) {\n                        name = name[1];\n                    }\n\n                    const call = new tree.VariableCall(name, i, fileInfo);\n                    if (!inValue && parsers.end()) {\n                        parserInput.forget();\n                        return call;\n                    }\n                    else {\n                        parserInput.forget();\n                        return new tree.NamespaceValue(call, lookups, i, fileInfo);\n                    }\n                }\n\n                parserInput.restore();\n            },\n\n            //\n            // extend syntax - used to extend selectors\n            //\n            extend: function(isRule) {\n                let elements;\n                let e;\n                const index = parserInput.i;\n                let option;\n                let extendList;\n                let extend;\n\n                if (!parserInput.$str(isRule ? '&:extend(' : ':extend(')) {\n                    return;\n                }\n\n                do {\n                    option = null;\n                    elements = null;\n                    let first = true;\n                    while (!(option = parserInput.$re(/^(!?all)(?=\\s*(\\)|,))/))) {\n                        e = this.element();\n\n                        if (!e) {\n                            break;\n                        }\n                        /**\n                         * @note - This will not catch selectors in pseudos like :is() and :where() because\n                         * they don't currently parse their contents as selectors.\n                         */\n                        if (!first && e.combinator.value) {\n                            warn('Targeting complex selectors can have unexpected behavior, and this behavior may change in the future.', index)\n                        }\n\n                        first = false;\n                        if (elements) {\n                            elements.push(e);\n                        } else {\n                            elements = [ e ];\n                        }\n                    }\n\n                    option = option && option[1];\n                    if (!elements) {\n                        error('Missing target selector for :extend().');\n                    }\n                    extend = new(tree.Extend)(new(tree.Selector)(elements), option, index + currentIndex, fileInfo);\n                    if (extendList) {\n                        extendList.push(extend);\n                    } else {\n                        extendList = [ extend ];\n                    }\n                } while (parserInput.$char(','));\n\n                expect(/^\\)/);\n\n                if (isRule) {\n                    expect(/^;/);\n                }\n\n                return extendList;\n            },\n\n            //\n            // extendRule - used in a rule to extend all the parent selectors\n            //\n            extendRule: function() {\n                return this.extend(true);\n            },\n\n            //\n            // Mixins\n            //\n            mixin: {\n                //\n                // A Mixin call, with an optional argument list\n                //\n                //     #mixins > .square(#fff);\n                //     #mixins.square(#fff);\n                //     .rounded(4px, black);\n                //     .button;\n                //\n                // We can lookup / return a value using the lookup syntax:\n                //\n                //     color: #mixin.square(#fff)[@color];\n                //\n                // The `while` loop is there because mixins can be\n                // namespaced, but we only support the child and descendant\n                // selector for now.\n                //\n                call: function (inValue, getLookup) {\n                    const s = parserInput.currentChar();\n                    let important = false;\n                    let lookups;\n                    const index = parserInput.i;\n                    let elements;\n                    let args;\n                    let hasParens;\n                    let parensIndex;\n                    let parensWS = false;\n\n                    if (s !== '.' && s !== '#') { return; }\n\n                    parserInput.save(); // stop us absorbing part of an invalid selector\n\n                    elements = this.elements();\n\n                    if (elements) {\n                        parensIndex = parserInput.i;\n                        if (parserInput.$char('(')) {\n                            parensWS = parserInput.isWhitespace(-2);\n                            args = this.args(true).args;\n                            expectChar(')');\n                            hasParens = true;\n                            if (parensWS) {\n                                warn('Whitespace between a mixin name and parentheses for a mixin call is deprecated', parensIndex, 'DEPRECATED');\n                            }\n                        }\n\n                        if (getLookup !== false) {\n                            lookups = this.ruleLookups();\n                        }\n                        if (getLookup === true && !lookups) {\n                            parserInput.restore();\n                            return;\n                        }\n\n                        if (inValue && !lookups && !hasParens) {\n                            // This isn't a valid in-value mixin call\n                            parserInput.restore();\n                            return;\n                        }\n\n                        if (!inValue && parsers.important()) {\n                            important = true;\n                        }\n\n                        if (inValue || parsers.end()) {\n                            parserInput.forget();\n                            const mixin = new(tree.mixin.Call)(elements, args, index + currentIndex, fileInfo, !lookups && important);\n                            if (lookups) {\n                                return new tree.NamespaceValue(mixin, lookups);\n                            }\n                            else {\n                                if (!hasParens) {\n                                    warn('Calling a mixin without parentheses is deprecated', parensIndex, 'DEPRECATED');\n                                }\n                                return mixin;\n                            }\n                        }\n                    }\n\n                    parserInput.restore();\n                },\n                /**\n                 * Matching elements for mixins\n                 * (Start with . or # and can have > )\n                 */\n                elements: function() {\n                    let elements;\n                    let e;\n                    let c;\n                    let elem;\n                    let elemIndex;\n                    const re = /^[#.](?:[\\w-]|\\\\(?:[A-Fa-f0-9]{1,6} ?|[^A-Fa-f0-9]))+/;\n                    while (true) {\n                        elemIndex = parserInput.i;\n                        e = parserInput.$re(re);\n\n                        if (!e) {\n                            break;\n                        }\n                        elem = new(tree.Element)(c, e, false, elemIndex + currentIndex, fileInfo);\n                        if (elements) {\n                            elements.push(elem);\n                        } else {\n                            elements = [ elem ];\n                        }\n                        c = parserInput.$char('>');\n                    }\n                    return elements;\n                },\n                args: function (isCall) {\n                    const entities = parsers.entities;\n                    const returner = { args:null, variadic: false };\n                    let expressions = [];\n                    const argsSemiColon = [];\n                    const argsComma = [];\n                    let isSemiColonSeparated;\n                    let expressionContainsNamed;\n                    let name;\n                    let nameLoop;\n                    let value;\n                    let arg;\n                    let expand;\n                    let hasSep = true;\n\n                    parserInput.save();\n\n                    while (true) {\n                        if (isCall) {\n                            arg = parsers.detachedRuleset() || parsers.expression();\n                        } else {\n                            parserInput.commentStore.length = 0;\n                            if (parserInput.$str('...')) {\n                                returner.variadic = true;\n                                if (parserInput.$char(';') && !isSemiColonSeparated) {\n                                    isSemiColonSeparated = true;\n                                }\n                                (isSemiColonSeparated ? argsSemiColon : argsComma)\n                                    .push({ variadic: true });\n                                break;\n                            }\n                            arg = entities.variable() || entities.property() || entities.literal() || entities.keyword() || this.call(true);\n                        }\n\n                        if (!arg || !hasSep) {\n                            break;\n                        }\n\n                        nameLoop = null;\n                        if (arg.throwAwayComments) {\n                            arg.throwAwayComments();\n                        }\n                        value = arg;\n                        let val = null;\n\n                        if (isCall) {\n                            // Variable\n                            if (arg.value && arg.value.length == 1) {\n                                val = arg.value[0];\n                            }\n                        } else {\n                            val = arg;\n                        }\n\n                        if (val && (val instanceof tree.Variable || val instanceof tree.Property)) {\n                            if (parserInput.$char(':')) {\n                                if (expressions.length > 0) {\n                                    if (isSemiColonSeparated) {\n                                        error('Cannot mix ; and , as delimiter types');\n                                    }\n                                    expressionContainsNamed = true;\n                                }\n\n                                value = parsers.detachedRuleset() || parsers.expression();\n\n                                if (!value) {\n                                    if (isCall) {\n                                        error('could not understand value for named argument');\n                                    } else {\n                                        parserInput.restore();\n                                        returner.args = [];\n                                        return returner;\n                                    }\n                                }\n                                nameLoop = (name = val.name);\n                            } else if (parserInput.$str('...')) {\n                                if (!isCall) {\n                                    returner.variadic = true;\n                                    if (parserInput.$char(';') && !isSemiColonSeparated) {\n                                        isSemiColonSeparated = true;\n                                    }\n                                    (isSemiColonSeparated ? argsSemiColon : argsComma)\n                                        .push({ name: arg.name, variadic: true });\n                                    break;\n                                } else {\n                                    expand = true;\n                                }\n                            } else if (!isCall) {\n                                name = nameLoop = val.name;\n                                value = null;\n                            }\n                        }\n\n                        if (value) {\n                            expressions.push(value);\n                        }\n\n                        argsComma.push({ name:nameLoop, value, expand });\n\n                        if (parserInput.$char(',')) {\n                            hasSep = true;\n                            continue;\n                        }\n                        hasSep = parserInput.$char(';') === ';';\n\n                        if (hasSep || isSemiColonSeparated) {\n\n                            if (expressionContainsNamed) {\n                                error('Cannot mix ; and , as delimiter types');\n                            }\n\n                            isSemiColonSeparated = true;\n\n                            if (expressions.length > 1) {\n                                value = new(tree.Value)(expressions);\n                            }\n                            argsSemiColon.push({ name, value, expand });\n\n                            name = null;\n                            expressions = [];\n                            expressionContainsNamed = false;\n                        }\n                    }\n\n                    parserInput.forget();\n                    returner.args = isSemiColonSeparated ? argsSemiColon : argsComma;\n                    return returner;\n                },\n                //\n                // A Mixin definition, with a list of parameters\n                //\n                //     .rounded (@radius: 2px, @color) {\n                //        ...\n                //     }\n                //\n                // Until we have a finer grained state-machine, we have to\n                // do a look-ahead, to make sure we don't have a mixin call.\n                // See the `rule` function for more information.\n                //\n                // We start by matching `.rounded (`, and then proceed on to\n                // the argument list, which has optional default values.\n                // We store the parameters in `params`, with a `value` key,\n                // if there is a value, such as in the case of `@radius`.\n                //\n                // Once we've got our params list, and a closing `)`, we parse\n                // the `{...}` block.\n                //\n                definition: function () {\n                    let name;\n                    let params = [];\n                    let match;\n                    let ruleset;\n                    let cond;\n                    let variadic = false;\n                    if ((parserInput.currentChar() !== '.' && parserInput.currentChar() !== '#') ||\n                        parserInput.peek(/^[^{]*\\}/)) {\n                        return;\n                    }\n\n                    parserInput.save();\n\n                    match = parserInput.$re(/^([#.](?:[\\w-]|\\\\(?:[A-Fa-f0-9]{1,6} ?|[^A-Fa-f0-9]))+)\\s*\\(/);\n                    if (match) {\n                        name = match[1];\n\n                        const argInfo = this.args(false);\n                        params = argInfo.args;\n                        variadic = argInfo.variadic;\n\n                        // .mixincall(\"@{a}\");\n                        // looks a bit like a mixin definition..\n                        // also\n                        // .mixincall(@a: {rule: set;});\n                        // so we have to be nice and restore\n                        if (!parserInput.$char(')')) {\n                            parserInput.restore('Missing closing \\')\\'');\n                            return;\n                        }\n\n                        parserInput.commentStore.length = 0;\n\n                        if (parserInput.$str('when')) { // Guard\n                            cond = expect(parsers.conditions, 'expected condition');\n                        }\n\n                        ruleset = parsers.block();\n\n                        if (ruleset) {\n                            parserInput.forget();\n                            return new(tree.mixin.Definition)(name, params, ruleset, cond, variadic);\n                        } else {\n                            parserInput.restore();\n                        }\n                    } else {\n                        parserInput.restore();\n                    }\n                },\n\n                ruleLookups: function() {\n                    let rule;\n                    const lookups = [];\n\n                    if (parserInput.currentChar() !== '[') {\n                        return;\n                    }\n\n                    while (true) {\n                        parserInput.save();\n                        rule = this.lookupValue();\n                        if (!rule && rule !== '') {\n                            parserInput.restore();\n                            break;\n                        }\n                        lookups.push(rule);\n                        parserInput.forget();\n                    }\n                    if (lookups.length > 0) {\n                        return lookups;\n                    }\n                },\n\n                lookupValue: function() {\n                    parserInput.save();\n\n                    if (!parserInput.$char('[')) {\n                        parserInput.restore();\n                        return;\n                    }\n\n                    const name = parserInput.$re(/^(?:[@$]{0,2})[_a-zA-Z0-9-]*/);\n\n                    if (!parserInput.$char(']')) {\n                        parserInput.restore();\n                        return;\n                    }\n\n                    if (name || name === '') {\n                        parserInput.forget();\n                        return name;\n                    }\n\n                    parserInput.restore();\n                }\n            },\n            //\n            // Entities are the smallest recognized token,\n            // and can be found inside a rule's value.\n            //\n            entity: function () {\n                const entities = this.entities;\n\n                return this.comment() || entities.literal() || entities.variable() || entities.url() ||\n                    entities.property() || entities.call() || entities.keyword() || this.mixin.call(true) ||\n                    entities.javascript();\n            },\n\n            //\n            // A Declaration terminator. Note that we use `peek()` to check for '}',\n            // because the `block` rule will be expecting it, but we still need to make sure\n            // it's there, if ';' was omitted.\n            //\n            end: function () {\n                return parserInput.$char(';') || parserInput.peek('}');\n            },\n\n            //\n            // IE's alpha function\n            //\n            //     alpha(opacity=88)\n            //\n            ieAlpha: function () {\n                let value;\n\n                // http://jsperf.com/case-insensitive-regex-vs-strtolower-then-regex/18\n                if (!parserInput.$re(/^opacity=/i)) { return; }\n                value = parserInput.$re(/^\\d+/);\n                if (!value) {\n                    value = expect(parsers.entities.variable, 'Could not parse alpha');\n                    value = `@{${value.name.slice(1)}}`;\n                }\n                expectChar(')');\n                return new tree.Quoted('', `alpha(opacity=${value})`);\n            },\n\n            /** \n             * A Selector Element\n             *\n             *   div\n             *   + h1\n             *   #socks\n             *   input[type=\"text\"]\n             *\n             * Elements are the building blocks for Selectors,\n             * they are made out of a `Combinator` (see combinator rule),\n             * and an element name, such as a tag a class, or `*`.\n             */\n            element: function () {\n                let e;\n                let c;\n                let v;\n                const index = parserInput.i;\n\n                c = this.combinator();\n\n                /** This selector parser is quite simplistic and will pass a number of invalid selectors. */\n                e = parserInput.$re(/^(?:\\d+\\.\\d+|\\d+)%/) ||\n                    // eslint-disable-next-line no-control-regex\n                    parserInput.$re(/^(?:[.#]?|:*)(?:[\\w-]|[^\\x00-\\x9f]|\\\\(?:[A-Fa-f0-9]{1,6} ?|[^A-Fa-f0-9]))+/) ||\n                    parserInput.$char('*') || parserInput.$char('&') || this.attribute() ||\n                    parserInput.$re(/^\\([^&()@]+\\)/) ||  parserInput.$re(/^[.#:](?=@)/) ||\n                    this.entities.variableCurly();\n\n                if (!e) {\n                    parserInput.save();\n                    if (parserInput.$char('(')) {\n                        if ((v = this.selector(false))) {\n                            let selectors = [];\n                            while (parserInput.$char(',')) {\n                                selectors.push(v);\n                                selectors.push(new Anonymous(','));\n                                v = this.selector(false);\n                            }\n                            selectors.push(v);\n                                                        \n                            if (parserInput.$char(')')) {\n                                if (selectors.length > 1) {\n                                    e = new (tree.Paren)(new Selector(selectors));\n                                } else {\n                                    e = new(tree.Paren)(v);\n                                }\n                                parserInput.forget();\n                            } else {\n                                parserInput.restore('Missing closing \\')\\'');\n                            }\n                        } else {\n                            parserInput.restore('Missing closing \\')\\'');\n                        }\n                    } else {\n                        parserInput.forget();\n                    }\n                }\n\n                if (e) { return new(tree.Element)(c, e, e instanceof tree.Variable, index + currentIndex, fileInfo); }\n            },\n\n            //\n            // Combinators combine elements together, in a Selector.\n            //\n            // Because our parser isn't white-space sensitive, special care\n            // has to be taken, when parsing the descendant combinator, ` `,\n            // as it's an empty space. We have to check the previous character\n            // in the input, to see if it's a ` ` character. More info on how\n            // we deal with this in *combinator.js*.\n            //\n            combinator: function () {\n                let c = parserInput.currentChar();\n\n                if (c === '/') {\n                    parserInput.save();\n                    const slashedCombinator = parserInput.$re(/^\\/[a-z]+\\//i);\n                    if (slashedCombinator) {\n                        parserInput.forget();\n                        return new(tree.Combinator)(slashedCombinator);\n                    }\n                    parserInput.restore();\n                }\n\n                if (c === '>' || c === '+' || c === '~' || c === '|' || c === '^') {\n                    parserInput.i++;\n                    if (c === '^' && parserInput.currentChar() === '^') {\n                        c = '^^';\n                        parserInput.i++;\n                    }\n                    while (parserInput.isWhitespace()) { parserInput.i++; }\n                    return new(tree.Combinator)(c);\n                } else if (parserInput.isWhitespace(-1)) {\n                    return new(tree.Combinator)(' ');\n                } else {\n                    return new(tree.Combinator)(null);\n                }\n            },\n            //\n            // A CSS Selector\n            // with less extensions e.g. the ability to extend and guard\n            //\n            //     .class > div + h1\n            //     li a:hover\n            //\n            // Selectors are made out of one or more Elements, see above.\n            //\n            selector: function (isLess) {\n                const index = parserInput.i;\n                let elements;\n                let extendList;\n                let c;\n                let e;\n                let allExtends;\n                let when;\n                let condition;\n                isLess = isLess !== false;\n                while ((isLess && (extendList = this.extend())) || (isLess && (when = parserInput.$str('when'))) || (e = this.element())) {\n                    if (when) {\n                        condition = expect(this.conditions, 'expected condition');\n                    } else if (condition) {\n                        error('CSS guard can only be used at the end of selector');\n                    } else if (extendList) {\n                        if (allExtends) {\n                            allExtends = allExtends.concat(extendList);\n                        } else {\n                            allExtends = extendList;\n                        }\n                    } else {\n                        if (allExtends) { error('Extend can only be used at the end of selector'); }\n                        c = parserInput.currentChar();\n                        if (Array.isArray(e)){\n                            e.forEach(ele => elements.push(ele));\n                        } if (elements) {\n                            elements.push(e);\n                        } else {\n                            elements = [ e ];\n                        }\n                        e = null;\n                    }\n                    if (c === '{' || c === '}' || c === ';' || c === ',' || c === ')') {\n                        break;\n                    }\n                }\n\n                if (elements) { return new(tree.Selector)(elements, allExtends, condition, index + currentIndex, fileInfo); }\n                if (allExtends) { error('Extend must be used to extend a selector, it cannot be used on its own'); }\n            },\n            selectors: function () {\n                let s;\n                let selectors;\n                while (true) {\n                    s = this.selector();\n                    if (!s) {\n                        break;\n                    }\n                    if (selectors) {\n                        selectors.push(s);\n                    } else {\n                        selectors = [ s ];\n                    }\n                    parserInput.commentStore.length = 0;\n                    if (s.condition && selectors.length > 1) {\n                        error('Guards are only currently allowed on a single selector.');\n                    }\n                    if (!parserInput.$char(',')) { break; }\n                    if (s.condition) {\n                        error('Guards are only currently allowed on a single selector.');\n                    }\n                    parserInput.commentStore.length = 0;\n                }\n                return selectors;\n            },\n            attribute: function () {\n                if (!parserInput.$char('[')) { return; }\n\n                const entities = this.entities;\n                let key;\n                let val;\n                let op;\n                //\n                // case-insensitive flag\n                // e.g. [attr operator value i]\n                //\n                let cif;\n\n                if (!(key = entities.variableCurly())) {\n                    key = expect(/^(?:[_A-Za-z0-9-*]*\\|)?(?:[_A-Za-z0-9-]|\\\\.)+/);\n                }\n\n                op = parserInput.$re(/^[|~*$^]?=/);\n                if (op) {\n                    val = entities.quoted() || parserInput.$re(/^[0-9]+%/) || parserInput.$re(/^[\\w-]+/) || entities.variableCurly();\n                    if (val) {\n                        cif = parserInput.$re(/^[iIsS]/);\n                    }\n                }\n\n                expectChar(']');\n\n                return new(tree.Attribute)(key, op, val, cif);\n            },\n\n            //\n            // The `block` rule is used by `ruleset` and `mixin.definition`.\n            // It's a wrapper around the `primary` rule, with added `{}`.\n            //\n            block: function () {\n                let content;\n                if (parserInput.$char('{') && (content = this.primary()) && parserInput.$char('}')) {\n                    return content;\n                }\n            },\n\n            blockRuleset: function() {\n                let block = this.block();\n\n                if (block) {\n                    block = new tree.Ruleset(null, block);\n                }\n                return block;\n            },\n\n            detachedRuleset: function() {\n                let argInfo;\n                let params;\n                let variadic;\n\n                parserInput.save();\n                if (parserInput.$re(/^[.#]\\(/)) {\n                    /**\n                     * DR args currently only implemented for each() function, and not\n                     * yet settable as `@dr: #(@arg) {}`\n                     * This should be done when DRs are merged with mixins.\n                     * See: https://github.com/less/less-meta/issues/16\n                     */\n                    argInfo = this.mixin.args(false);\n                    params = argInfo.args;\n                    variadic = argInfo.variadic;\n                    if (!parserInput.$char(')')) {\n                        parserInput.restore();\n                        return;\n                    }\n                }\n                const blockRuleset = this.blockRuleset();\n                if (blockRuleset) {\n                    parserInput.forget();\n                    if (params) {\n                        return new tree.mixin.Definition(null, params, blockRuleset, null, variadic);\n                    }\n                    return new tree.DetachedRuleset(blockRuleset);\n                }\n                parserInput.restore();\n            },\n\n            //\n            // div, .class, body > p {...}\n            //\n            ruleset: function () {\n                let selectors;\n                let rules;\n                let debugInfo;\n\n                parserInput.save();\n\n                if (context.dumpLineNumbers) {\n                    debugInfo = getDebugInfo(parserInput.i);\n                }\n\n                selectors = this.selectors();\n\n                if (selectors && (rules = this.block())) {\n                    parserInput.forget();\n                    const ruleset = new(tree.Ruleset)(selectors, rules, context.strictImports);\n                    if (context.dumpLineNumbers) {\n                        ruleset.debugInfo = debugInfo;\n                    }\n                    return ruleset;\n                } else {\n                    parserInput.restore();\n                }\n            },\n            declaration: function () {\n                let name;\n                let value;\n                const index = parserInput.i;\n                let hasDR;\n                const c = parserInput.currentChar();\n                let important;\n                let merge;\n                let isVariable;\n\n                if (c === '.' || c === '#' || c === '&' || c === ':') { return; }\n\n                parserInput.save();\n\n                name = this.variable() || this.ruleProperty();\n                if (name) {\n                    isVariable = typeof name === 'string';\n\n                    if (isVariable) {\n                        value = this.detachedRuleset();\n                        if (value) {\n                            hasDR = true;\n                        }\n                    }\n\n                    parserInput.commentStore.length = 0;\n                    if (!value) {\n                        // a name returned by this.ruleProperty() is always an array of the form:\n                        // [string-1, ..., string-n, \"\"] or [string-1, ..., string-n, \"+\"]\n                        // where each item is a tree.Keyword or tree.Variable\n                        merge = !isVariable && name.length > 1 && name.pop().value;\n\n                        // Custom property values get permissive parsing\n                        if (name[0].value && name[0].value.slice(0, 2) === '--') {\n                            if (parserInput.$char(';')) {\n                                value = new Anonymous('');\n                            } else {\n                                value = this.permissiveValue(/[;}]/, true);\n                            }\n                        }\n                        // Try to store values as anonymous\n                        // If we need the value later we'll re-parse it in ruleset.parseValue\n                        else {\n                            value = this.anonymousValue();\n                        }\n                        if (value) {\n                            parserInput.forget();\n                            // anonymous values absorb the end ';' which is required for them to work\n                            return new(tree.Declaration)(name, value, false, merge, index + currentIndex, fileInfo);\n                        }\n\n                        if (!value) {\n                            value = this.value();\n                        }\n\n                        if (value) {\n                            important = this.important();\n                        } else if (isVariable) {\n                            /**\n                             * As a last resort, try permissiveValue\n                             *\n                             * @todo - This has created some knock-on problems of not\n                             * flagging incorrect syntax or detecting user intent.\n                             */\n                            value = this.permissiveValue();\n                        }\n                    }\n\n                    if (value && (this.end() || hasDR)) {\n                        parserInput.forget();\n                        return new(tree.Declaration)(name, value, important, merge, index + currentIndex, fileInfo);\n                    }\n                    else {\n                        parserInput.restore();\n                    }\n                } else {\n                    parserInput.restore();\n                }\n            },\n            anonymousValue: function () {\n                const index = parserInput.i;\n                const match = parserInput.$re(/^([^.#@$+/'\"*`(;{}-]*);/);\n                if (match) {\n                    return new(tree.Anonymous)(match[1], index + currentIndex);\n                }\n            },\n            /**\n             * Used for custom properties, at-rules, and variables (as fallback)\n             * Parses almost anything inside of {} [] () \"\" blocks\n             * until it reaches outer-most tokens.\n             *\n             * First, it will try to parse comments and entities to reach\n             * the end. This is mostly like the Expression parser except no\n             * math is allowed.\n             * \n             * @param {RexExp} untilTokens - Characters to stop parsing at\n             */\n            permissiveValue: function (untilTokens) {\n                let i;\n                let e;\n                let done;\n                let value;\n                const tok = untilTokens || ';';\n                const index = parserInput.i;\n                const result = [];\n\n                function testCurrentChar() {\n                    const char = parserInput.currentChar();\n                    if (typeof tok === 'string') {\n                        return char === tok;\n                    } else {\n                        return tok.test(char);\n                    }\n                }\n                if (testCurrentChar()) {\n                    return;\n                }\n                value = [];\n                do {\n                    e = this.comment();\n                    if (e) {\n                        value.push(e);\n                        continue;\n                    }\n                    e = this.entity();\n                    if (e) {\n                        value.push(e);\n                    }\n                    if (parserInput.peek(',')) {\n                        value.push(new (tree.Anonymous)(',', parserInput.i));\n                        parserInput.$char(',');\n                    }\n                } while (e);\n\n                done = testCurrentChar();\n\n                if (value.length > 0) {\n                    value = new(tree.Expression)(value);\n                    if (done) {\n                        return value;\n                    }\n                    else {\n                        result.push(value);\n                    }\n                    // Preserve space before $parseUntil as it will not\n                    if (parserInput.prevChar() === ' ') {\n                        result.push(new tree.Anonymous(' ', index));\n                    }\n                }\n                parserInput.save();\n\n                value = parserInput.$parseUntil(tok);\n\n                if (value) {\n                    if (typeof value === 'string') {\n                        error(`Expected '${value}'`, 'Parse');\n                    }\n                    if (value.length === 1 && value[0] === ' ') {\n                        parserInput.forget();\n                        return new tree.Anonymous('', index);\n                    }\n                    /** @type {string} */\n                    let item;\n                    for (i = 0; i < value.length; i++) {\n                        item = value[i];\n                        if (Array.isArray(item)) {\n                            // Treat actual quotes as normal quoted values\n                            result.push(new tree.Quoted(item[0], item[1], true, index, fileInfo));\n                        }\n                        else {\n                            if (i === value.length - 1) {\n                                item = item.trim();\n                            }\n                            // Treat like quoted values, but replace vars like unquoted expressions\n                            const quote = new tree.Quoted('\\'', item, true, index, fileInfo);\n                            const variableRegex = /@([\\w-]+)/g;\n                            const propRegex = /\\$([\\w-]+)/g;\n                            if (variableRegex.test(item)) {\n                                warn('@[ident] in unknown values will not be evaluated as variables in the future. Use @{[ident]}', index, 'DEPRECATED');\n                            }\n                            if (propRegex.test(item)) {\n                                warn('$[ident] in unknown values will not be evaluated as property references in the future. Use ${[ident]}', index, 'DEPRECATED');\n                            }\n                            quote.variableRegex = /@([\\w-]+)|@{([\\w-]+)}/g;\n                            quote.propRegex = /\\$([\\w-]+)|\\${([\\w-]+)}/g;\n                            result.push(quote);\n                        }\n                    }\n                    parserInput.forget();\n                    return new tree.Expression(result, true);\n                }\n                parserInput.restore();\n            },\n\n            //\n            // An @import atrule\n            //\n            //     @import \"lib\";\n            //\n            // Depending on our environment, importing is done differently:\n            // In the browser, it's an XHR request, in Node, it would be a\n            // file-system operation. The function used for importing is\n            // stored in `import`, which we pass to the Import constructor.\n            //\n            'import': function () {\n                let path;\n                let features;\n                const index = parserInput.i;\n\n                const dir = parserInput.$re(/^@import\\s+/);\n\n                if (dir) {\n                    const options = (dir ? this.importOptions() : null) || {};\n\n                    if ((path = this.entities.quoted() || this.entities.url())) {\n                        features = this.mediaFeatures({});\n\n                        if (!parserInput.$char(';')) {\n                            parserInput.i = index;\n                            error('missing semi-colon or unrecognised media features on import');\n                        }\n                        features = features && new(tree.Value)(features);\n                        return new(tree.Import)(path, features, options, index + currentIndex, fileInfo);\n                    }\n                    else {\n                        parserInput.i = index;\n                        error('malformed import statement');\n                    }\n                }\n            },\n\n            importOptions: function() {\n                let o;\n                const options = {};\n                let optionName;\n                let value;\n\n                // list of options, surrounded by parens\n                if (!parserInput.$char('(')) { return null; }\n                do {\n                    o = this.importOption();\n                    if (o) {\n                        optionName = o;\n                        value = true;\n                        switch (optionName) {\n                            case 'css':\n                                optionName = 'less';\n                                value = false;\n                                break;\n                            case 'once':\n                                optionName = 'multiple';\n                                value = false;\n                                break;\n                        }\n                        options[optionName] = value;\n                        if (!parserInput.$char(',')) { break; }\n                    }\n                } while (o);\n                expectChar(')');\n                return options;\n            },\n\n            importOption: function() {\n                const opt = parserInput.$re(/^(less|css|multiple|once|inline|reference|optional)/);\n                if (opt) {\n                    return opt[1];\n                }\n            },\n\n            mediaFeature: function (syntaxOptions) {\n                const entities = this.entities;\n                const nodes = [];\n                let e;\n                let p;\n                let rangeP;\n                parserInput.save();\n                do {\n                    e = entities.declarationCall.bind(this)() || entities.keyword() || entities.variable() || entities.mixinLookup()\n                    if (e) {\n                        nodes.push(e);\n                    } else if (parserInput.$char('(')) {\n                        p = this.property();\n                        parserInput.save();\n                        if (!p && syntaxOptions.queryInParens && parserInput.$re(/^[0-9a-z-]*\\s*([<>]=|<=|>=|[<>]|=)/)) {\n                            parserInput.restore();\n                            p = this.condition();\n\n                            parserInput.save();\n                            rangeP = this.atomicCondition(null, p.rvalue);\n                            if (!rangeP) {\n                                parserInput.restore();\n                            }\n                        } else {\n                            parserInput.restore();\n                            e = this.value();\n                        }\n                        if (parserInput.$char(')')) {\n                            if (p && !e) {\n                                nodes.push(new (tree.Paren)(new (tree.QueryInParens)(p.op, p.lvalue, p.rvalue, rangeP ? rangeP.op : null, rangeP ? rangeP.rvalue : null, p._index)));\t\t\t\t \n                                e = p;\n                            } else if (p && e) {\n                                nodes.push(new (tree.Paren)(new (tree.Declaration)(p, e, null, null, parserInput.i + currentIndex, fileInfo, true)));\n                            } else if (e) {\n                                nodes.push(new(tree.Paren)(e));\n                            } else {\n                                error('badly formed media feature definition');\n                            }\n                        } else {\n                            error('Missing closing \\')\\'', 'Parse');\n                        }\n                    }\n                } while (e);\n\n                parserInput.forget();\n                if (nodes.length > 0) {\n                    return new(tree.Expression)(nodes);\n                }\n            },\n\n            mediaFeatures: function (syntaxOptions) {\n                const entities = this.entities;\n                const features = [];\n                let e;\n                do {\n                    e = this.mediaFeature(syntaxOptions);\n                    if (e) {\n                        features.push(e);\n                        if (!parserInput.$char(',')) { break; }\n                    } else {\n                        e = entities.variable() || entities.mixinLookup();\n                        if (e) {\n                            features.push(e);\n                            if (!parserInput.$char(',')) { break; }\n                        }\n                    }\n                } while (e);\n\n                return features.length > 0 ? features : null;\n            },\n\n            prepareAndGetNestableAtRule: function (treeType, index, debugInfo, syntaxOptions) {\n                const features = this.mediaFeatures(syntaxOptions);\n\n                const rules = this.block();\n\n                if (!rules) {\n                    error('media definitions require block statements after any features');\n                }\n\n                parserInput.forget();\n\n                const atRule = new (treeType)(rules, features, index + currentIndex, fileInfo);\n                if (context.dumpLineNumbers) {\n                    atRule.debugInfo = debugInfo;\n                }\n\n                return atRule;\n            },\n\n            nestableAtRule: function () {\n                let debugInfo;\n                const index = parserInput.i;\n\n                if (context.dumpLineNumbers) {\n                    debugInfo = getDebugInfo(index);\n                }\n                parserInput.save();\n\n                if (parserInput.$peekChar('@')) {\n                    if (parserInput.$str('@media')) {\n                        return this.prepareAndGetNestableAtRule(tree.Media, index, debugInfo, MediaSyntaxOptions);\n                    }\n                    \n                    if (parserInput.$str('@container')) {\n                        return this.prepareAndGetNestableAtRule(tree.Container, index, debugInfo, ContainerSyntaxOptions);\n                    }\n                }\n                \n                parserInput.restore();\n            },\n\n            //\n\n            // A @plugin directive, used to import plugins dynamically.\n            //\n            //     @plugin (args) \"lib\";\n            //\n            plugin: function () {\n                let path;\n                let args;\n                let options;\n                const index = parserInput.i;\n                const dir   = parserInput.$re(/^@plugin\\s+/);\n\n                if (dir) {\n                    args = this.pluginArgs();\n\n                    if (args) {\n                        options = {\n                            pluginArgs: args,\n                            isPlugin: true\n                        };\n                    }\n                    else {\n                        options = { isPlugin: true };\n                    }\n\n                    if ((path = this.entities.quoted() || this.entities.url())) {\n\n                        if (!parserInput.$char(';')) {\n                            parserInput.i = index;\n                            error('missing semi-colon on @plugin');\n                        }\n                        return new(tree.Import)(path, null, options, index + currentIndex, fileInfo);\n                    }\n                    else {\n                        parserInput.i = index;\n                        error('malformed @plugin statement');\n                    }\n                }\n            },\n\n            pluginArgs: function() {\n                // list of options, surrounded by parens\n                parserInput.save();\n                if (!parserInput.$char('(')) {\n                    parserInput.restore();\n                    return null;\n                }\n                const args = parserInput.$re(/^\\s*([^);]+)\\)\\s*/);\n                if (args[1]) {\n                    parserInput.forget();\n                    return args[1].trim();\n                }\n                else {\n                    parserInput.restore();\n                    return null;\n                }\n            },\n\n            //\n            // A CSS AtRule\n            //\n            //     @charset \"utf-8\";\n            //\n            atrule: function () {\n                const index = parserInput.i;\n                let name;\n                let value;\n                let rules;\n                let nonVendorSpecificName;\n                let hasIdentifier;\n                let hasExpression;\n                let hasUnknown;\n                let hasBlock = true;\n                let isRooted = true;\n                let isKeywordList = false;\n\n                if (parserInput.currentChar() !== '@') { return; }\n\n                value = this['import']() || this.plugin() || this.nestableAtRule();\n                if (value) {\n                    return value;\n                }\n\n                parserInput.save();\n\n                name = parserInput.$re(/^@[a-z-]+/);\n\n                if (!name) { return; }\n\n                nonVendorSpecificName = name;\n                if (name.charAt(1) == '-' && name.indexOf('-', 2) > 0) {\n                    nonVendorSpecificName = `@${name.slice(name.indexOf('-', 2) + 1)}`;\n                }\n\n                switch (nonVendorSpecificName) {\n                    case '@charset':\n                        hasIdentifier = true;\n                        hasBlock = false;\n                        break;\n                    case '@namespace':\n                        hasExpression = true;\n                        hasBlock = false;\n                        break;\n                    case '@keyframes':\n                    case '@counter-style':\n                        hasIdentifier = true;\n                        break;\n                    case '@document':\n                    case '@supports':\n                        hasUnknown = true;\n                        isRooted = false;\n                        break;\n                    case '@starting-style':\n                        isRooted = false;\n                        break;\n                    case '@layer':\n                        isRooted = false;\n                        break;\n                    default:\n                        hasUnknown = true;\n                        break;\n                }\n\n                parserInput.commentStore.length = 0;\n\n                if (hasIdentifier) {\n                    value = this.entity();\n                    if (!value) {\n                        error(`expected ${name} identifier`);\n                    }\n                } else if (hasExpression) {\n                    value = this.expression();\n                    if (!value) {\n                        error(`expected ${name} expression`);\n                    }\n                } else if (hasUnknown) {\n                    value = this.permissiveValue(/^[{;]/);\n                    hasBlock = (parserInput.currentChar() === '{');\n                    if (!value) {\n                        if (!hasBlock && parserInput.currentChar() !== ';') {\n                            error(`${name} rule is missing block or ending semi-colon`);\n                        }\n                    }\n                    else if (!value.value) {\n                        value = null;\n                    }\n                }\n\n                if (hasBlock) {\n                    rules = this.blockRuleset();\n\n                    parserInput.save();\n\n                    if (!rules && !isRooted) {\n                        value = this.entity();\n                        rules = this.blockRuleset();\n                    }\n\n                    if (!rules && !isRooted) {\n                        parserInput.restore();\n\n                        let e = [];\n                        value = this.entity();\n\n                        while (parserInput.$char(',')) {\n                            e.push(value);\n                            value = this.entity();\n                        }\n\n                        if (value && e.length > 0) {\n                            e.push(value);\n                            value = e;\n                            isKeywordList = true;\n                        } else {\n                            rules = this.blockRuleset();\n                        }\n                    } else {\n                        parserInput.forget();\n                    }\n                }\n\n                if (rules || isKeywordList || (!hasBlock && value && parserInput.$char(';'))) {\n                    parserInput.forget();\n                    return new(tree.AtRule)(name, value, rules, index + currentIndex, fileInfo,\n                        context.dumpLineNumbers ? getDebugInfo(index) : null,\n                        isRooted\n                    );\n                }\n\n                parserInput.restore('at-rule options not recognised');\n            },\n\n            //\n            // A Value is a comma-delimited list of Expressions\n            //\n            //     font-family: Baskerville, Georgia, serif;\n            //\n            // In a Rule, a Value represents everything after the `:`,\n            // and before the `;`.\n            //\n            value: function () {\n                let e;\n                const expressions = [];\n                const index = parserInput.i;\n\n                do {\n                    e = this.expression();\n                    if (e) {\n                        expressions.push(e);\n                        if (!parserInput.$char(',')) { break; }\n                    }\n                } while (e);\n\n                if (expressions.length > 0) {\n                    return new(tree.Value)(expressions, index + currentIndex);\n                }\n            },\n            important: function () {\n                if (parserInput.currentChar() === '!') {\n                    return parserInput.$re(/^! *important/);\n                }\n            },\n            sub: function () {\n                let a;\n                let e;\n\n                parserInput.save();\n                if (parserInput.$char('(')) {\n                    a = this.addition();\n                    if (a && parserInput.$char(')')) {\n                        parserInput.forget();\n                        e = new(tree.Expression)([a]);\n                        e.parens = true;\n                        return e;\n                    }\n                    parserInput.restore('Expected \\')\\'');\n                    return;\n                }\n                parserInput.restore();\n            },\n            colorOperand: function () {\n                parserInput.save();\n                         \n                // hsl or rgb or lch operand\n                const match = parserInput.$re(/^[lchrgbs]\\s+/);\n                if (match) {\n                    return new tree.Keyword(match[0]);\n                }\n\n                parserInput.restore();\n            },\n            multiplication: function () {\n                let m;\n                let a;\n                let op;\n                let operation;\n                let isSpaced;\n                m = this.operand();\n                if (m) {\n                    isSpaced = parserInput.isWhitespace(-1);\n                    while (true) {\n                        if (parserInput.peek(/^\\/[*/]/)) {\n                            break;\n                        }\n\n                        parserInput.save();\n\n                        op = parserInput.$char('/') || parserInput.$char('*');\n                        if (!op) {\n                            let index = parserInput.i;\n                            op = parserInput.$str('./');\n                            if (op) {\n                                warn('./ operator is deprecated', index, 'DEPRECATED');\n                            }\n                        }\n\n                        if (!op) { parserInput.forget(); break; }\n\n                        a = this.operand();\n\n                        if (!a) { parserInput.restore(); break; }\n                        parserInput.forget();\n\n                        m.parensInOp = true;\n                        a.parensInOp = true;\n                        operation = new(tree.Operation)(op, [operation || m, a], isSpaced);\n                        isSpaced = parserInput.isWhitespace(-1);\n                    }\n                    return operation || m;\n                }\n            },\n            addition: function () {\n                let m;\n                let a;\n                let op;\n                let operation;\n                let isSpaced;\n                m = this.multiplication();\n                if (m) {\n                    isSpaced = parserInput.isWhitespace(-1);\n                    while (true) {\n                        op = parserInput.$re(/^[-+]\\s+/) || (!isSpaced && (parserInput.$char('+') || parserInput.$char('-')));\n                        if (!op) {\n                            break;\n                        }\n                        a = this.multiplication();\n                        if (!a) {\n                            break;\n                        }\n\n                        m.parensInOp = true;\n                        a.parensInOp = true;\n                        operation = new(tree.Operation)(op, [operation || m, a], isSpaced);\n                        isSpaced = parserInput.isWhitespace(-1);\n                    }\n                    return operation || m;\n                }\n            },\n            conditions: function () {\n                let a;\n                let b;\n                const index = parserInput.i;\n                let condition;\n\n                a = this.condition(true);\n                if (a) {\n                    while (true) {\n                        if (!parserInput.peek(/^,\\s*(not\\s*)?\\(/) || !parserInput.$char(',')) {\n                            break;\n                        }\n                        b = this.condition(true);\n                        if (!b) {\n                            break;\n                        }\n                        condition = new(tree.Condition)('or', condition || a, b, index + currentIndex);\n                    }\n                    return condition || a;\n                }\n            },\n            condition: function (needsParens) {\n                let result;\n                let logical;\n                let next;\n                function or() {\n                    return parserInput.$str('or');\n                }\n\n                result = this.conditionAnd(needsParens);\n                if (!result) {\n                    return ;\n                }\n                logical = or();\n                if (logical) {\n                    next = this.condition(needsParens);\n                    if (next) {\n                        result = new(tree.Condition)(logical, result, next);\n                    } else {\n                        return ;\n                    }\n                }\n                return result;\n            },\n            conditionAnd: function (needsParens) {\n                let result;\n                let logical;\n                let next;\n                const self = this;\n                function insideCondition() {\n                    const cond = self.negatedCondition(needsParens) || self.parenthesisCondition(needsParens);\n                    if (!cond && !needsParens) {\n                        return self.atomicCondition(needsParens);\n                    }\n                    return cond;\n                }\n                function and() {\n                    return parserInput.$str('and');\n                }\n\n                result = insideCondition();\n                if (!result) {\n                    return ;\n                }\n                logical = and();\n                if (logical) {\n                    next = this.conditionAnd(needsParens);\n                    if (next) {\n                        result = new(tree.Condition)(logical, result, next);\n                    } else {\n                        return ;\n                    }\n                }\n                return result;\n            },\n            negatedCondition: function (needsParens) {\n                if (parserInput.$str('not')) {\n                    const result = this.parenthesisCondition(needsParens);\n                    if (result) {\n                        result.negate = !result.negate;\n                    }\n                    return result;\n                }\n            },\n            parenthesisCondition: function (needsParens) {\n                function tryConditionFollowedByParenthesis(me) {\n                    let body;\n                    parserInput.save();\n                    body = me.condition(needsParens);\n                    if (!body) {\n                        parserInput.restore();\n                        return ;\n                    }\n                    if (!parserInput.$char(')')) {\n                        parserInput.restore();\n                        return ;\n                    }\n                    parserInput.forget();\n                    return body;\n                }\n\n                let body;\n                parserInput.save();\n                if (!parserInput.$str('(')) {\n                    parserInput.restore();\n                    return ;\n                }\n                body = tryConditionFollowedByParenthesis(this);\n                if (body) {\n                    parserInput.forget();\n                    return body;\n                }\n\n                body = this.atomicCondition(needsParens);\n                if (!body) {\n                    parserInput.restore();\n                    return ;\n                }\n                if (!parserInput.$char(')')) {\n                    parserInput.restore(`expected ')' got '${parserInput.currentChar()}'`);\n                    return ;\n                }\n                parserInput.forget();\n                return body;\n            },\n            atomicCondition: function (needsParens, preparsedCond) {\n                const entities = this.entities;\n                const index = parserInput.i;\n                let a;\n                let b;\n                let c;\n                let op;\n\n                const cond = (function() {\n                    return this.addition() || entities.keyword() || entities.quoted() || entities.mixinLookup();\n                }).bind(this)\n\n                if (preparsedCond) {\n                    a = preparsedCond;\n                } else {\n                    a = cond();\n                }\n\n                if (a) {\n                    if (parserInput.$char('>')) {\n                        if (parserInput.$char('=')) {\n                            op = '>=';\n                        } else {\n                            op = '>';\n                        }\n                    } else\n                    if (parserInput.$char('<')) {\n                        if (parserInput.$char('=')) {\n                            op = '<=';\n                        } else {\n                            op = '<';\n                        }\n                    } else\n                    if (parserInput.$char('=')) {\n                        if (parserInput.$char('>')) {\n                            op = '=>';\n                        } else if (parserInput.$char('<')) {\n                            op = '=<';\n                        } else {\n                            op = '=';\n                        }\n                    }\n                    if (op) {\n                        b = cond();\n                        if (b) {\n                            c = new(tree.Condition)(op, a, b, index + currentIndex, false);\n                        } else {\n                            error('expected expression');\n                        }\n                    } else if (!preparsedCond) {\n                        c = new(tree.Condition)('=', a, new(tree.Keyword)('true'), index + currentIndex, false);\n                    }\n                    return c;\n                }\n            },\n\n            //\n            // An operand is anything that can be part of an operation,\n            // such as a Color, or a Variable\n            //\n            operand: function () {\n                const entities = this.entities;\n                let negate;\n\n                if (parserInput.peek(/^-[@$(]/)) {\n                    negate = parserInput.$char('-');\n                }\n\n                let o = this.sub() || entities.dimension() ||\n                        entities.color() || entities.variable() ||\n                        entities.property() || entities.call() ||\n                        entities.quoted(true) || entities.colorKeyword() ||\n                        this.colorOperand() || entities.mixinLookup();\n\n                if (negate) {\n                    o.parensInOp = true;\n                    o = new(tree.Negative)(o);\n                }\n\n                return o;\n            },\n\n            //\n            // Expressions either represent mathematical operations,\n            // or white-space delimited Entities.\n            //\n            //     1px solid black\n            //     @var * 2\n            //\n            expression: function () {\n                const entities = [];\n                let e;\n                let delim;\n                const index = parserInput.i;\n\n                do {\n                    e = this.comment();\n                    if (e && !e.isLineComment) {\n                        entities.push(e);\n                        continue;\n                    }\n                    e = this.addition() || this.entity();\n\n                    if (e instanceof tree.Comment) {\n                        e = null;\n                    }\n\n                    if (e) {\n                        entities.push(e);\n                        // operations do not allow keyword \"/\" dimension (e.g. small/20px) so we support that here\n                        if (!parserInput.peek(/^\\/[/*]/)) {\n                            delim = parserInput.$char('/');\n                            if (delim) {\n                                entities.push(new(tree.Anonymous)(delim, index + currentIndex));\n                            }\n                        }\n                    }\n                } while (e);\n                if (entities.length > 0) {\n                    return new(tree.Expression)(entities);\n                }\n            },\n            property: function () {\n                const name = parserInput.$re(/^(\\*?-?[_a-zA-Z0-9-]+)\\s*:/);\n                if (name) {\n                    return name[1];\n                }\n            },\n            ruleProperty: function () {\n                let name = [];\n                const index = [];\n                let s;\n                let k;\n\n                parserInput.save();\n\n                const simpleProperty = parserInput.$re(/^([_a-zA-Z0-9-]+)\\s*:/);\n                if (simpleProperty) {\n                    name = [new(tree.Keyword)(simpleProperty[1])];\n                    parserInput.forget();\n                    return name;\n                }\n\n                function match(re) {\n                    const i = parserInput.i;\n                    const chunk = parserInput.$re(re);\n                    if (chunk) {\n                        index.push(i);\n                        return name.push(chunk[1]);\n                    }\n                }\n\n                match(/^(\\*?)/);\n                while (true) {\n                    if (!match(/^((?:[\\w-]+)|(?:[@$]\\{[\\w-]+\\}))/)) {\n                        break;\n                    }\n                }\n\n                if ((name.length > 1) && match(/^((?:\\+_|\\+)?)\\s*:/)) {\n                    parserInput.forget();\n\n                    // at last, we have the complete match now. move forward,\n                    // convert name particles to tree objects and return:\n                    if (name[0] === '') {\n                        name.shift();\n                        index.shift();\n                    }\n                    for (k = 0; k < name.length; k++) {\n                        s = name[k];\n                        name[k] = (s.charAt(0) !== '@' && s.charAt(0) !== '$') ?\n                            new(tree.Keyword)(s) :\n                            (s.charAt(0) === '@' ?\n                                new(tree.Variable)(`@${s.slice(2, -1)}`, index[k] + currentIndex, fileInfo) :\n                                new(tree.Property)(`$${s.slice(2, -1)}`, index[k] + currentIndex, fileInfo));\n                    }\n                    return name;\n                }\n                parserInput.restore();\n            }\n        }\n    };\n};\nParser.serializeVars = vars => {\n    let s = '';\n\n    for (const name in vars) {\n        if (Object.hasOwnProperty.call(vars, name)) {\n            const value = vars[name];\n            s += `${((name[0] === '@') ? '' : '@') + name}: ${value}${(String(value).slice(-1) === ';') ? '' : ';'}`;\n        }\n    }\n\n    return s;\n};\n\nexport default Parser;"]}