{"version": 3, "file": "common.js", "sourceRoot": "", "sources": ["../../source/types/common.ts"], "names": [], "mappings": "", "sourcesContent": ["// eslint-disable-next-line @typescript-eslint/ban-types\nexport type Primitive = null | undefined | string | number | boolean | symbol | bigint;\n\nexport type Required<T, K extends keyof T = keyof T> = T & {[P in K]-?: T[P]};\n\nexport type LiteralUnion<LiteralType extends BaseType, BaseType extends Primitive> =\n\t| LiteralType\n\t| (BaseType & {_?: never});\n"]}