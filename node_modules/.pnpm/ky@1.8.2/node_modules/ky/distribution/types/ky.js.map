{"version": 3, "file": "ky.js", "sourceRoot": "", "sources": ["../../source/types/ky.ts"], "names": [], "mappings": "", "sourcesContent": ["import {type stop} from '../core/constants.js';\nimport type {Input, Options} from './options.js';\nimport type {ResponsePromise} from './ResponsePromise.js';\n\nexport type KyInstance = {\n\t/**\n\tFetch the given `url`.\n\n\t@param url - `Request` object, `URL` object, or URL string.\n\t@returns A promise with `Body` method added.\n\n\t@example\n\t```\n\timport ky from 'ky';\n\n\tconst json = await ky('https://example.com', {json: {foo: true}}).json();\n\n\tconsole.log(json);\n\t//=> `{data: '🦄'}`\n\t```\n\t*/\n\t<T>(url: Input, options?: Options): ResponsePromise<T>;\n\n\t/**\n\tFetch the given `url` using the option `{method: 'get'}`.\n\n\t@param url - `Request` object, `URL` object, or URL string.\n\t@returns A promise with `Body` methods added.\n\t*/\n\tget: <T>(url: Input, options?: Options) => ResponsePromise<T>;\n\n\t/**\n\tFetch the given `url` using the option `{method: 'post'}`.\n\n\t@param url - `Request` object, `URL` object, or URL string.\n\t@returns A promise with `Body` methods added.\n\t*/\n\tpost: <T>(url: Input, options?: Options) => ResponsePromise<T>;\n\n\t/**\n\tFetch the given `url` using the option `{method: 'put'}`.\n\n\t@param url - `Request` object, `URL` object, or URL string.\n\t@returns A promise with `Body` methods added.\n\t*/\n\tput: <T>(url: Input, options?: Options) => ResponsePromise<T>;\n\n\t/**\n\tFetch the given `url` using the option `{method: 'delete'}`.\n\n\t@param url - `Request` object, `URL` object, or URL string.\n\t@returns A promise with `Body` methods added.\n\t*/\n\tdelete: <T>(url: Input, options?: Options) => ResponsePromise<T>;\n\n\t/**\n\tFetch the given `url` using the option `{method: 'patch'}`.\n\n\t@param url - `Request` object, `URL` object, or URL string.\n\t@returns A promise with `Body` methods added.\n\t*/\n\tpatch: <T>(url: Input, options?: Options) => ResponsePromise<T>;\n\n\t/**\n\tFetch the given `url` using the option `{method: 'head'}`.\n\n\t@param url - `Request` object, `URL` object, or URL string.\n\t@returns A promise with `Body` methods added.\n\t*/\n\thead: (url: Input, options?: Options) => ResponsePromise;\n\n\t/**\n\tCreate a new Ky instance with complete new defaults.\n\n\t@returns A new Ky instance.\n\t*/\n\tcreate: (defaultOptions?: Options) => KyInstance;\n\n\t/**\n\tCreate a new Ky instance with some defaults overridden with your own.\n\n\tIn contrast to `ky.create()`, `ky.extend()` inherits defaults from its parent.\n\n\tYou can also refer to parent defaults by providing a function to `.extend()`.\n\n\t@example\n\t```\n\timport ky from 'ky';\n\n\tconst api = ky.create({prefixUrl: 'https://example.com/api'});\n\n\tconst usersApi = api.extend((options) => ({prefixUrl: `${options.prefixUrl}/users`}));\n\n\tconst response = await usersApi.get('123');\n\t//=> 'https://example.com/api/users/123'\n\n\tconst response = await api.get('version');\n\t//=> 'https://example.com/api/version'\n\t```\n\n\t@returns A new Ky instance.\n\t*/\n\textend: (defaultOptions: Options | ((parentOptions: Options) => Options)) => KyInstance;\n\n\t/**\n\tA `Symbol` that can be returned by a `beforeRetry` hook to stop the retry. This will also short circuit the remaining `beforeRetry` hooks.\n\n\tNote: Returning this symbol makes Ky abort and return with an `undefined` response. Be sure to check for a response before accessing any properties on it or use [optional chaining](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Operators/Optional_chaining). It is also incompatible with body methods, such as `.json()` or `.text()`, because there is no response to parse. In general, we recommend throwing an error instead of returning this symbol, as that will cause Ky to abort and then throw, which avoids these limitations.\n\n\tA valid use-case for `ky.stop` is to prevent retries when making requests for side effects, where the returned data is not important. For example, logging client activity to the server.\n\n\t@example\n\t```\n\timport ky from 'ky';\n\n\tconst options = {\n\t\thooks: {\n\t\t\tbeforeRetry: [\n\t\t\t\tasync ({request, options, error, retryCount}) => {\n\t\t\t\t\tconst shouldStopRetry = await ky('https://example.com/api');\n\t\t\t\t\tif (shouldStopRetry) {\n\t\t\t\t\t\treturn ky.stop;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t]\n\t\t}\n\t};\n\n\t// Note that response will be `undefined` in case `ky.stop` is returned.\n\tconst response = await ky.post('https://example.com', options);\n\n\t// Using `.text()` or other body methods is not supported.\n\tconst text = await ky('https://example.com', options).text();\n\t```\n\t*/\n\treadonly stop: typeof stop;\n};\n"]}