{"version": 3, "file": "options.js", "sourceRoot": "", "sources": ["../../source/types/options.ts"], "names": [], "mappings": "", "sourcesContent": ["import type {LiteralUnion, Required} from './common.js';\nimport type {Hooks} from './hooks.js';\nimport type {RetryOptions} from './retry.js';\n\n// eslint-disable-next-line unicorn/prevent-abbreviations\nexport type SearchParamsInit = string | string[][] | Record<string, string> | URLSearchParams | undefined;\n\n// eslint-disable-next-line unicorn/prevent-abbreviations\nexport type SearchParamsOption = SearchParamsInit | Record<string, string | number | boolean> | Array<Array<string | number | boolean>>;\n\nexport type HttpMethod = 'get' | 'post' | 'put' | 'patch' | 'head' | 'delete';\n\nexport type Input = string | URL | Request;\n\nexport type Progress = {\n\tpercent: number;\n\ttransferredBytes: number;\n\n\t/**\n\tNote: If it's not possible to retrieve the body size, it will be `0`.\n\t*/\n\ttotalBytes: number;\n};\n\n// Not HeadersInit directly because @types/node doesn't export it\nexport type KyHeadersInit = NonNullable<RequestInit['headers']> | Record<string, string | undefined>;\n\n/**\nCustom Ky options\n*/\n\nexport type KyOptions = {\n\t/**\n\tShortcut for sending JSON. Use this instead of the `body` option.\n\n\tAccepts any plain object or value, which will be `JSON.stringify()`'d and sent in the body with the correct header set.\n\t*/\n\tjson?: unknown;\n\n\t/**\n\tUser-defined JSON-parsing function.\n\n\tUse-cases:\n\t1. Parse JSON via the [`bourne` package](https://github.com/hapijs/bourne) to protect from prototype pollution.\n\t2. Parse JSON with [`reviver` option of `JSON.parse()`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON/parse).\n\n\t@default JSON.parse()\n\n\t@example\n\t```\n\timport ky from 'ky';\n\timport bourne from '@hapijs/bourne';\n\n\tconst json = await ky('https://example.com', {\n\t\tparseJson: text => bourne(text)\n\t}).json();\n\t```\n\t*/\n\tparseJson?: (text: string) => unknown;\n\n\t/**\n\tUser-defined JSON-stringifying function.\n\n\tUse-cases:\n\t1. Stringify JSON with a custom `replacer` function.\n\n\t@default JSON.stringify()\n\n\t@example\n\t```\n\timport ky from 'ky';\n\timport {DateTime} from 'luxon';\n\n\tconst json = await ky('https://example.com', {\n\t\tstringifyJson: data => JSON.stringify(data, (key, value) => {\n\t\t\tif (key.endsWith('_at')) {\n\t\t\t\treturn DateTime.fromISO(value).toSeconds();\n\t\t\t}\n\n\t\t\treturn value;\n\t\t})\n\t}).json();\n\t```\n\t*/\n\tstringifyJson?: (data: unknown) => string;\n\n\t/**\n\tSearch parameters to include in the request URL. Setting this will override all existing search parameters in the input URL.\n\n\tAccepts any value supported by [`URLSearchParams()`](https://developer.mozilla.org/en-US/docs/Web/API/URLSearchParams/URLSearchParams).\n\t*/\n\tsearchParams?: SearchParamsOption;\n\n\t/**\n\tA prefix to prepend to the `input` URL when making the request. It can be any valid URL, either relative or absolute. A trailing slash `/` is optional and will be added automatically, if needed, when it is joined with `input`. Only takes effect when `input` is a string. The `input` argument cannot start with a slash `/` when using this option.\n\n\tUseful when used with [`ky.extend()`](#kyextenddefaultoptions) to create niche-specific Ky-instances.\n\n\tNotes:\n\t - After `prefixUrl` and `input` are joined, the result is resolved against the [base URL](https://developer.mozilla.org/en-US/docs/Web/API/Node/baseURI) of the page (if any).\n\t - Leading slashes in `input` are disallowed when using this option to enforce consistency and avoid confusion about how the `input` URL is handled, given that `input` will not follow the normal URL resolution rules when `prefixUrl` is being used, which changes the meaning of a leading slash.\n\n\t@example\n\t```\n\timport ky from 'ky';\n\n\t// On https://example.com\n\n\tconst response = await ky('unicorn', {prefixUrl: '/api'});\n\t//=> 'https://example.com/api/unicorn'\n\n\tconst response = await ky('unicorn', {prefixUrl: 'https://cats.com'});\n\t//=> 'https://cats.com/unicorn'\n\t```\n\t*/\n\tprefixUrl?: URL | string;\n\n\t/**\n\tAn object representing `limit`, `methods`, `statusCodes`, `afterStatusCodes`, and `maxRetryAfter` fields for maximum retry count, allowed methods, allowed status codes, status codes allowed to use the [`Retry-After`](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Retry-After) time, and maximum [`Retry-After`](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Retry-After) time.\n\n\tIf `retry` is a number, it will be used as `limit` and other defaults will remain in place.\n\n\tIf the response provides an HTTP status contained in `afterStatusCodes`, Ky will wait until the date or timeout given in the [`Retry-After`](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Retry-After) header has passed to retry the request. If `Retry-After` is missing, the non-standard [`RateLimit-Reset`](https://www.ietf.org/archive/id/draft-polli-ratelimit-headers-02.html#section-3.3) header is used in its place as a fallback. If the provided status code is not in the list, the [`Retry-After`](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Retry-After) header will be ignored.\n\n\tIf `maxRetryAfter` is set to `undefined`, it will use `options.timeout`. If [`Retry-After`](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Retry-After) header is greater than `maxRetryAfter`, it will cancel the request.\n\n\tBy default, delays between retries are calculated with the function `0.3 * (2 ** (attemptCount - 1)) * 1000`, where `attemptCount` is the attempt number (starts from 1), however this can be changed by passing a `delay` function.\n\n\tRetries are not triggered following a timeout.\n\n\t@example\n\t```\n\timport ky from 'ky';\n\n\tconst json = await ky('https://example.com', {\n\t\tretry: {\n\t\t\tlimit: 10,\n\t\t\tmethods: ['get'],\n\t\t\tstatusCodes: [413]\n\t\t}\n\t}).json();\n\t```\n\t*/\n\tretry?: RetryOptions | number;\n\n\t/**\n\tTimeout in milliseconds for getting a response, including any retries. Can not be greater than 2147483647.\n\tIf set to `false`, there will be no timeout.\n\n\t@default 10000\n\t*/\n\ttimeout?: number | false;\n\n\t/**\n\tHooks allow modifications during the request lifecycle. Hook functions may be async and are run serially.\n\t*/\n\thooks?: Hooks;\n\n\t/**\n\tThrow an `HTTPError` when, after following redirects, the response has a non-2xx status code. To also throw for redirects instead of following them, set the [`redirect`](https://developer.mozilla.org/en-US/docs/Web/API/WindowOrWorkerGlobalScope/fetch#Parameters) option to `'manual'`.\n\n\tSetting this to `false` may be useful if you are checking for resource availability and are expecting error responses.\n\n\tNote: If `false`, error responses are considered successful and the request will not be retried.\n\n\t@default true\n\t*/\n\tthrowHttpErrors?: boolean;\n\n\t/**\n\tDownload progress event handler.\n\n\t@param progress - Object containing download progress information.\n\t@param chunk - Data that was received. Note: It's empty for the first call.\n\n\t@example\n\t```\n\timport ky from 'ky';\n\n\tconst response = await ky('https://example.com', {\n\t\tonDownloadProgress: (progress, chunk) => {\n\t\t\t// Example output:\n\t\t\t// `0% - 0 of 1271 bytes`\n\t\t\t// `100% - 1271 of 1271 bytes`\n\t\t\tconsole.log(`${progress.percent * 100}% - ${progress.transferredBytes} of ${progress.totalBytes} bytes`);\n\t\t}\n\t});\n\t```\n\t*/\n\tonDownloadProgress?: (progress: Progress, chunk: Uint8Array) => void;\n\n\t/**\n\tUpload progress event handler.\n\n\t@param progress - Object containing upload progress information.\n\t@param chunk - Data that was sent. Note: It's empty for the last call.\n\n\t@example\n\t```\n\timport ky from 'ky';\n\n\tconst response = await ky.post('https://example.com/upload', {\n\t\tbody: largeFile,\n\t\tonUploadProgress: (progress, chunk) => {\n\t\t\t// Example output:\n\t\t\t// `0% - 0 of 1271 bytes`\n\t\t\t// `100% - 1271 of 1271 bytes`\n\t\t\tconsole.log(`${progress.percent * 100}% - ${progress.transferredBytes} of ${progress.totalBytes} bytes`);\n\t\t}\n\t});\n\t```\n\t*/\n\tonUploadProgress?: (progress: Progress, chunk: Uint8Array) => void;\n\n\t/**\n\tUser-defined `fetch` function.\n\tHas to be fully compatible with the [Fetch API](https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API) standard.\n\n\tUse-cases:\n\t1. Use custom `fetch` implementations like [`isomorphic-unfetch`](https://www.npmjs.com/package/isomorphic-unfetch).\n\t2. Use the `fetch` wrapper function provided by some frameworks that use server-side rendering (SSR).\n\n\t@default fetch\n\n\t@example\n\t```\n\timport ky from 'ky';\n\timport fetch from 'isomorphic-unfetch';\n\n\tconst json = await ky('https://example.com', {fetch}).json();\n\t```\n\t*/\n\tfetch?: (input: Input, init?: RequestInit) => Promise<Response>;\n};\n\n/**\nEach key from KyOptions is present and set to `true`.\n\nThis type is used for identifying and working with the known keys in KyOptions.\n*/\nexport type KyOptionsRegistry = {[K in keyof KyOptions]-?: true};\n\n/**\nOptions are the same as `window.fetch`, except for the KyOptions\n*/\nexport interface Options extends KyOptions, Omit<RequestInit, 'headers'> { // eslint-disable-line @typescript-eslint/consistent-type-definitions -- This must stay an interface so that it can be extended outside of Ky for use in `ky.create`.\n\t/**\n\tHTTP method used to make the request.\n\n\tInternally, the standard methods (`GET`, `POST`, `PUT`, `PATCH`, `HEAD` and `DELETE`) are uppercased in order to avoid server errors due to case sensitivity.\n\t*/\n\tmethod?: LiteralUnion<HttpMethod, string>;\n\n\t/**\n\tHTTP headers used to make the request.\n\n\tYou can pass a `Headers` instance or a plain object.\n\n\tYou can remove a header with `.extend()` by passing the header with an `undefined` value.\n\n\t@example\n\t```\n\timport ky from 'ky';\n\n\tconst url = 'https://sindresorhus.com';\n\n\tconst original = ky.create({\n\t\theaders: {\n\t\t\trainbow: 'rainbow',\n\t\t\tunicorn: 'unicorn'\n\t\t}\n\t});\n\n\tconst extended = original.extend({\n\t\theaders: {\n\t\t\trainbow: undefined\n\t\t}\n\t});\n\n\tconst response = await extended(url).json();\n\n\tconsole.log('rainbow' in response);\n\t//=> false\n\n\tconsole.log('unicorn' in response);\n\t//=> true\n\t```\n\t*/\n\theaders?: KyHeadersInit;\n}\n\nexport type InternalOptions = Required<\nOmit<Options, 'hooks' | 'retry'>,\n'fetch' | 'prefixUrl' | 'timeout'\n> & {\n\theaders: Required<Headers>;\n\thooks: Required<Hooks>;\n\tretry: Required<RetryOptions>;\n\tprefixUrl: string;\n};\n\n/**\nNormalized options passed to the `fetch` call and the `beforeRequest` hooks.\n*/\nexport interface NormalizedOptions extends RequestInit { // eslint-disable-line @typescript-eslint/consistent-type-definitions -- This must stay an interface so that it can be extended outside of Ky for use in `ky.create`.\n\t// Extended from `RequestInit`, but ensured to be set (not optional).\n\tmethod: NonNullable<RequestInit['method']>;\n\tcredentials?: NonNullable<RequestInit['credentials']>;\n\n\t// Extended from custom `KyOptions`, but ensured to be set (not optional).\n\tretry: RetryOptions;\n\tprefixUrl: string;\n\tonDownloadProgress: Options['onDownloadProgress'];\n\tonUploadProgress: Options['onUploadProgress'];\n}\n\nexport type {RetryOptions} from './retry.js';\n"]}