{"version": 3, "file": "ResponsePromise.js", "sourceRoot": "", "sources": ["../../source/types/ResponsePromise.ts"], "names": [], "mappings": "", "sourcesContent": ["/**\nReturns a `Response` object with `Body` methods added for convenience. So you can, for example, call `ky.get(input).json()` directly without having to await the `Response` first. When called like that, an appropriate `Accept` header will be set depending on the body method used. Unlike the `Body` methods of `window.Fetch`; these will throw an `HTTPError` if the response status is not in the range of `200...299`. Also, `.json()` will return an empty string if body is empty or the response status is `204` instead of throwing a parse error due to an empty body.\n*/\nimport {type KyResponse} from './response.js';\n\nexport type ResponsePromise<T = unknown> = {\n\tarrayBuffer: () => Promise<ArrayBuffer>;\n\n\tblob: () => Promise<Blob>;\n\n\tformData: () => Promise<FormData>;\n\n\t// TODO: Use `json<T extends JSONValue>(): Promise<T>;` when it's fixed in TS.\n\t// See https://github.com/microsoft/TypeScript/issues/15300 and https://github.com/sindresorhus/ky/pull/80\n\t/**\n\tGet the response body as JSON.\n\n\t@example\n\t```\n\timport ky from 'ky';\n\n\tconst json = await ky(…).json();\n\t```\n\n\t@example\n\t```\n\timport ky from 'ky';\n\n\tinterface Result {\n\t\tvalue: number;\n\t}\n\n\tconst result1 = await ky(…).json<Result>();\n\t// or\n\tconst result2 = await ky<Result>(…).json();\n\t```\n\t*/\n\tjson: <J = T>() => Promise<J>;\n\n\ttext: () => Promise<string>;\n} & Promise<KyResponse<T>>;\n"]}