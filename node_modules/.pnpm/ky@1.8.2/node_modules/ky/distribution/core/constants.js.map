{"version": 3, "file": "constants.js", "sourceRoot": "", "sources": ["../../source/core/constants.ts"], "names": [], "mappings": "AAIA,MAAM,CAAC,MAAM,sBAAsB,GAAG,CAAC,GAAG,EAAE;IAC3C,IAAI,cAAc,GAAG,KAAK,CAAC;IAC3B,IAAI,cAAc,GAAG,KAAK,CAAC;IAC3B,MAAM,sBAAsB,GAAG,OAAO,UAAU,CAAC,cAAc,KAAK,UAAU,CAAC;IAC/E,MAAM,eAAe,GAAG,OAAO,UAAU,CAAC,OAAO,KAAK,UAAU,CAAC;IAEjE,IAAI,sBAAsB,IAAI,eAAe,EAAE,CAAC;QAC/C,IAAI,CAAC;YACJ,cAAc,GAAG,IAAI,UAAU,CAAC,OAAO,CAAC,uBAAuB,EAAE;gBAChE,IAAI,EAAE,IAAI,UAAU,CAAC,cAAc,EAAE;gBACrC,MAAM,EAAE,MAAM;gBACd,yCAAyC;gBACzC,IAAI,MAAM;oBACT,cAAc,GAAG,IAAI,CAAC;oBACtB,OAAO,MAAM,CAAC;gBACf,CAAC;aACD,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAChC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAChB,6EAA6E;YAC7E,IAAI,KAAK,YAAY,KAAK,IAAI,KAAK,CAAC,OAAO,KAAK,2BAA2B,EAAE,CAAC;gBAC7E,OAAO,KAAK,CAAC;YACd,CAAC;YAED,MAAM,KAAK,CAAC;QACb,CAAC;IACF,CAAC;IAED,OAAO,cAAc,IAAI,CAAC,cAAc,CAAC;AAC1C,CAAC,CAAC,EAAE,CAAC;AAEL,MAAM,CAAC,MAAM,uBAAuB,GAAG,OAAO,UAAU,CAAC,eAAe,KAAK,UAAU,CAAC;AACxF,MAAM,CAAC,MAAM,mBAAmB,GAAG,OAAO,UAAU,CAAC,WAAW,KAAK,UAAU,IAAI,OAAO,UAAU,CAAC,WAAW,CAAC,GAAG,KAAK,UAAU,CAAC;AACpI,MAAM,CAAC,MAAM,uBAAuB,GAAG,OAAO,UAAU,CAAC,cAAc,KAAK,UAAU,CAAC;AACvF,MAAM,CAAC,MAAM,gBAAgB,GAAG,OAAO,UAAU,CAAC,QAAQ,KAAK,UAAU,CAAC;AAE1E,MAAM,CAAC,MAAM,cAAc,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAU,CAAC;AAEzF,MAAM,QAAQ,GAAG,GAA0B,EAAE,CAAC,SAAyB,CAAC;AACxE,QAAQ,EAEJ,CAAC;AAEL,MAAM,CAAC,MAAM,aAAa,GAAG;IAC5B,IAAI,EAAE,kBAAkB;IACxB,IAAI,EAAE,QAAQ;IACd,QAAQ,EAAE,qBAAqB;IAC/B,WAAW,EAAE,KAAK;IAClB,IAAI,EAAE,KAAK;CACF,CAAC;AAEX,oDAAoD;AACpD,MAAM,CAAC,MAAM,cAAc,GAAG,aAAa,CAAC;AAE5C,8EAA8E;AAC9E,MAAM,CAAC,MAAM,qBAAqB,GAAG,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,0CAA0C,CAAC,CAAC,MAAM,CAAC;AAEjH,MAAM,CAAC,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;AAEnC,MAAM,CAAC,MAAM,YAAY,GAAsB;IAC9C,IAAI,EAAE,IAAI;IACV,SAAS,EAAE,IAAI;IACf,aAAa,EAAE,IAAI;IACnB,YAAY,EAAE,IAAI;IAClB,SAAS,EAAE,IAAI;IACf,KAAK,EAAE,IAAI;IACX,OAAO,EAAE,IAAI;IACb,KAAK,EAAE,IAAI;IACX,eAAe,EAAE,IAAI;IACrB,kBAAkB,EAAE,IAAI;IACxB,gBAAgB,EAAE,IAAI;IACtB,KAAK,EAAE,IAAI;CACX,CAAC;AAEF,MAAM,CAAC,MAAM,sBAAsB,GAAwB;IAC1D,MAAM,EAAE,IAAI;IACZ,OAAO,EAAE,IAAI;IACb,IAAI,EAAE,IAAI;IACV,IAAI,EAAE,IAAI;IACV,WAAW,EAAE,IAAI;IACjB,KAAK,EAAE,IAAI;IACX,QAAQ,EAAE,IAAI;IACd,QAAQ,EAAE,IAAI;IACd,cAAc,EAAE,IAAI;IACpB,SAAS,EAAE,IAAI;IACf,SAAS,EAAE,IAAI;IACf,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE,IAAI;IACZ,UAAU,EAAE,IAAI;IAChB,MAAM,EAAE,IAAI;IACZ,QAAQ,EAAE,IAAI;CACd,CAAC", "sourcesContent": ["import type {Expect, Equal} from '@type-challenges/utils';\nimport {type HttpMethod, type KyOptionsRegistry} from '../types/options.js';\nimport {type RequestInitRegistry} from '../types/request.js';\n\nexport const supportsRequestStreams = (() => {\n\tlet duplexAccessed = false;\n\tlet hasContentType = false;\n\tconst supportsReadableStream = typeof globalThis.ReadableStream === 'function';\n\tconst supportsRequest = typeof globalThis.Request === 'function';\n\n\tif (supportsReadableStream && supportsRequest) {\n\t\ttry {\n\t\t\thasContentType = new globalThis.Request('https://empty.invalid', {\n\t\t\t\tbody: new globalThis.ReadableStream(),\n\t\t\t\tmethod: 'POST',\n\t\t\t\t// @ts-expect-error - Types are outdated.\n\t\t\t\tget duplex() {\n\t\t\t\t\tduplexAccessed = true;\n\t\t\t\t\treturn 'half';\n\t\t\t\t},\n\t\t\t}).headers.has('Content-Type');\n\t\t} catch (error) {\n\t\t\t// QQBrowser on iOS throws \"unsupported BodyInit type\" error (see issue #581)\n\t\t\tif (error instanceof Error && error.message === 'unsupported BodyInit type') {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\tthrow error;\n\t\t}\n\t}\n\n\treturn duplexAccessed && !hasContentType;\n})();\n\nexport const supportsAbortController = typeof globalThis.AbortController === 'function';\nexport const supportsAbortSignal = typeof globalThis.AbortSignal === 'function' && typeof globalThis.AbortSignal.any === 'function';\nexport const supportsResponseStreams = typeof globalThis.ReadableStream === 'function';\nexport const supportsFormData = typeof globalThis.FormData === 'function';\n\nexport const requestMethods = ['get', 'post', 'put', 'patch', 'head', 'delete'] as const;\n\nconst validate = <T extends Array<true>>() => undefined as unknown as T;\nvalidate<[\n\tExpect<Equal<typeof requestMethods[number], HttpMethod>>,\n]>();\n\nexport const responseTypes = {\n\tjson: 'application/json',\n\ttext: 'text/*',\n\tformData: 'multipart/form-data',\n\tarrayBuffer: '*/*',\n\tblob: '*/*',\n} as const;\n\n// The maximum value of a 32bit int (see issue #117)\nexport const maxSafeTimeout = 2_147_483_647;\n\n// Size in bytes of a typical form boundary, used to help estimate upload size\nexport const usualFormBoundarySize = new TextEncoder().encode('------WebKitFormBoundaryaxpyiPgbbPti10Rw').length;\n\nexport const stop = Symbol('stop');\n\nexport const kyOptionKeys: KyOptionsRegistry = {\n\tjson: true,\n\tparseJson: true,\n\tstringifyJson: true,\n\tsearchParams: true,\n\tprefixUrl: true,\n\tretry: true,\n\ttimeout: true,\n\thooks: true,\n\tthrowHttpErrors: true,\n\tonDownloadProgress: true,\n\tonUploadProgress: true,\n\tfetch: true,\n};\n\nexport const requestOptionsRegistry: RequestInitRegistry = {\n\tmethod: true,\n\theaders: true,\n\tbody: true,\n\tmode: true,\n\tcredentials: true,\n\tcache: true,\n\tredirect: true,\n\treferrer: true,\n\treferrerPolicy: true,\n\tintegrity: true,\n\tkeepalive: true,\n\tsignal: true,\n\twindow: true,\n\tdispatcher: true,\n\tduplex: true,\n\tpriority: true,\n};\n"]}