{"version": 3, "file": "normalize.js", "sourceRoot": "", "sources": ["../../source/utils/normalize.ts"], "names": [], "mappings": "AAAA,OAAO,EAAC,cAAc,EAAC,MAAM,sBAAsB,CAAC;AAIpD,MAAM,CAAC,MAAM,sBAAsB,GAAG,CAAC,KAAa,EAAU,EAAE,CAC/D,cAAc,CAAC,QAAQ,CAAC,KAAmB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;AAE5E,MAAM,YAAY,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;AAE1E,MAAM,gBAAgB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AAE7D,MAAM,qBAAqB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AAE9C,MAAM,mBAAmB,GAA2B;IACnD,KAAK,EAAE,CAAC;IACR,OAAO,EAAE,YAAY;IACrB,WAAW,EAAE,gBAAgB;IAC7B,gBAAgB,EAAE,qBAAqB;IACvC,aAAa,EAAE,MAAM,CAAC,iBAAiB;IACvC,YAAY,EAAE,MAAM,CAAC,iBAAiB;IACtC,KAAK,EAAE,YAAY,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI;CAC7D,CAAC;AAEF,MAAM,CAAC,MAAM,qBAAqB,GAAG,CAAC,QAA+B,EAAE,EAA0B,EAAE;IAClG,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC/B,OAAO;YACN,GAAG,mBAAmB;YACtB,KAAK,EAAE,KAAK;SACZ,CAAC;IACH,CAAC;IAED,IAAI,KAAK,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC;QACpD,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;IACnD,CAAC;IAED,IAAI,KAAK,CAAC,WAAW,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE,CAAC;QAC5D,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;IACvD,CAAC;IAED,OAAO;QACN,GAAG,mBAAmB;QACtB,GAAG,KAAK;KACR,CAAC;AACH,CAAC,CAAC", "sourcesContent": ["import {requestMethods} from '../core/constants.js';\nimport type {HttpMethod} from '../types/options.js';\nimport type {RetryOptions} from '../types/retry.js';\n\nexport const normalizeRequestMethod = (input: string): string =>\n\trequestMethods.includes(input as HttpMethod) ? input.toUpperCase() : input;\n\nconst retryMethods = ['get', 'put', 'head', 'delete', 'options', 'trace'];\n\nconst retryStatusCodes = [408, 413, 429, 500, 502, 503, 504];\n\nconst retryAfterStatusCodes = [413, 429, 503];\n\nconst defaultRetryOptions: Required<RetryOptions> = {\n\tlimit: 2,\n\tmethods: retryMethods,\n\tstatusCodes: retryStatusCodes,\n\tafterStatusCodes: retryAfterStatusCodes,\n\tmaxRetryAfter: Number.POSITIVE_INFINITY,\n\tbackoffLimit: Number.POSITIVE_INFINITY,\n\tdelay: attemptCount => 0.3 * (2 ** (attemptCount - 1)) * 1000,\n};\n\nexport const normalizeRetryOptions = (retry: number | RetryOptions = {}): Required<RetryOptions> => {\n\tif (typeof retry === 'number') {\n\t\treturn {\n\t\t\t...defaultRetryOptions,\n\t\t\tlimit: retry,\n\t\t};\n\t}\n\n\tif (retry.methods && !Array.isArray(retry.methods)) {\n\t\tthrow new Error('retry.methods must be an array');\n\t}\n\n\tif (retry.statusCodes && !Array.isArray(retry.statusCodes)) {\n\t\tthrow new Error('retry.statusCodes must be an array');\n\t}\n\n\treturn {\n\t\t...defaultRetryOptions,\n\t\t...retry,\n\t};\n};\n"]}