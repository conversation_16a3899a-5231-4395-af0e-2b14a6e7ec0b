{"version": 3, "file": "timeout.js", "sourceRoot": "", "sources": ["../../source/utils/timeout.ts"], "names": [], "mappings": "AAAA,OAAO,EAAC,YAAY,EAAC,MAAM,2BAA2B,CAAC;AAOvD,oCAAoC;AACpC,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,OAAO,CACpC,OAAgB,EAChB,IAAiB,EACjB,eAA4C,EAC5C,OAAuB;IAEvB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACtC,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;YACjC,IAAI,eAAe,EAAE,CAAC;gBACrB,eAAe,CAAC,KAAK,EAAE,CAAC;YACzB,CAAC;YAED,MAAM,CAAC,IAAI,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC;QACnC,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;QAEpB,KAAK,OAAO;aACV,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC;aACpB,IAAI,CAAC,OAAO,CAAC;aACb,KAAK,CAAC,MAAM,CAAC;aACb,IAAI,CAAC,GAAG,EAAE;YACV,YAAY,CAAC,SAAS,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACJ,CAAC", "sourcesContent": ["import {TimeoutError} from '../errors/TimeoutError.js';\n\nexport type TimeoutOptions = {\n\ttimeout: number;\n\tfetch: typeof fetch;\n};\n\n// `Promise.race()` workaround (#91)\nexport default async function timeout(\n\trequest: Request,\n\tinit: RequestInit,\n\tabortController: AbortController | undefined,\n\toptions: TimeoutOptions,\n): Promise<Response> {\n\treturn new Promise((resolve, reject) => {\n\t\tconst timeoutId = setTimeout(() => {\n\t\t\tif (abortController) {\n\t\t\t\tabortController.abort();\n\t\t\t}\n\n\t\t\treject(new TimeoutError(request));\n\t\t}, options.timeout);\n\n\t\tvoid options\n\t\t\t.fetch(request, init)\n\t\t\t.then(resolve)\n\t\t\t.catch(reject)\n\t\t\t.then(() => {\n\t\t\t\tclearTimeout(timeoutId);\n\t\t\t});\n\t});\n}\n"]}