import type { KyHeadersInit, Options } from '../types/options.js';
import type { Hooks } from '../types/hooks.js';
export declare const validateAndMerge: (...sources: Array<Partial<Options> | undefined>) => Partial<Options>;
export declare const mergeHeaders: (source1?: KyHeadersInit, source2?: KyHeadersInit) => Headers;
export declare const mergeHooks: (original?: Hooks, incoming?: Hooks) => Required<Hooks>;
export declare const deepMerge: <T>(...sources: Array<Partial<T> | undefined>) => T;
