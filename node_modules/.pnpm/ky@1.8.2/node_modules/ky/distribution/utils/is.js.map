{"version": 3, "file": "is.js", "sourceRoot": "", "sources": ["../../source/utils/is.ts"], "names": [], "mappings": "AAAA,wDAAwD;AACxD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAC,KAAc,EAAmB,EAAE,CAAC,KAAK,KAAK,IAAI,IAAI,OAAO,KAAK,KAAK,QAAQ,CAAC", "sourcesContent": ["// eslint-disable-next-line @typescript-eslint/ban-types\nexport const isObject = (value: unknown): value is object => value !== null && typeof value === 'object';\n"]}