#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Projects/api-interceptor-extension/node_modules/.pnpm/tsup@7.2.0_@swc+core@1.13.2_@swc+helpers@0.5.17__postcss@8.5.6_typescript@5.2.2/node_modules/tsup/dist/node_modules:/Users/<USER>/Projects/api-interceptor-extension/node_modules/.pnpm/tsup@7.2.0_@swc+core@1.13.2_@swc+helpers@0.5.17__postcss@8.5.6_typescript@5.2.2/node_modules/tsup/node_modules:/Users/<USER>/Projects/api-interceptor-extension/node_modules/.pnpm/tsup@7.2.0_@swc+core@1.13.2_@swc+helpers@0.5.17__postcss@8.5.6_typescript@5.2.2/node_modules:/Users/<USER>/Projects/api-interceptor-extension/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Projects/api-interceptor-extension/node_modules/.pnpm/tsup@7.2.0_@swc+core@1.13.2_@swc+helpers@0.5.17__postcss@8.5.6_typescript@5.2.2/node_modules/tsup/dist/node_modules:/Users/<USER>/Projects/api-interceptor-extension/node_modules/.pnpm/tsup@7.2.0_@swc+core@1.13.2_@swc+helpers@0.5.17__postcss@8.5.6_typescript@5.2.2/node_modules/tsup/node_modules:/Users/<USER>/Projects/api-interceptor-extension/node_modules/.pnpm/tsup@7.2.0_@swc+core@1.13.2_@swc+helpers@0.5.17__postcss@8.5.6_typescript@5.2.2/node_modules:/Users/<USER>/Projects/api-interceptor-extension/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../dist/cli-node.js" "$@"
else
  exec node  "$basedir/../../dist/cli-node.js" "$@"
fi
