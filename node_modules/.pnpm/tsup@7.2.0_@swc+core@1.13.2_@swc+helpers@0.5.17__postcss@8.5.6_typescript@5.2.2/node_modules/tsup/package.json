{"name": "tsup", "version": "7.2.0", "description": "Bundle your TypeScript library with no config, powered by esbuild", "main": "dist/index.js", "bin": {"tsup": "dist/cli-default.js", "tsup-node": "dist/cli-node.js"}, "types": "dist/index.d.ts", "files": ["/dist", "/assets", "/schema.json"], "author": "EGOIST", "license": "MIT", "homepage": "https://tsup.egoist.dev/", "repository": {"url": "https://github.com/egoist/tsup.git"}, "scripts": {"dev": "npm run build-fast -- --watch", "build": "tsup src/cli-*.ts src/index.ts src/rollup.ts --clean --splitting", "prepublishOnly": "npm run build", "test": "npm run build && npm run test-only", "test-only": "vitest run", "build-fast": "npm run build -- --no-dts"}, "dependencies": {"bundle-require": "^4.0.0", "cac": "^6.7.12", "chokidar": "^3.5.1", "debug": "^4.3.1", "esbuild": "^0.18.2", "execa": "^5.0.0", "globby": "^11.0.3", "joycon": "^3.0.1", "postcss-load-config": "^4.0.1", "resolve-from": "^5.0.0", "rollup": "^3.2.5", "source-map": "0.8.0-beta.0", "sucrase": "^3.20.3", "tree-kill": "^1.2.2"}, "devDependencies": {"@rollup/plugin-json": "5.0.1", "@swc/core": "1.2.218", "@types/debug": "4.1.7", "@types/flat": "5.0.2", "@types/fs-extra": "9.0.13", "@types/node": "14.18.12", "@types/resolve": "1.20.1", "colorette": "2.0.16", "consola": "2.15.3", "flat": "5.0.2", "fs-extra": "10.0.0", "postcss": "8.4.12", "postcss-simple-vars": "6.0.3", "prettier": "2.5.1", "resolve": "1.20.0", "rollup-plugin-dts": "5.3.0", "rollup-plugin-hashbang": "3.0.0", "sass": "1.62.1", "strip-json-comments": "4.0.0", "svelte": "3.46.4", "svelte-preprocess": "5.0.3", "terser": "^5.16.0", "ts-essentials": "9.1.2", "tsconfig-paths": "3.12.0", "tsup": "7.1.0", "typescript": "5.0.2", "vitest": "0.28.4", "wait-for-expect": "3.0.2"}, "peerDependencies": {"@swc/core": "^1", "postcss": "^8.4.12", "typescript": ">=4.1.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}, "postcss": {"optional": true}, "@swc/core": {"optional": true}}, "engines": {"node": ">=16.14"}, "packageManager": "pnpm@8.6.0"}